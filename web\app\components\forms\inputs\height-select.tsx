import React, { useMemo, useState } from 'react';
import { Label } from '#app/components/forms/helpers/label';
import { UnitToggle } from '#app/components/forms/inputs/unit-toggle';
import { HeightUnitsEnum, UnitTypeEnum } from '#shared/constants/enums';
import { GenericSelect } from './generic-select';

const getHeightOptions = (unit: HeightUnitsEnum) => {
  const cmOptions = Array.from({ length: 151 }, (_, i) => {
    const height = 100 + i;
    return { value: height.toString(), label: `${height} cm` };
  });

  if (unit === HeightUnitsEnum.CM) {
    return cmOptions;
  } else {
    return cmOptions
      .map((option) => {
        const inHeight = Math.round(Number(option.value) * 0.393701); // Convert cm to inches
        return {
          value: inHeight.toString(),
          label: `${inHeight} in`,
        };
      })
      .filter(
        (item, index, self) =>
          self.findIndex((t) => t.label === item.label) === index,
      );
  }
};

type HeightSelectProps = Omit<
  React.ComponentProps<typeof GenericSelect>,
  'options' | 'value' | 'onChange' | 'defaultValue' | 'formValue'
> & {
  value?: number;
  onChange?: (value: number) => void;
  labelProps?: React.ComponentProps<typeof Label>;
};

const HeightSelect = ({
  value: inputValue,
  onChange: inputOnChange,
  labelProps,
  ...props
}: HeightSelectProps) => {
  const [unit, setUnit] = useState<HeightUnitsEnum>(HeightUnitsEnum.CM);
  const heightOptions = useMemo(() => getHeightOptions(unit), [unit]);

  const onChange = (value: string) => {
    if (inputOnChange) {
      inputOnChange(Number(value));
    }
  };

  const value = inputValue ? inputValue.toString() : undefined;

  return (
    <>
      <div className="mb-2 flex items-center justify-between">
        <Label {...labelProps} />
        <UnitToggle
          type={UnitTypeEnum.HEIGHT}
          value={unit}
          onChange={(value) => setUnit(value as HeightUnitsEnum)}
          name={`${props.name}Unit`}
        />
      </div>
      <GenericSelect
        options={heightOptions}
        onChange={onChange}
        value={value}
        formValue={value}
        {...props}
      />
    </>
  );
};

export { HeightSelect };
