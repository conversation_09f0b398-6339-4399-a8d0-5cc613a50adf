import { type FastifyInstance } from 'fastify';
import { t } from '#server/utils/api.server';
import { prisma } from '#server/utils/db.server';
import { handleFastifyRoute } from '#server/utils/fastify-helpers.server';

export async function blockProfileRouter(fastify: FastifyInstance) {
  fastify.post(
    '/:profileId/block',
    handleFastifyRoute(async ({ request, sendSuccessMessage }) => {
      const { profile, blockedProfile } = request;
      const existingBlock = await prisma.profileBlock.findFirst({
        where: {
          blockerProfileId: profile.id,
          blockedProfileId: blockedProfile.id,
        },
      });

      if (!existingBlock) {
        await prisma.profileBlock.create({
          data: {
            blockerProfileId: profile.id,
            blockedProfileId: blockedProfile.id,
          },
        });
      }
      sendSuccessMessage(t('Profile blocked successfully.'));
    }),
  );

  fastify.delete(
    '/:profileId/block',
    handleFastifyRoute(async ({ request, sendSuccessMessage }) => {
      const { profile, blockedProfile } = request;
      const existingBlock = await prisma.profileBlock.findFirst({
        where: {
          blockerProfileId: profile.id,
          blockedProfileId: blockedProfile.id,
        },
      });

      if (existingBlock) {
        await prisma.profileBlock.delete({ where: { id: existingBlock.id } });
      }
      sendSuccessMessage(t('Profile unblocked successfully.'));
    }),
  );

  fastify.get(
    '/:profileId/block/status',
    handleFastifyRoute(async ({ request }) => {
      const { profile, blockedProfile } = request;
      const existingBlock = await prisma.profileBlock.findFirst({
        where: {
          blockerProfileId: profile.id,
          blockedProfileId: blockedProfile.id,
        },
      });
      return { isBlocked: !!existingBlock };
    }),
  );
}
