import {
  type Conversation,
  type Message,
  type Profile,
  PhotoType,
} from '@prisma/client';
import { uploadAttachment } from '#server/utils/cloudflare.server.js';
import { prisma } from '#server/utils/db.server';
import { BadRequestError, NotFoundError } from '#server/utils/errors.server';
import {
  attachImageUrlsToPhoto,
  getOnlineStatusForProfileId,
  getProfileCardInfoFromProfileIds,
  getBlockStatus,
} from '#server/utils/profile.server.js';
import { type EnrichedConversation } from '#shared/types/conversations.js';

export async function getConversationWithMessages(
  initiator: Profile,
  receiver: Profile,
  page = 1,
  limit = 20,
) {
  const skip = (page - 1) * limit;

  const conversation = await prisma.conversation.findFirst({
    where: {
      OR: [
        {
          initiatorProfileId: initiator.id,
          receiverProfileId: receiver.id,
        },
        {
          initiatorProfileId: receiver.id,
          receiverProfileId: initiator.id,
        },
      ],
    },
  });

  if (!conversation) return null;

  const totalMessages = await prisma.message.count({
    where: {
      conversationId: conversation.id,
    },
  });

  const messages = await prisma.message.findMany({
    where: {
      conversationId: conversation.id,
    },
    include: {
      MessageAttachment: true,
    },
    orderBy: {
      createdAt: 'desc',
    },
    skip,
    take: limit,
  });

  const messagesInOrder = [...messages].reverse();

  return {
    ...conversation,
    messages: messagesInOrder,
    totalMessages,
    currentPage: page,
    hasMore: totalMessages > skip + messages.length,
  };
}

export async function getProfileByUsername({
  username,
  includeMainPhoto = false,
  includeOnlineStatus = false,
  viewerProfileId,
}: {
  username: string;
  includeMainPhoto?: boolean;
  includeOnlineStatus?: boolean;
  viewerProfileId?: string;
}) {
  const userProfile = await prisma.profile.findFirst({
    where: { username: { equals: username, mode: 'insensitive' } },
    include: {
      parentPhotos: {
        include: {
          photoVariants: true
        },
      },
    },
  });

  if (!userProfile) {
    throw new NotFoundError('Profile does not exist');
  }

  const [onlineStatus, blockStatus] = await Promise.all([
    includeOnlineStatus
      ? getOnlineStatusForProfileId(userProfile.id)
      : Promise.resolve(undefined),
    viewerProfileId
      ? getBlockStatus(viewerProfileId, userProfile.id)
      : Promise.resolve(null),
  ]);

  const mainPhoto = includeMainPhoto
    ? userProfile.parentPhotos.find((photo) => photo.photoType === PhotoType.MAIN)
    : undefined;

  const enrichedMainPhoto = mainPhoto
    ? attachImageUrlsToPhoto(mainPhoto)
    : undefined;

  return {
    ...userProfile,
    ...(includeMainPhoto ? { mainPhoto: enrichedMainPhoto } : {}),
    ...(includeOnlineStatus ? { onlineStatus } : {}),
    isBlocked: !!blockStatus,
  };
}

export async function createConversationAndMessage(
  initiator: Profile,
  receiver: Profile,
  content: string,
  subject?: string,
) {
  const isBlocked = await getBlockStatus(initiator.id, receiver.id);
  if (isBlocked) {
    throw new BadRequestError('You cannot message this user.');
  }
  // Called by `messages/:username/initiate`
  const conversation = await prisma.conversation.create({
    data: {
      subject: subject,
      initiatorProfileId: initiator.id,
      receiverProfileId: receiver.id,
      messages: {
        create: {
          content,
          senderProfileId: initiator.id,
        },
      },
    },
    include: {
      messages: {
        take: 1,
      },
    },
  });

  createMessageNotification({
    message: conversation.messages[0]!, // we know the message must exist
    receiver,
  }).catch(console.error);

  return conversation;
}

export async function addMessageToConversation(
  initiator: Profile,
  receiver: Profile,
  content: string,
  attachmentFiles?: File[],
) {
  const isBlocked = await getBlockStatus(initiator.id, receiver.id);
  if (isBlocked) {
    throw new BadRequestError('You cannot message this user.');
  }
  const existingConversation = await prisma.conversation.findFirst({
    where: {
      OR: [
        {
          initiatorProfileId: initiator.id,
          receiverProfileId: receiver.id,
        },
        {
          initiatorProfileId: receiver.id,
          receiverProfileId: initiator.id,
        },
      ],
    },
  });
  try {
    if (!existingConversation) {
      throw new NotFoundError('Conversation not found.');
    }

    const attachments = attachmentFiles
      ? await Promise.all(
          attachmentFiles.map(async (file) => {
            try {
              const key = await uploadAttachment(file);
              return {
                url: `${process.env.CLOUDFLARE_PUBLIC_URL}/${key}`,
                type: file.type,
              };
            } catch (error) {
              console.error('Error uploading file:', file.name, error);
              throw error;
            }
          }),
        )
      : [];

    const updatedConversation = await prisma.conversation.update({
      where: {
        id: existingConversation.id,
      },
      data: {
        updatedAt: new Date(),
        messages: {
          create: {
            content,
            senderProfileId: initiator.id,
          },
        },
      },
      include: {
        messages: {
          orderBy: {
            createdAt: 'desc',
          },
          take: 1,
          include: {
            MessageAttachment: true,
          },
        },
      },
    });

    // Create attachments record
    if (
      attachments &&
      attachments.length > 0 &&
      updatedConversation.messages &&
      updatedConversation.messages.length > 0
    ) {
      const attachmentsPayload = attachments.map((attachment) => ({
        ...attachment,
        messageId: updatedConversation.messages[0]!.id,
      }));

      await prisma.messageAttachment.createMany({
        data: attachmentsPayload,
      });
    }

    const [conversation] = await Promise.all([
      prisma.conversation.findUnique({
        where: {
          id: existingConversation.id,
        },
        include: {
          messages: {
            orderBy: {
              createdAt: 'desc',
            },
            include: {
              MessageAttachment: true,
            },
          },
        },
      }),
      createMessageNotification({
        message: updatedConversation.messages[0]!,
        receiver,
      }),
    ]);

    return conversation;
  } catch (error) {
    console.error('Error in addMessageToConversation:', error);
    throw error;
  }
}

export async function getConversationOverviewsForProfile(profile: Profile) {
  const conversations = await prisma.conversation.findMany({
    where: {
      OR: [
        { initiatorProfileId: profile.id },
        { receiverProfileId: profile.id },
      ],
    },
    include: {
      messages: {
        orderBy: {
          createdAt: 'desc',
        },
        take: 1,
      },
    },
    orderBy: {
      updatedAt: 'desc',
    },
  });

  const conversationOverviews = conversations.map(
    ({ messages, ...conversation }) => {
      const lastMessage = messages[0];
      return {
        lastMessage,
        ...conversation,
      };
    },
  );

  type EnrichedConversationOverview = Conversation & {
    lastMessage: Message;
  };

  function isEnrichedConversationOverview(
    conversation: any,
  ): conversation is EnrichedConversationOverview {
    return conversation.lastMessage !== undefined;
  }

  const filteredConversationOverviews: EnrichedConversationOverview[] =
    conversationOverviews.filter(isEnrichedConversationOverview);

  return filteredConversationOverviews;
}

async function createMessageNotification({
  message,
  receiver,
}: {
  message: Message;
  receiver: Profile;
}) {
  await prisma.messageNotification.create({
    data: {
      message: {
        connect: { id: message.id },
      },
      user: {
        connect: { id: receiver.userId },
      },
    },
  });
}

export async function getEnrichedConversations(profile: Profile) {
  const conversations = await getConversationOverviewsForProfile(profile);

  const profileIds = conversations
    .flatMap((conversation) => [
      conversation.initiatorProfileId,
      conversation.receiverProfileId,
    ])
    .filter((userId) => userId !== profile.id);

  const profiles = await getProfileCardInfoFromProfileIds({
    profileIds,
    viewerProfileId: profile.id,
  });

  const _enrichedConversations = conversations.map((conversation) => {
    return {
      ...conversation,
      otherProfile: profiles.find(
        (profile) =>
          profile.id === conversation.initiatorProfileId ||
          profile.id === conversation.receiverProfileId,
      ),
    };
  });

  function isEnrichedConversation(
    conversation: any,
  ): conversation is EnrichedConversation {
    return conversation.otherProfile !== undefined;
  }

  const enrichedConversations: EnrichedConversation[] =
    _enrichedConversations.filter(isEnrichedConversation);

  return enrichedConversations;
}

export async function markMessagesAsRead(
  profileId: string,
  conversationId: string,
) {
  try {
    await prisma.message.updateMany({
      where: {
        conversationId: conversationId,
        senderProfileId: { not: profileId },
        readAt: null,
      },
      data: {
        readAt: new Date(),
      },
    });

    return { success: true };
  } catch (error) {
    console.error('Error marking messages as read:', error);
    return { success: false, error };
  }
}
