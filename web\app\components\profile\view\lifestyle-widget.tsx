import { ReligionEnum, type Lifestyle } from '@prisma/client';
import { type TFunction } from 'i18next';
import { useTranslation } from 'react-i18next';
import { IoWineOutline } from 'react-icons/io5';
import { PiBabyCarriage, PiCigarette, PiHandsPraying } from 'react-icons/pi';
import { IconDetailItem } from '#app/components/profile/view';
import Typography from '#app/components/typography';
import {
  getNeverSometimesOftenLabelFromEnum,
  getReligionLabelFromEnum,
  getYesNoLabelFromEnum,
  getYesNoNotSureLabelFromEnum,
} from '#shared/utils/form-options';

type Prop = {
  lifestyle: Omit<
    Lifestyle,
    'id' | 'createdAt' | 'updatedAt' | 'profileId' | 'interests'
  > | null;
};

const LifestyleMap = (
  lifestyle: Required<Prop['lifestyle']>,
  t: TFunction,
) => ({
  drinking: lifestyle?.drinking && (
    <IconDetailItem
      label={getNeverSometimesOftenLabelFromEnum(lifestyle?.drinking, t)}
      icon={<IoWineOutline fontSize={18} />}
    />
  ),
  smoking: lifestyle?.smoking && (
    <IconDetailItem
      label={getNeverSometimesOftenLabelFromEnum(lifestyle?.smoking, t)}
      icon={<PiCigarette fontSize={18} />}
    />
  ),
  haveChildren: lifestyle?.haveChildren && (
    <IconDetailItem
      label={getYesNoLabelFromEnum(lifestyle?.haveChildren, t)}
      icon={<PiBabyCarriage fontSize={18} />}
    />
  ),
  wantChildren: lifestyle?.wantChildren && (
    <IconDetailItem
      label={getYesNoNotSureLabelFromEnum(lifestyle?.wantChildren, t)}
      icon={<PiBabyCarriage fontSize={18} />}
    />
  ),
  religion: lifestyle?.religion && (
    <IconDetailItem
      label={getReligionLabelFromEnum(lifestyle?.religion, t)}
      icon={<PiHandsPraying fontSize={18} />}
    />
  ),
});

function LifestyleWidget({ lifestyle }: Prop) {
  const { t } = useTranslation();

  const userLifestyle: Required<Prop['lifestyle']> = lifestyle ?? {
    drinking: 'NEVER',
    smoking: 'NEVER',
    haveChildren: 'NO',
    wantChildren: 'NO',
    religion: ReligionEnum.OTHER,
  };

  return (
    <div>
      <Typography variant="h5" className="font-medium mb-2 !leading-none">
        {t('Lifestyle')}
      </Typography>

      <div className="grid grid-cols-2 gap-1 w-full mx-auto">
        {Object.entries(LifestyleMap(userLifestyle, t)).map(
          ([key, value]) => value && <div key={key}>{value}</div>,
        )}
      </div>
    </div>
  );
}

export { LifestyleWidget };
