import React, { type ElementType } from 'react';
import { cn } from '#app/utils/misc';

export type Variant =
  | 'h1'
  | 'h2'
  | 'h3'
  | 'h4'
  | 'h5'
  | 'body'
  | 'small'
  | 'h6'
  | 'extra-small'
  | 'extra-large';

interface Props {
  variant?: Variant;
  children?: React.ReactNode;
  className?: string;
  as?: ElementType;
}

const sizes: Record<Variant, string> = {
  h1: 'text-[2.25rem] leading-[2.75rem] font-extrabold sm:text-[3rem] sm:leading-[3.5rem] lg:text-[3.375rem] lg:leading-[4.125rem]', // 36px -> 54px
  h2: 'text-[1.875rem] leading-[2.25rem] font-bold sm:text-[2.25rem] sm:leading-[2.75rem] lg:text-[2.25rem] lg:leading-[3rem]', // 30px -> 36px
  h3: 'text-[1.5rem] leading-[2rem] font-bold sm:text-[1.75rem] sm:leading-[2.25rem] lg:text-[2rem] lg:leading-[2.5rem]', // 24px -> 32px
  h4: 'text-[1.25rem] leading-[1.75rem] font-bold sm:text-[1.5rem] sm:leading-[2rem] lg:text-[1.5rem] lg:leading-[2.25rem]', // 20px -> 24px
  h5: 'text-[1rem] leading-[1.5rem] font-bold sm:text-[1.25rem] sm:leading-[1.75rem] lg:text-[1.25rem] lg:leading-[1.75rem]', // 16px -> 20px
  h6: 'text-[0.875rem] leading-[1.25rem] font-bold sm:text-[1rem] sm:leading-[1.5rem] lg:text-[1rem] lg:leading-[1.5rem]', // 14px -> 16px
  body: 'text-[0.875rem] leading-[1.25rem] font-regular sm:text-[1rem] sm:leading-[1.5rem] lg:text-[1rem] lg:leading-[1.5rem]', // 14px -> 16px
  small:
    'text-[0.75rem] leading-[1rem] font-medium sm:text-[0.875rem] sm:leading-[1.25rem] lg:text-[0.875rem] lg:leading-[1.25rem]', // 12px -> 14px
  'extra-small':
    'text-[0.625rem] leading-[0.875rem] font-regular sm:text-[0.75rem] sm:leading-[1rem] lg:text-[0.75rem] lg:leading-[1rem]', // 10px -> 12px
  'extra-large':
    'text-[2.25rem] leading-[2.75rem] font-extrabold sm:text-[3rem] sm:leading-[3.5rem] lg:text-[3.375rem] lg:leading-[4rem]', // 36px -> 54px
};

const getTagVariant = (variant: Variant): React.ElementType => {
  switch (variant) {
    case 'h1':
      return 'h1';
    case 'h2':
      return 'h2';
    case 'h3':
      return 'h3';
    case 'h4':
      return 'h4';
    case 'h5':
      return 'h5';
    case 'h6':
      return 'h6';
    case 'body':
    case 'extra-small':
    case 'small':
      return 'p';
    case 'extra-large':
      return 'h1'; // Optional: Use `h1` for mega styles
    default:
      return 'p';
  }
};

const Typography = ({
  variant = 'body',
  children = '',
  className = '',
  as,
}: Props) => {
  const tagVariant = getTagVariant(variant);
  const sizeClasses = sizes[variant];
  const Tag = as || tagVariant;
  return <Tag className={cn(`${sizeClasses}`, className)}>{children}</Tag>;
};

export default Typography;
