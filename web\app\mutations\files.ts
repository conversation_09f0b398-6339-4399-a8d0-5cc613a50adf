import { useMutation } from '@tanstack/react-query';
import { apiDelete } from '#app/utils/fetch.client';
import { type PhotoType } from '#shared/constants/enums.js';
import { MIN_PHOTO_DIMENSION } from '#shared/constants/media.js';
import { apiFetch } from '#shared/utils/fetch';
import { getImageDimensions } from '#shared/utils/images.js';

export function useFileDeleteMutation() {
  return useMutation({
    mutationFn: async ({
      fileId,
      type = 'image',
    }: {
      fileId: string;
      type?: 'image' | 'video';
    }) => {
      if (!fileId || fileId.trim() === '') {
        throw new Error('Invalid file ID');
      }

      const res = await apiDelete<{ message: string; fileId: string }>(
        `/api/files/${encodeURIComponent(fileId)}`,
        {
          body: JSON.stringify({ type }),
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );

      return res;
    },
  });
}

export function useFileUploadMutation() {
  return useMutation({
    mutationFn: async ({
      file,
      fileType,
      path,
      photoType,
    }: {
      file: File;
      fileType: 'image' | 'video';
      path: string;
      photoType?: PhotoType;
    }) => {
      const formData = new FormData();

      if (fileType === 'image') {
        const dimensions = await getImageDimensions(file);
        if (
          dimensions.width < MIN_PHOTO_DIMENSION ||
          dimensions.height < MIN_PHOTO_DIMENSION
        ) {
          throw new Error('Image is too small');
        }
      }

      formData.append('file', file);
      formData.append('type', fileType);
      formData.append('path', path);

      if (fileType === 'image') {
        if (!photoType) {
          throw new Error('Phototype is required for images');
        }
        formData.append('photoType', photoType);
      }

      const res = await apiFetch<{
        message: string;
        fileId: string;
        url: string;
      }>(`/api/files`, 'POST', {
        body: formData,
        headers: undefined,
      });

      return res;
    },
  });
}
