import { useLoaderData } from '@remix-run/react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { FormFromConfig } from '#app/components/forms/form-from-config';
import { ProfileLayout } from '#app/components/profile/profile-layout';
import { enrichFieldsWithDefaultValues } from '#app/utils/forms';
import {
  handleFormActionForProfile,
  handleLoaderForProfileRoute,
} from '#app/utils/remix-helpers.server';
import { prisma } from '#server/utils/db.server';
import { type FieldsFromConfig } from '#shared/types/forms';
import { type TranslationFunction } from '#shared/types/translations';
const createBioValidator = (t: TranslationFunction) => {
  return z.object({
    tagline: z.string({ message: t('Tagline must be a string') }).optional(),
    aboutMe: z.string({ message: t('About Me must be a string') }).optional(),
    occupation: z
      .string({ message: t('Occupation must be a string') })
      .optional(),
  });
};

export const loader = handleLoaderForProfileRoute(async ({ profile }) => {
  const bio = await prisma.bio.findUnique({
    where: { profileId: profile.id },
  });
  return {
    bio,
  };
});

export const action = handleFormActionForProfile(
  createBioValidator,
  async ({ profile, data, successWithMessage }) => {
    await prisma.bio.upsert({
      where: { profileId: profile.id },
      update: data,
      create: { ...data, profile: { connect: { id: profile.id } } },
    });
    return successWithMessage('Bio updated successfully');
  },
);

export default function NewProfileRoute() {
  const { bio } = useLoaderData<typeof loader>();
  const { t } = useTranslation();
  let fields: FieldsFromConfig<typeof createBioValidator> = [
    {
      name: 'tagline',
      label: t('Tagline'),
      type: 'text',
    },
    {
      name: 'aboutMe',
      label: t('About Me'),
      type: 'textarea',
    },
    {
      name: 'occupation',
      label: t('Occupation'),
      type: 'text',
    },
  ] as const;
  if (bio) {
    fields = enrichFieldsWithDefaultValues(fields, bio);
  }
  return (
    <ProfileLayout>
      <div className="form-container m-5">
        {t('Edit Bio Info')}
        <FormFromConfig
          method="POST"
          action="/profile/edit/bio"
          fields={fields}
          createValidator={createBioValidator}
        />
      </div>
    </ProfileLayout>
  );
}
