import { useLoaderData } from '@remix-run/react';
import { useTranslation } from 'react-i18next';
import { ActivityLayout } from '#app/components/activity/activity-layout';
import { ProfileResult } from '#app/components/search/profile-result';
import { useClearNotificationOfType } from '#app/contexts';
import { handleLoaderForProfileRoute } from '#app/utils/remix-helpers.server';
import { prisma } from '#server/utils/db.server';
import { getProfileCardInfoFromProfileIds } from '#server/utils/profile.server';
import { NOTIFICATION_TYPE_MAP } from '#shared/constants/profile';

export const loader = handleLoaderForProfileRoute(async ({ profile }) => {
  const profileViews = await prisma.profileView.findMany({
    where: { viewedProfileId: profile.id },
    include: {
      viewerProfile: {
        select: { id: true },
      },
    },
  });
  const profileIds = profileViews.map((view) => view.viewerProfile.id);
  const profiles = await getProfileCardInfoFromProfileIds({
    profileIds,
    viewerProfileId: profile.id,
  });
  const _enrichedProfileViews = profileViews.map((view) => ({
    ...view,
    viewerProfile: profiles.find(
      (profile) => profile.id === view.viewerProfile.id,
    ),
  }));

  // ugly types but whatever
  type EnrichedProfileView = Omit<
    (typeof _enrichedProfileViews)[number],
    'viewerProfile'
  > & {
    viewerProfile: NonNullable<
      (typeof _enrichedProfileViews)[number]['viewerProfile']
    >;
  };
  const enrichedProfileViews: EnrichedProfileView[] =
    _enrichedProfileViews.filter(
      (view): view is EnrichedProfileView => view.viewerProfile !== undefined,
    );
  enrichedProfileViews.sort((a, b) => {
    return (
      new Date(b.lastViewedTime).getTime() -
      new Date(a.lastViewedTime).getTime()
    );
  });
  return { enrichedProfileViews };
});

export default function ProfileViews() {
  const { t } = useTranslation();
  const { enrichedProfileViews } = useLoaderData<typeof loader>();
  useClearNotificationOfType(NOTIFICATION_TYPE_MAP.PROFILE_VIEW);

  return (
    <ActivityLayout>
      {enrichedProfileViews.map((profileView) => {
        const profile = profileView.viewerProfile;
        const viewTime = profileView.lastViewedTime;
        return (
          <div key={profile.id}>
            <ProfileResult
              profile={{
                ...profile,
                mainPreviewPhoto: profile.mainPhoto,
                likesThisProfile: profile.likesThisProfile ?? false,
              }}
            />
            <p>
              {t('Viewed at')}: {new Date(viewTime).toLocaleString()}
            </p>
          </div>
        );
      })}
    </ActivityLayout>
  );
}
