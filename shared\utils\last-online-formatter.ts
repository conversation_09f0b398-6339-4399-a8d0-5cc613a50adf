import { type PrivateUser } from '@prisma/client';
import { ONLINE_STATUS_MAP } from '#shared/constants/profile';
import {
  type OnlineStatusType,
  type OnlineStatusWithTime,
} from '#shared/types/profile';
import { type TranslationFunction } from '#shared/types/translations';

interface LastOnlineTimeParams {
  statusWithTime: OnlineStatusWithTime;
  userTimeZone?: string;
  privateUser?: PrivateUser;
  t?: TranslationFunction;
}

/**
 * Formats the last online time according to the specification:
 * - "x minutes ago" (< 1 hour)
 * - "x hours ago" (< 48 hours)
 * - "x days ago" (< 2 weeks)
 * - Actual date (> 2 weeks)
 * - Proper timezone and locale localization
 */
export function formatLastOnlineTime({
  statusWithTime,
  userTimeZone,
  privateUser,
  t,
}: LastOnlineTimeParams): string {
  // Default translation function that returns the key if no translation function provided
  t = t || ((key: string) => key);

  if (statusWithTime.onlineStatus === ONLINE_STATUS_MAP.ONLINE) {
    return t('Online');
  }

  if (!statusWithTime.lastOnlineTime) {
    return t('Offline');
  }

  const now = new Date();
  const lastOnline = new Date(statusWithTime.lastOnlineTime);

  // Validate that lastOnline is a valid date
  if (isNaN(lastOnline.getTime())) {
    return t('Offline');
  }

  const diffInMs = now.getTime() - lastOnline.getTime();

  // Handle future timestamps (negative difference) or invalid calculations
  if (diffInMs < 0 || isNaN(diffInMs)) {
    return t('Just now');
  }

  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  // Less than 1 hour: show minutes
  if (diffInMinutes < 60) {
    return `${diffInMinutes} ${t('minutes ago')}`;
  }

  // Less than 48 hours: show hours
  if (diffInHours < 48) {
    if (diffInHours === 1) {
      return t('1 hour ago');
    }
    return `${diffInHours} ${t('hours ago')}`;
  }

  // Less than 2 weeks: show days
  if (diffInDays < 14) {
    if (diffInDays === 1) {
      return t('1 day ago');
    }
    return `${diffInDays} ${t('days ago')}`;
  }

  // More than 2 weeks: show actual date with timezone
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    timeZone: userTimeZone || 'UTC',
  };

  const userLocale = privateUser?.locale || 'en-US';

  return lastOnline.toLocaleDateString(userLocale, options);
}

/**
 * Helper function for backward compatibility where only the status is needed
 */
export function getOnlineStatusOnly(
  statusWithTime: OnlineStatusWithTime,
): OnlineStatusType {
  return statusWithTime.onlineStatus;
}
