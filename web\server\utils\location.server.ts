import { type Location } from '@prisma/client';
import { prisma } from '#server/utils/db.server';

export async function getLocationByProfileId(profileId: string) {
  return await prisma.location.findUnique({
    where: { profileId },
  });
}

export function isInternational(location: Location | null): boolean {
  if (!location) {
    return true; // Fallback to international
  }
  return location.countryIso2 !== 'PH';
}
