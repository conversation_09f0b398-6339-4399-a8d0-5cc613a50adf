#!/bin/bash

# Function to start services
start_services() {
    echo "Starting services..."

    # Check if the container already exists
    if [ "$(docker ps -a -q -f name=filipina-meet-test-db)" ]; then
        echo "Removing existing container..."
        docker rm -f filipina-meet-test-db
    fi
        
    # Start the database service
    docker run -d --name filipina-meet-test-db \
    -e POSTGRES_USER=postgres \
    -e POSTGRES_PASSWORD=postgres \
    -e POSTGRES_DB=filipina-meet-db-test \
    -v pgdata:/var/lib/postgresql/data \
    -p 5432:5432 postgis/postgis:17-3.5
    
    # Wait for services to be ready
    echo "Waiting for services to be ready..."
    sleep 10
}

# Function to stop and clean up services
stop_services() {
    echo "Stopping services..."
    
    # Stop and remove services
    docker stop filipina-meet-test-db
    docker rm filipina-meet-test-db
}

# Check the command passed to the script
case "$1" in
    start)
        start_services
    ;;
    stop)
        stop_services
    ;;
    *)
        echo "Usage: $0 {start|stop}"
        exit 1
    ;;
esac
