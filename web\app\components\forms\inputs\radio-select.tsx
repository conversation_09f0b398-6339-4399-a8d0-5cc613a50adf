import { RadioGroup, RadioGroupItem } from '#app/components/ui/radio-group.js';
import { cn } from '#app/utils/misc.js';

interface RadioSelectProps<T extends { label: string; value: string }> {
  name: string;
  options?: T[];
  onChange: (value: T['value']) => void;
  value?: T['value'];
  showAny?: boolean; // New prop to control "Any" option visibility
  containerClassName?: string;
}

const RadioSelect = <
  T extends { label: string; value: string; disabled?: boolean },
>({
  name,
  options = [],
  onChange,
  value = 'Any',
  showAny = false,
  containerClassName,
}: RadioSelectProps<T>) => {
  const optionsWithAny = showAny
    ? [
        { label: 'Any', value: 'Any' as T['value'], disabled: false },
        ...options,
      ]
    : options;

  return (
    <RadioGroup
      value={value}
      onValueChange={onChange}
      className={cn('flex flex-col space-y-2', containerClassName)}
    >
      {optionsWithAny.map((option) => (
        <RadioGroupItem
          key={option.value}
          value={option.value}
          label={option.label}
          className={option.disabled ? 'text-gray-800' : 'text-gray-400'}
          disabled={option.disabled}
        />
      ))}
      <input type="hidden" name={name} value={value} />
    </RadioGroup>
  );
};

export { RadioSelect };
