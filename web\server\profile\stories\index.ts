import { type Profile } from '@prisma/client';
import { type FastifyInstance, type FastifyRequest } from 'fastify';
import { prisma } from '#server/utils/db.server';
import { NotFoundError } from '#server/utils/errors.server';
import { attachVideoUrlFromVideo } from '#server/utils/media.server';
import { likeStoriesRouter } from './like';

declare module 'fastify' {
  interface FastifyRequest {
    profile: Profile;
    likedVideo: ReturnType<typeof attachVideoUrlFromVideo>;
  }
}

async function checkLikedVideoMiddleware(request: FastifyRequest) {
  const { videoId } = request.params as { videoId: string };
  const video = await prisma.video.findUnique({
    where: { id: videoId },
  });
  if (!video) {
    throw new NotFoundError('Video not found');
  }
  request.likedVideo = attachVideoUrlFromVideo(video);
}

export async function storiesRouter(fastify: FastifyInstance) {
  fastify.addHook('preHandler', checkLikedVideoMiddleware);
  fastify.register(likeStoriesRouter);
}
