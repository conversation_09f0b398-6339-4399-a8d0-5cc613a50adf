import ProfileView from '#app/components/profile/profile-view';
import { handleLoaderForProfileRoute } from '#app/utils/remix-helpers.server';
import { performStoriesSearch } from '#app/utils/stories.server.js';
import { BadRequestError, ForbiddenError } from '#server/utils/errors.server';
import { attachImageUrlsToPhoto } from '#server/utils/media.server';
import {
  enrichProfileForFullProfileView,
  getProfileWithExtrasByUserId,
  getProfileWithExtrasByUsername,
  getRecommendedProfiles,
} from '#server/utils/profile.server';
import { useLoaderData } from '@remix-run/react';

export const loader = handleLoaderForProfileRoute(
  async ({ profile: myProfile, user, params: { username } }) => {
    if (!username) {
      throw new BadRequestError('No username given');
    }
    const profileWithExtras =
      username === 'me'
        ? await getProfileWithExtrasByUserId(user.id)
        : await getProfileWithExtrasByUsername(username);

    const fullProfile = await enrichProfileForFullProfileView(
      profileWithExtras,
      myProfile.id,
    );

    const stories = (await performStoriesSearch(myProfile)).at(0);

    if (fullProfile.isBlocked) {
      throw new ForbiddenError('You are not allowed to view this profile.');
    }

    // TODO: make this serializable
    const recommendedProfiles = await getRecommendedProfiles(myProfile.id);

    const galleryPhotos = fullProfile.galleryPhotos.map(attachImageUrlsToPhoto);
    return {
      myProfile,
      fullProfile,
      galleryPhotos,
      mainPhoto: fullProfile.mainPhoto,
      recommendedProfiles,
      stories,
    };
  },
);

export default function ProfilePage() {
  const { fullProfile, stories } = useLoaderData<typeof loader>();

  return (
    <ProfileView
      profile={fullProfile}
      recommendedProfiles={[]}
      stories={stories ?? []}
    />
  );
}
