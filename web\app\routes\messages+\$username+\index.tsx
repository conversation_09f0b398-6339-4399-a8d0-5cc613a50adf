import { $Enums, type Profile } from '@prisma/client';
import { useLoaderData } from '@remix-run/react';
import { z } from 'zod';
import { MessageContainer } from '#app/components/messages/message-container.js';
import { WebSocketProvider } from '#app/contexts';
import { type TranslationFunction } from '#app/types';
import { LayoutType } from '#app/types/layouts';
import {
  getConversationWithMessages,
  addMessageToConversation,
  getEnrichedConversations,
  markMessagesAsRead,
  getProfileByUsername,
} from '#app/utils/conversations.server';
import { sendFacebookNotificationMessage } from '#app/utils/facebook.server.js';
import {
  handleFormActionForProfile,
  handleLoaderForProfileRoute,
} from '#app/utils/remix-helpers.server';
import { t } from '#server/utils/api.server';
import { prisma } from '#server/utils/db.server.js';
import { BadRequestError } from '#server/utils/errors.server';
import { SocialProvider } from '#shared/constants/enums.js';
import { CONVERSATION_UPDATES_TOPIC } from '#shared/constants/facebook.js';

export const createMessageValidator = (t: TranslationFunction) => {
  return z
    .object({
      message: z.string(),
      attachments: z.array(z.any()).default([]),
      testinput: z.string().optional(),
    })
    .refine(
      (data) =>
        data.attachments.length > 0 ||
        (data.message && data.message.trim().length > 0),
      {
        message: t('Either a message or attachments are required'),
        path: ['message'],
      },
    );
};

export const handle = { layout: LayoutType.CHAT };

export const loader = handleLoaderForProfileRoute(
  async ({ profile, params, request }) => {
    const { username } = params;
    if (!username) {
      throw new BadRequestError('Invalid username');
    }

    const url = new URL(request.url);
    const page = Number.parseInt(url.searchParams.get('page') || '1');
    const limit = Number.parseInt(url.searchParams.get('limit') || '15');

    const enrichedConversations = await getEnrichedConversations(profile);
    const receiverProfile = await getProfileByUsername({
      username,
      includeMainPhoto: true,
      includeOnlineStatus: true,
      viewerProfileId: profile.id,
    });
    const senderProfile = await getProfileByUsername({
      username: profile.username,
      includeMainPhoto: true,
      includeOnlineStatus: true,
    });
    const conversationWithMessages = await getConversationWithMessages(
      profile,
      receiverProfile,
      page,
      limit,
    );

    if (conversationWithMessages && conversationWithMessages.id) {
      await markMessagesAsRead(profile.id, conversationWithMessages.id);
    }

    const token = request.headers.get('cookie');
    const BASE_URL = process.env.BASE_URL;

    return {
      profile: senderProfile,
      conversationWithMessages,
      receiverProfile,
      enrichedConversations,
      token,
      BASE_URL,
      currentPage: page,
      hasMore: conversationWithMessages?.totalMessages
        ? conversationWithMessages.messages.length <
          conversationWithMessages.totalMessages
        : false,
    };
  },
);

async function checkAndSendExternalFacebookNotification({
  receiverProfile,
  conversationId,
}: {
  receiverProfile: Profile;
  conversationId: string;
}) {
  const receiverMessageNotificationSettings =
    await prisma.messageNotificationSetting.findFirst({
      where: {
        privateUser: {
          user: {
            id: receiverProfile.userId,
          },
        },
        communicationChannel: $Enums.ExternalCommunicationChannelEnum.FACEBOOK,
      },
    });

  if (
    receiverMessageNotificationSettings &&
    receiverMessageNotificationSettings.setting !==
      $Enums.MessageNotificationSettingEnum.NEVER
  ) {
    const receiverFacebookOptIn = await prisma.facebookUserOptIn.findFirst({
      where: {
        title: CONVERSATION_UPDATES_TOPIC,
        status: receiverMessageNotificationSettings,
        socialConnection: {
          providerName: SocialProvider.FACEBOOK,
          privateUser: {
            user: {
              id: receiverProfile.userId,
            },
          },
        },
      },
    });

    const { setting } = receiverMessageNotificationSettings;

    // Can only send facebook notifications after one day has passed since last sent or
    // lastMessageSentAt is null (not yet set means no notification has been sent)
    if (receiverFacebookOptIn) {
      const canSendNotification =
        receiverFacebookOptIn.lastMessageSentAt === null
          ? true
          : Date.now() - receiverFacebookOptIn.lastMessageSentAt.getTime() >
            86400000;

      const { notificationMessageToken, socialConnectionId } =
        receiverFacebookOptIn;
      const { username } = receiverProfile;

      const conversation = await prisma.conversation.findUnique({
        where: {
          id: conversationId,
        },
        include: {
          messages: true,
        },
      });

      if (
        canSendNotification &&
        conversation &&
        conversation.messages.length === 1 &&
        setting === $Enums.MessageNotificationSettingEnum.NEW_CONVERSATION
      ) {
        await sendFacebookNotificationMessage({
          recipientNotificationMessagesToken: notificationMessageToken,
          recipientUsername: username,
          socialConnectionId: socialConnectionId,
        });
      } else if (
        canSendNotification &&
        conversation &&
        setting === $Enums.MessageNotificationSettingEnum.EVERY_MESSAGE
      ) {
        await sendFacebookNotificationMessage({
          recipientNotificationMessagesToken: notificationMessageToken,
          recipientUsername: username,
          socialConnectionId: socialConnectionId,
        });
      }
    }
  }
}

export const action = handleFormActionForProfile(
  createMessageValidator,
  async ({ profile, data, successWithMessage, params }) => {
    const { message, attachments } = data;

    const { username } = params;
    if (!username) {
      throw new BadRequestError('Invalid username');
    }
    const receiverProfile = await getProfileByUsername({
      username,
      viewerProfileId: profile.id,
    });

    if (receiverProfile.isBlocked) {
      throw new BadRequestError('Failed to send message.');
    }

    const conversation = await addMessageToConversation(
      profile,
      receiverProfile,
      message,
      attachments,
    );

    if (conversation) {
      // probably don't need to await to maintain app's chat experience
      checkAndSendExternalFacebookNotification({
        receiverProfile: receiverProfile,
        conversationId: conversation.id,
      }).catch((reason) => {
        throw new BadRequestError(reason);
      });
    }

    return successWithMessage(t('Success'));
  },
);

export default function MessagePage() {
  const {
    profile,
    conversationWithMessages,
    receiverProfile,
    enrichedConversations,
    token,
    BASE_URL,
    currentPage,
    hasMore,
  } = useLoaderData<typeof loader>();

  return (
    <WebSocketProvider
      conversationId={conversationWithMessages?.id || ''}
      conversationToken={token || ''}
      userId={profile.id}
      BASE_URL={BASE_URL || ''}
      initialMessages={conversationWithMessages?.messages || []}
      initialEnrichedConversations={enrichedConversations || []}
    >
      <MessageContainer
        conversationWithMessages={conversationWithMessages}
        profile={profile}
        receiverProfile={receiverProfile}
        currentPage={currentPage}
        hasMore={hasMore}
      />
    </WebSocketProvider>
  );
}
