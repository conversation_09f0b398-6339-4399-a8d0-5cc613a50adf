import {
  BODY_TYPE_ENUM_LIST,
  GENDER_LIST,
  NEVER_SOMETIMES_OFTEN_LIST,
  RELATIONSHIP_ENUM_LIST,
  YES_NO_LIST,
  YES_NO_NOT_SURE_LIST,
  RELIGION_ENUM_LIST,
} from '#shared/constants/profile';
import { type TranslationFunction } from '#shared/types/translations';

export function getFormOptionsGenerator<T extends string>(
  getEnumToLabel: (t: TranslationFunction) => Record<T, string>,
  enumList: readonly T[],
) {
  return (t: TranslationFunction) => {
    const EnumToLabel = getEnumToLabel(t);
    return enumList.map((option) => ({
      value: option,
      label: EnumToLabel[option],
    }));
  };
}

const getGenderToLabel = (t: TranslationFunction) =>
  ({
    MALE: t('Male'),
    FEMALE: t('Female'),
    TRANSGENDER_MALE: t('Trans Man'),
    TRANS<PERSON>NDER_FEMALE: t('Trans Woman'),
    NON_BINARY: t('Non-binary'),
  }) as const;

export const getGenderFormOptions = getFormOptionsGenerator(
  getGenderToLabel,
  GENDER_LIST,
);

const getNeverSometimesOftenToLabel = (t: TranslationFunction) =>
  ({
    NEVER: t('Never'),
    SOMETIMES: t('Sometimes'),
    OFTEN: t('Often'),
  }) as const;

export const getNeverSometimesOftenFormOptions = getFormOptionsGenerator(
  getNeverSometimesOftenToLabel,
  NEVER_SOMETIMES_OFTEN_LIST,
);

const getBodyTypeEnumToLabel = (t: TranslationFunction) =>
  ({
    PETITE: t('Petite'),
    SLIM: t('Slim'),
    ATHLETIC: t('Athletic'),
    AVERAGE: t('Average'),
    FEW_EXTRA_POUNDS: t('Few extra pounds'),
    FULL_FIGURED: t('Full figured'),
    LARGE: t('Large'),
  }) as const;

export const getBodyTypeEnumFormOptions = getFormOptionsGenerator(
  getBodyTypeEnumToLabel,
  BODY_TYPE_ENUM_LIST,
);

const getRelationshipEnumtoLabel = (t: TranslationFunction) =>
  ({
    MARRIAGE: t('Marriage'),
    SERIOUS_RELATIONSHIP: t('Serious Relationship'),
    OPEN_RELATIONSHIP: t('Open Relationship'),
    FRIENDSHIP_OR_COMPANIONSHIP: t('Friendship or Companionship'),
    CASUAL: t('Casual'),
    UNSURE: t('Unsure'),
    LARGE: t('Large'),
  }) as const;

export const getRelationshipEnumFormOptions = getFormOptionsGenerator(
  getRelationshipEnumtoLabel,
  RELATIONSHIP_ENUM_LIST,
);

const getYesNoToLabel = (t: TranslationFunction) =>
  ({
    YES: t('Yes'),
    NO: t('No'),
  }) as const;

export const getYesNoFormOptions = getFormOptionsGenerator(
  getYesNoToLabel,
  YES_NO_LIST,
);

const getYesNoNotSureToLabel = (t: TranslationFunction) =>
  ({
    YES: t('Yes'),
    NO: t('No'),
    NOT_SURE: t('Not Sure'),
  }) as const;

export const getYesNoNotSureFormOptions = getFormOptionsGenerator(
  getYesNoNotSureToLabel,
  YES_NO_NOT_SURE_LIST,
);

export const getReligionEnumToLabel = (t: TranslationFunction) =>
  ({
    CHRISTIAN: t('Christian'),
    MUSLIM: t('Muslim'),
    HINDU: t('Hindu'),
    BUDDHIST: t('Buddhist'),
    JEWISH: t('Jewish'),
    SPIRITUAL: t('Spiritual'),
    AGNOSTIC: t('Agnostic'),
    ATHEIST: t('Atheist'),
    OTHER: t('Other'),
  }) as const;

export const getReligionEnumFormOptions = getFormOptionsGenerator(
  getReligionEnumToLabel,
  RELIGION_ENUM_LIST,
);

export const getHairColorEnumToLabel = (t: TranslationFunction) =>
  ({
    BALD_SHAVEN: t('Bald/Shaven'),
    BLACK: t('Black'),
    BLONDE: t('Blonde'),
    BROWN: t('Brown'),
    GREY_WHITE: t('Grey/White'),
    LIGHT_BROWN: t('Light Brown'),
    RED: t('Red'),
    CHANGES_FREQUENTLY: t('Changes Frequently'),
  }) as const;

export const getEyeColorEnumToLabel = (t: TranslationFunction) =>
  ({
    BLACK: t('Black'),
    BLUE: t('Blue'),
    BROWN: t('Brown'),
    GREEN: t('Green'),
    GREY: t('Grey'),
    HAZEL: t('Hazel'),
  }) as const;

export const getEthnicityEnumToLabel = (t: TranslationFunction) => ({
  MIDDLE_EASTERN: t('Middle Eastern'),
  EAST_ASIAN: t('East Asian'),
  SOUTH_ASIAN: t('South Asian'),
  SOUTHEAST_ASIAN: t('Southeast Asian'),
  FILIPINO: t('Filipino'),
  BLACK: t('Black'),
  WHITE: t('White'),
  HISPANIC: t('Hispanic'),
  PACIFIC_ISLANDER: t('Pacific Islander'),
  OTHER: t('Other'),
});

export function getLabelFromEnum<T extends string>(
  enumValue: T,
  getEnumToLabel: (t: TranslationFunction) => Record<T, string>,
  t: TranslationFunction,
) {
  const EnumToLabel = getEnumToLabel(t);
  return EnumToLabel[enumValue];
}

export function createLabelFromEnumFunction<T extends string>(
  getEnumToLabel: (t: TranslationFunction) => Record<T, string>,
) {
  return (enumValue: T, t: TranslationFunction) => {
    return getLabelFromEnum(enumValue, getEnumToLabel, t);
  };
}

export const getReligionLabelFromEnum = createLabelFromEnumFunction(
  getReligionEnumToLabel,
);

export const getYesNoNotSureLabelFromEnum = createLabelFromEnumFunction(
  getYesNoNotSureToLabel,
);

export const getYesNoLabelFromEnum =
  createLabelFromEnumFunction(getYesNoToLabel);

export const getNeverSometimesOftenLabelFromEnum = createLabelFromEnumFunction(
  getNeverSometimesOftenToLabel,
);

export const getBodyTypeLabelFromEnum = createLabelFromEnumFunction(
  getBodyTypeEnumToLabel,
);

export const getHairColorLabelFromEnum = createLabelFromEnumFunction(
  getHairColorEnumToLabel,
);

export const getEyeColorLabelFromEnum = createLabelFromEnumFunction(
  getEyeColorEnumToLabel,
);
export const getEthnicityLabelFromEnum = createLabelFromEnumFunction(
  getEthnicityEnumToLabel,
);

export const getRelationshipLabelFromEnum = createLabelFromEnumFunction(
  getRelationshipEnumtoLabel,
);
