import { Link } from '@remix-run/react';
import { buttonVariants, type But<PERSON> } from '#app/components/ui/button';
import { cn } from '#app/utils/misc.js';

interface LinkButtonProps extends React.ComponentProps<typeof Button> {
  to: string;
}

const LinkButton: React.FC<LinkButtonProps> = ({
  to,
  children,
  className,
  variant,
  size,
}) => {
  return (
    <Link to={to} className={cn(buttonVariants({ variant, size }), className)}>
      {children}
    </Link>
  );
};

export { LinkButton };
