import PhoneInput, { type PhoneInputProps } from 'react-phone-input-2';
import { cn } from '#app/utils/misc.js';
import 'react-phone-input-2/lib/style.css';

interface PhoneNumberProps extends PhoneInputProps {
  'aria-invalid'?: boolean;
  name?: string;
  required?: boolean;
}

export const PhoneNumber = ({
  country = 'ph',
  inputClass,
  buttonClass,
  inputProps,
  name,
  required,
  placeholder,
  ...rest
}: PhoneNumberProps) => {
  const ariaInvalid = !!rest?.['aria-invalid'];
  return (
    <PhoneInput
      {...rest}
      defaultMask="-"
      country={country}
      buttonClass={cn(
        '!rounded-tl-xl !rounded-bl-xl !border-input !aspect-square',
        '[&_.selected-flag]:!w-full [&_.selected-flag]:!px-3 [&_.selected-flag]:!px-3 [&_.selected-flag:hover]:!bg-transparent',
        buttonClass,
        ariaInvalid ? '!border-input-invalid' : '',
      )}
      inputClass={cn(
        'flex !h-12 !w-full !rounded-xl !pl-[60px] border !border-input bg-transparent px-4 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-input aria-[invalid]:border-input-invalid',
        inputClass,
        ariaInvalid ? '!border-input-invalid' : '',
      )}
      inputProps={{
        ...inputProps,
        name,
        required,
        placeholder,
      }}
    />
  );
};
