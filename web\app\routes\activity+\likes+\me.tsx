import { useLoaderData } from '@remix-run/react';
import { useTranslation } from 'react-i18next';
import { ActivityLayout } from '#app/components/activity/activity-layout';
import { ProfileResult } from '#app/components/search/profile-result';
import { useClearNotificationOfType } from '#app/contexts';
import { getLikesCounts } from '#app/utils/count.server.js';
import { handleLoaderForProfileRoute } from '#app/utils/remix-helpers.server';
import { prisma } from '#server/utils/db.server';
import { getProfileCardInfoFromProfileIds } from '#server/utils/profile.server';
import { NOTIFICATION_TYPE_MAP } from '#shared/constants/profile';

export const loader = handleLoaderForProfileRoute(async ({ profile }) => {
  const [myLikes, likesMe, mutualLikes] = await getLikesCounts(profile.id);

  const profilesWhoLikeMe = await prisma.profileLike.findMany({
    where: { likedProfileId: profile.id },
    include: { likerProfile: { select: { id: true } } },
  });

  const profileIds = profilesWhoLikeMe.map((p) => p.likerProfile.id);
  const profiles = await getProfileCardInfoFromProfileIds({
    profileIds,
    viewerProfileId: profile.id,
  });

  return {
    profiles,
    counts: { myLikes, likesMe, mutualLikes },
  };
});

export default function LikesMe() {
  useTranslation();
  const { profiles, counts } = useLoaderData<typeof loader>();
  useClearNotificationOfType(NOTIFICATION_TYPE_MAP.PROFILE_LIKE);
  return (
    <ActivityLayout counts={counts}>
      {profiles.map((profile) => {
        return (
          <ProfileResult
            key={profile.id}
            profile={{
              ...profile,
              mainPreviewPhoto: profile.mainPhoto,
              likesThisProfile: profile.likesThisProfile ?? false,
            }}
          />
        );
      })}
    </ActivityLayout>
  );
}
