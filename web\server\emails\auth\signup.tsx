import * as E from '@react-email/components';

// TODO: put in more things to welcome people to FilipinaMeet
function SignupEmail({ verificationUrl }: { verificationUrl: string }) {
  return (
    <E.Html lang="en" dir="ltr">
      <E.Container>
        <h1>
          <E.Text>Verify Your Email Address</E.Text>
        </h1>
        <p>
          <E.Text>Click the button below to verify your email address:</E.Text>
        </p>
        <E.Button href={verificationUrl} style={{ color: '#61dafb' }}>
          Verify Email
        </E.Button>
      </E.Container>
    </E.Html>
  );
}

export { SignupEmail };
