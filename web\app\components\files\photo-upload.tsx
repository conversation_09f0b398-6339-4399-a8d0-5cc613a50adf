import { Loader2Icon } from 'lucide-react';
import { useCallback, useState } from 'react';
import { toast as showToast } from 'sonner';
import { Input } from '#app/components/forms/inputs/input';
import PlusIcon from '#app/components/icons/PlusIcon';
import { Button } from '#app/components/ui/button';
import {
  useFileDeleteMutation,
  useFileUploadMutation,
} from '#app/mutations/files';
import { cn } from '#app/utils/misc';
import { isImageLarge } from '#app/utils/photo';
import { type PhotoType } from '#shared/constants/enums.js';
import XIcon from '../icons/XIcon';

type PhotoUploadProps = {
  imageSize?: 'medium' | 'large';
  onUpload: (fileId: string) => void;
  onDelete: (fileId: string) => void;
  value?: string;
  imageUrl?: string;
  path: string;
  photoType: PhotoType;
};

const sizeClasses = {
  medium: 'h-32 w-32', // Medium size classes
  large: 'h-64 w-64', // Large size classes
};

export const PhotoUpload = ({
  imageSize = 'medium',
  path,
  onUpload,
  onDelete,
  value,
  imageUrl,
  photoType,
  ...props
}: PhotoUploadProps) => {
  const { mutate: uploadPhoto, isPending: isUploading } =
    useFileUploadMutation();
  const { mutate: deletePhoto, isPending: isDeleting } =
    useFileDeleteMutation();
  const [photo, setPhoto] = useState<string | null>(imageUrl || null);
  const [fileId, setFileId] = useState<string | null>(value || null);
  // Temporary state for tracking delete process
  const [isDeleteInProgress, setIsDeleteInProgress] = useState(false);

  const clearFileInput = useCallback(() => {
    // Validate fileId before attempting to delete
    if (!fileId) {
      console.error('No file ID to delete');
      return;
    }

    // Start animation immediately
    setIsDeleteInProgress(true);

    deletePhoto(
      { fileId: fileId, type: 'image' },
      {
        onSuccess: (data) => {
          setPhoto(null);
          setFileId(null);
          onDelete(data.fileId);
          setIsDeleteInProgress(false);
        },
        onError: (error) => {
          console.error('Error deleting photo:', error);
          setIsDeleteInProgress(false);
        },
      },
    );
  }, [fileId, onDelete, deletePhoto]);

  const handlePhotoChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    uploadPhoto(
      { file, fileType: 'image', path, photoType },
      {
        onSuccess(data) {
          setPhoto(data.url);
          setFileId(data.fileId);
          onUpload(data.fileId);
        },
        onError(error) {
          showToast.error(error.message);
        },
      },
    );
  };

  const inputField = (
    <Input
      type="file"
      accept="image/*"
      className={`inset-0 h-full w-full cursor-pointer ${isUploading ? 'hidden' : ''}`}
      onChange={handlePhotoChange}
      {...props}
    />
  );

  // Use both the mutation's isPending state and our local state
  const showLoadingSpinner = isUploading || isDeleting || isDeleteInProgress;

  return (
    <div
      className={`relative overflow-hidden rounded-3xl ${sizeClasses[imageSize]}`}
    >
      {showLoadingSpinner ? (
        <div className="absolute inset-0 left-0 top-0 z-10 flex h-full w-full items-center justify-center bg-black opacity-50">
          <Loader2Icon className="h-12 w-12 animate-spin text-white" />
        </div>
      ) : null}
      {photo ? (
        <>
          <img
            src={photo}
            alt="Uploaded photo"
            className="w-full h-full object-cover"
          />
          <button
            type="button"
            onClick={clearFileInput}
            className="absolute top-2 right-2"
            disabled={showLoadingSpinner}
            aria-label="Delete photo"
          >
            <div
              className={cn(
                'rounded-lg bg-red-100 hover:bg-red-300 flex items-center justify-center group transition-colors duration-200',
                isImageLarge(imageSize) ? 'h-8 w-8' : 'h-6 w-6',
              )}
            >
              <XIcon
                className={cn(
                  'text-red-500 group-hover:text-red-100 transition-colors duration-200',
                  isImageLarge(imageSize) ? 'w-2.5 h-2.5' : 'w-2 h-2',
                )}
              />
            </div>
          </button>
          {inputField} {/* Keep the input field always present but hidden */}
        </>
      ) : (
        <div className="relative flex h-full w-full items-center justify-center bg-neutral-100">
          <div
            className={cn(
              'bg-[#F6D7DA] rounded-lg flex items-center justify-center',
              isImageLarge(imageSize) ? 'w-14 h-12' : 'w-10 h-9',
            )}
          >
            <PlusIcon
              className={cn(
                'text-[#D33544]',
                isImageLarge(imageSize) ? 'w-5 h-5' : 'w-4 h-4',
              )}
            />
          </div>
          <Button
            asChild
            size="icon"
            variant="ghost"
            className="absolute inset-0 cursor-pointer opacity-0"
            disabled={showLoadingSpinner}
          >
            {inputField}
          </Button>
        </div>
      )}
    </div>
  );
};
