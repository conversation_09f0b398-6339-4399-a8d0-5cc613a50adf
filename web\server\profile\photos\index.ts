import { type FastifyInstance } from 'fastify';
import { z } from 'zod';
import { t } from '#server/utils/api.server';
import { prisma } from '#server/utils/db.server';
import { NotFoundError } from '#server/utils/errors.server';
import { handleFastifyRoute } from '#server/utils/fastify-helpers.server';
import { deletePhotoFromCloudandDB } from '#server/utils/image.server.js';

const deletePhotoParamsSchema = z.object({
  photoId: z.string({ message: t('Must be string') }),
});

// POST method handled by Remix forms
export async function photoRouter(fastify: FastifyInstance) {
  fastify.delete(
    '/:photoId',
    handleFastifyRoute(async ({ request, sendSuccessMessage }) => {
      const { photoId } = deletePhotoParamsSchema.parse(request.params);

      // make sure to check the profile id as well
      // Search and delete child photos as well
      const photo = await prisma.parentPhoto.findUnique({
        where: { fileId: photoId, profileId: request.profile.id },
      });
      if (!photo) {
        throw new NotFoundError(t('Photo not found'));
      }

      await deletePhotoFromCloudandDB({
        parentPhotoId: photoId,
      });

      sendSuccessMessage(t('Photo deleted successfully'));
    }),
  );
}
