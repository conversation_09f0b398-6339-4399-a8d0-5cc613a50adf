export enum GenderEnum {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
  TRANSGENDER_MALE = 'TRANSGENDER_MALE',
  TRANSGENDER_FEMALE = 'TRANSGENDER_FEMALE',
  NON_BINARY = 'NON_BINARY',
}

export enum PhotoType {
  MAIN = 'MAIN',
  VERIFICATION = 'VERIFICATION',
  GALLERY = 'GALLERY',
}

export enum VideoType {
  MAIN = 'MAIN',
  GALLERY = 'GALLERY',
  STORY = 'STORY',
}

export enum VerificationStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

export enum HairColorEnum {
  BALD_SHAVEN = 'BALD_SHAVEN',
  BLACK = 'BLACK',
  BLONDE = 'BLONDE',
  BROWN = 'BROWN',
  GREY_WHITE = 'GREY_WHITE',
  LIGHT_BROWN = 'LIGHT_BROWN',
  RED = 'RED',
  CHANGES_FREQUENTLY = 'CHANGES_FREQUENTLY',
}

export enum EyeColorEnum {
  BLACK = 'BLACK',
  BLUE = 'BLUE',
  BROWN = 'BROWN',
  GREEN = 'GREEN',
  GREY = 'GREY',
  HAZEL = 'HAZEL',
}

export enum BodyTypeEnum {
  PETITE = 'PETITE',
  SLIM = 'SLIM',
  ATHLETIC = 'ATHLETIC',
  AVERAGE = 'AVERAGE',
  FEW_EXTRA_POUNDS = 'FEW_EXTRA_POUNDS',
  FULL_FIGURED = 'FULL_FIGURED',
  LARGE = 'LARGE',
}

export enum EthnicityEnum {
  MIDDLE_EASTERN = 'MIDDLE_EASTERN',
  EAST_ASIAN = 'EAST_ASIAN',
  SOUTH_ASIAN = 'SOUTH_ASIAN',
  SOUTHEAST_ASIAN = 'SOUTHEAST_ASIAN',
  FILIPINO = 'FILIPINO',
  BLACK = 'BLACK',
  WHITE = 'WHITE',
  HISPANIC = 'HISPANIC',
  PACIFIC_ISLANDER = 'PACIFIC_ISLANDER',
  OTHER = 'OTHER',
}

export enum NeverSometimesOften {
  NEVER = 'NEVER',
  SOMETIMES = 'SOMETIMES',
  OFTEN = 'OFTEN',
}

export enum YesNo {
  YES = 'YES',
  NO = 'NO',
}

export enum YesNoNotSure {
  YES = 'YES',
  NO = 'NO',
  NOT_SURE = 'NOT_SURE',
}

export enum ReligionEnum {
  CHRISTIAN = 'CHRISTIAN',
  MUSLIM = 'MUSLIM',
  HINDU = 'HINDU',
  BUDDHIST = 'BUDDHIST',
  JEWISH = 'JEWISH',
  SPIRITUAL = 'SPIRITUAL',
  AGNOSTIC = 'AGNOSTIC',
  ATHEIST = 'ATHEIST',
  OTHER = 'OTHER',
}

export enum RelationshipEnum {
  MARRIAGE = 'MARRIAGE',
  SERIOUS_RELATIONSHIP = 'SERIOUS_RELATIONSHIP',
  OPEN_RELATIONSHIP = 'OPEN_RELATIONSHIP',
  FRIENDSHIP_OR_COMPANIONSHIP = 'FRIENDSHIP_OR_COMPANIONSHIP',
  CASUAL = 'CASUAL',
  UNSURE = 'UNSURE',
}

export enum SocialProvider {
  FACEBOOK = 'FACEBOOK',
  GOOGLE = 'GOOGLE',
}

export enum UserType {
  USER = 'USER',
  ADMIN = 'ADMIN',
}

export enum WeightUnitsEnum {
  KG = 'kg',
  LB = 'lb',
}

export enum HeightUnitsEnum {
  CM = 'cm',
  IN = 'in',
}

export enum UnitTypeEnum {
  WEIGHT = 'weight',
  HEIGHT = 'height',
}

export enum ExternalCommunicationChannelEnum {
  EMAIL = 'EMAIL',
  SMS = 'SMS',
}

export enum MessageNotificationSettingEnum {
  EVERY_MESSAGE = 'EVERY_MESSAGE',
  NEW_CONVERSATION = 'NEW_CONVERSATION',
  NEVER = 'NEVER',
}

export enum ImageSizeEnum {
  IMAGE_LARGE = 'large',
}

export enum ProfileCategoryEnum {
  RECOMMENDED = "recommended",
  NEW_PROFILES = "new_profiles",
  MOST_LIKED = "most_liked",
  ONLINE_NOW = "online_now"
}

export enum FacebookUserOptInStatus {
  RESUME_NOTIFICATIONS = "RESUME_NOTIFICATIONS",
  STOP_NOTIFICATIONS = "STOP_NOTIFICATIONS"
}
