// there are more fields but only using these
export interface JsonCountry {
	name: string
	iso2: string
}

export interface JsonRegion {
	name: string
	state_code: string
}

export interface JsonCountryWithRegions extends JsonCountry {
	states: JsonRegion[]
}

export interface ApiCountry {
	name: string
	iso2: string
}

export interface ApiRegion {
	name: string
	regionCode: string
	countryIso2: string
}

export interface GoogleGeocode {
	results: Array<{
		address_components: Array<{
			long_name: string
			short_name: string
			types: string[]
		}>
		formatted_address: string
		geometry: {
			bounds: {
				northeast: {
					lat: number
					lng: number
				}
				southwest: {
					lat: number
					lng: number
				}
			}
			location: {
				lat: number
				lng: number
			}
			location_type: string
			viewport: {
				northeast: {
					lat: number
					lng: number
				}
				southwest: {
					lat: number
					lng: number
				}
			}
		}
		partial_match?: boolean
		place_id: string
		types: string[]
	}>
	status: string
}
