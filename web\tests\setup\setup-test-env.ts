import 'dotenv/config';
import '#app/utils/env.server';
// we need these to be imported first 👆

import { cleanup } from '@testing-library/react';
import { afterEach, beforeEach, vi, type MockInstance } from 'vitest';
import { server } from '#tests/mocks/index';
import './custom-matchers';
import { prisma } from '../../server/utils/db.server';

afterEach(() => server.resetHandlers());
afterEach(async () => {
  // Clean up data after each test
  cleanup();
  await prisma.user.deleteMany();
});

export let consoleError: MockInstance<(typeof console)['error']>;

beforeEach(() => {
  const originalConsoleError = console.error;
  consoleError = vi.spyOn(console, 'error');
  consoleError.mockImplementation(
    (...args: Parameters<typeof console.error>) => {
      originalConsoleError(...args);
      throw new Error(
        'Console error was called. Call consoleError.mockImplementation(() => {}) if this is expected.',
      );
    },
  );
});
