import { Link, useLocation } from '@remix-run/react';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import Typography from '#app/components/typography';
import { navigationItems } from '#app/constants/navigation';
import { useNotificationContext } from '#app/contexts';
import MainLogoSVG from '#app/images/svg-icons/logos/main.svg?react';
import WordLogoSVG from '#app/images/svg-icons/logos/word.svg?react';
import { type RootUser, type RootProfile } from '#app/types/serialized-types';
import { cn } from '#app/utils/misc';
import { ACTIVITY_LIKES_ME_ROUTE } from '#shared/constants/routes';
import { Notification } from './notification';
import { ProfileThumbnail } from './profille-thumbnail';

interface DummyHeaderProps {
  user: RootUser;
  profile: RootProfile;
}

const Header: React.FC<DummyHeaderProps> = ({ profile }) => {
  const { t } = useTranslation();
  const { refetchNotifications } = useNotificationContext();
  const location = useLocation();

  useEffect(() => {
    if (profile) {
      refetchNotifications();
    }
  }, [profile, refetchNotifications]);

  return (
    <header className="flex flex-col gap-2 py-4 tablet:px-0 bg-white shadow-gray-100/50 shadow-lg">
      <div className="flex items-center justify-between gap-4 px-4 py-2">
        <Link to="/" className="flex items-center">
          <MainLogoSVG className="tablet:h-9" />
          <WordLogoSVG className="tablet:h-5 tablet:w-full" />
        </Link>
        <div className="gap-8 tablet-min:flex hidden items-center">
          {navigationItems.map((item) => {
            const isActive =
              item.path === ACTIVITY_LIKES_ME_ROUTE
                ? location.pathname.startsWith('/activity/likes')
                : location.pathname === item.path;

            return (
              <Link
                key={item.label}
                to={item.path}
                className="flex flex-col items-center justify-center"
              >
                <div className="relative">
                  <item.icon
                    className={cn(isActive ? 'text-brand-primary' : '')}
                    fontSize={24}
                  />
                </div>
                <Typography
                  variant="small"
                  className={cn(
                    `${
                      isActive
                        ? 'bg-gradient-to-r from-brand-secondary to-brand-primary bg-clip-text text-transparent'
                        : ''
                    }`,
                    'font-medium',
                  )}
                >
                  {t(item.label)}
                </Typography>
              </Link>
            );
          })}

          <Notification />
        </div>
        <ProfileThumbnail profile={profile} />
      </div>
    </header>
  );
};

export { Header };
