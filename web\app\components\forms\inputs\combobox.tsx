import {
  Combobox as HeadlessCombobox,
  ComboboxButton,
  ComboboxInput,
  ComboboxOption,
  ComboboxOptions,
} from '@headlessui/react';
import { CheckIcon, ChevronUpDownIcon } from '@heroicons/react/20/solid';
import { useState, useMemo } from 'react';
import { cn } from '#app/utils/misc';

export type ComboboxOption<T extends string = string> = {
  value: T;
  label: string;
  disabled?: boolean;
};

type ComboboxProps<T extends string = string> = {
  options: ComboboxOption<T>[];
  value?: T;
  onChange: (value: T) => void;
  placeholder?: string;
  name?: string;
  disabled?: boolean;
  error?: string;
  'aria-label'?: string;
  'aria-describedby'?: string;
};

export function Combobox<T extends string = string>({
  options,
  value,
  onChange,
  placeholder = 'Select an option',
  name,
  disabled = false,
  error,
  'aria-label': ariaLabel,
  'aria-describedby': ariaDescribedBy,
}: ComboboxProps<T>) {
  const [query, setQuery] = useState('');

  const selectedOption = useMemo(
    () => options.find((option) => option.value === value) ?? null,
    [options, value],
  );

  const filteredOptions = useMemo(() => {
    if (query === '') return options;

    const lowerQuery = query.toLowerCase();
    return options.filter((option) =>
      option.label.toLowerCase().includes(lowerQuery),
    );
  }, [options, query]);

  return (
    <HeadlessCombobox
      as="div"
      value={selectedOption}
      onChange={(option: ComboboxOption<T> | null) => {
        if (option && !option.disabled) {
          onChange(option.value);
        }
      }}
      disabled={disabled}
      className="relative"
    >
      <div className="relative">
        <input type="hidden" name={name} value={value || ''} />
        <ComboboxInput
          className={cn(
            'w-full rounded-md border bg-white py-2 pl-3 pr-10 text-left shadow-sm sm:text-sm',
            'focus:outline-none focus:ring-1',
            error
              ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
              : 'border-gray-300 focus:border-indigo-500 focus:ring-indigo-500',
            disabled && 'cursor-not-allowed opacity-60 bg-gray-50',
          )}
          onChange={(event) => setQuery(event.target.value)}
          onBlur={() => setQuery('')}
          displayValue={(option: ComboboxOption<T> | null) =>
            option?.label || ''
          }
          placeholder={placeholder}
          aria-label={ariaLabel}
          aria-describedby={ariaDescribedBy}
          aria-invalid={!!error}
        />
        <ComboboxButton className="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none">
          <ChevronUpDownIcon
            className={cn(
              'h-5 w-5',
              disabled ? 'text-gray-300' : 'text-gray-400',
            )}
            aria-hidden="true"
          />
        </ComboboxButton>
      </div>

      <ComboboxOptions
        anchor={{ to: 'top', gap: '0.25rem' }}
        className="w-[var(--input-width)] max-h-60 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm empty:hidden"
      >
        {filteredOptions.map((option) => (
          <ComboboxOption
            key={option.value}
            value={option}
            disabled={option.disabled}
            className={cn(
              'group relative cursor-default select-none py-2 pl-3 pr-9',
              'text-gray-900 data-[focus]:bg-indigo-600 data-[focus]:text-white',
              'data-[disabled]:cursor-not-allowed data-[disabled]:opacity-50',
            )}
          >
            {({ selected }) => (
              <>
                <span
                  className={cn('block truncate', selected && 'font-semibold')}
                >
                  {option.label}
                </span>

                {selected && (
                  <span className="absolute inset-y-0 right-0 flex items-center pr-4 text-indigo-600 group-data-[focus]:text-white">
                    <CheckIcon className="h-5 w-5" aria-hidden="true" />
                  </span>
                )}
              </>
            )}
          </ComboboxOption>
        ))}
      </ComboboxOptions>

      {error && (
        <p className="mt-2 text-sm text-red-600" id={ariaDescribedBy}>
          {error}
        </p>
      )}
    </HeadlessCombobox>
  );
}
