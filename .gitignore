node_modules
.DS_store
.idea

/web/build
/web/public/build
/web/server-build
.env
.cache

/web/tests/prisma
/web/vite.config.ts.timestamp*

/test-results/
/web/test-results/
/web/playwright-report/
/web/playwright/.cache/
/web/tests/fixtures/email/
/web/coverage
/mobile/__snapshots__/

# Easy way to create temporary files/folders that won't accidentally be added to git
*.local.*

# generated files
/web/app/components/ui/icons

tsconfig.tsbuildinfo

dist
bun.lock
