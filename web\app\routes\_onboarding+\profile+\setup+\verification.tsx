import { $Enums } from '@prisma/client';
import {
  useFetcher,
  useFetchers,
  useLoaderData,
  useNavigate,
} from '@remix-run/react';
import { useTranslation } from 'react-i18next';
import { FaArrowLeftLong } from 'react-icons/fa6';
import { z } from 'zod';
import {
  FormFromConfig,
  FormType,
} from '#app/components/forms/form-from-config.js';
import { Button } from '#app/components/ui/button.js';
import photoVerifyImage from '#app/images/photo-verify.webp';
import { LayoutType } from '#app/types/layouts.js';
import { cn } from '#app/utils/misc.js';
import {
  handleFormActionForProfile,
  handleLoaderForProfileRoute,
} from '#app/utils/remix-helpers.server.js';
import { prisma } from '#server/utils/db.server.js';
import { BadRequestError } from '#server/utils/errors.server.js';
import {
  deletePhotoFromCloudandDB,
  transformImageAndUploadToR2AndDatabase,
} from '#server/utils/image.server.js';
import { attachImageUrlsToPhoto } from '#server/utils/media.server.js';
import { PROFILE_SETUP_PHOTOS_ROUTE } from '#shared/constants/routes.js';
import { type FieldsFromConfig } from '#shared/types/forms.js';
import { type TranslationFunction } from '#shared/types/translations.js';
import { createPhotoValidator } from '#shared/utils/validators.js';

const nextRoute = PROFILE_SETUP_PHOTOS_ROUTE;

export const handle = { layout: LayoutType.NONE };

const createVerificationPhotoValidator = (t: TranslationFunction) =>
  z.object({
    photo: createPhotoValidator(t),
  });

export const loader = handleLoaderForProfileRoute(async ({ profile }) => {
  const verificationPhoto = await prisma.parentPhoto.findFirst({
    where: {
      photoType: $Enums.PhotoType.VERIFICATION,
      profileId: profile.id,
    },
    include: {
      photoVariants: true,
    },
  });

  return {
    initialVerificationPhoto:
      verificationPhoto !== null
        ? attachImageUrlsToPhoto(verificationPhoto)
        : undefined,
  };
});

export const action = handleFormActionForProfile(
  createVerificationPhotoValidator,
  async ({ redirect, data, request, profile }) => {
    const { photo } = data;

    const method = request.method;

    if (method === 'POST') {
      const existingPhoto = await prisma.parentPhoto.findFirst({
        where: {
          profileId: profile.id,
          photoType: $Enums.PhotoType.VERIFICATION,
        },
      });

      if (!existingPhoto) {
        const arrayBuffer: ArrayBuffer = await photo.arrayBuffer();
        if (arrayBuffer.byteLength === 0) {
          throw new BadRequestError('No verification photo uploaded');
        }

        await transformImageAndUploadToR2AndDatabase({
          file: photo,
          photoType: $Enums.PhotoType.VERIFICATION,
          profileId: profile.id,
        });
      }
    }

    if (method === 'DELETE') {
      await deletePhotoFromCloudandDB({
        parentPhotoId: photo,
      });
      return null;
    }

    return redirect(nextRoute);
  },
);

export default function SetupVerificationRoute() {
  const { initialVerificationPhoto } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const fetchers = useFetchers();
  const fetcher = useFetcher();

  function handleClickBack() {
    if (window.history.length > 1) {
      window.history.back();
    }
  }

  function handleClickSkip() {
    console.log('skipping');
    navigate(nextRoute);
  }

  const isFetching = fetchers.length !== 0 && fetchers[0]?.state !== 'idle';

  const fields: FieldsFromConfig<typeof createVerificationPhotoValidator> = [
    {
      name: 'photo',
      type: 'photo-upload',
      imageSize: 'large',
      defaultImageUrl: initialVerificationPhoto?.largeUrl,
      onDelete: () => {
        if (!initialVerificationPhoto?.id) return;
        const formData = new FormData();
        formData.append('photo', initialVerificationPhoto.id);
        fetcher.submit(formData, {
          method: 'DELETE',
          action: '/profile/setup/verification',
        });
      },
      showLoadingSpinner: isFetching,
      disableDelete: isFetching,
    },
  ];

  return (
    <>
      <FormFromConfig
        method="POST"
        action="/profile/setup/verification"
        encType="multipart/form-data"
        formType={FormType.FETCHER}
        fields={fields}
        createValidator={createVerificationPhotoValidator}
        renderFields={(fields) => {
          return (
            <div>
              <div className="mb-6 md:mb-8">
                <h1
                  className={cn(
                    'text-3xl md:text-4xl font-extrabold text-gray-900',
                    'dark:text-white md:tracking-tight',
                  )}
                >
                  {t('Keep it real with a quick snap')}
                </h1>
                <p className="mt-1 text-sm text-gray-500">
                  {t(
                    'Match the pose in a photo to confirm your identity and show you’re the real deal',
                  )}
                </p>
              </div>
              <div className="flex gap-10">
                <img
                  src={photoVerifyImage}
                  alt="Pose to match"
                  className="mb-4"
                />
                {fields.fields.photo.node}
              </div>
              <div className="flex justify-between mt-auto pt-8">
                <Button
                  onClick={handleClickBack}
                  className="text-black"
                  variant="link"
                  disabled={isFetching}
                >
                  <FaArrowLeftLong className="mr-2" />
                  Back
                </Button>
                <button
                  type="submit"
                  className={cn(
                    'w-[20%] rounded-lg bg-[#D33544] py-3 text-center font-medium text-white',
                    'transition-colors hover:bg-[#B82C3A] focus:outline-none focus:ring-2',
                    'focus:ring-[#D33544] focus:ring-offset-2',
                  )}
                  disabled={isFetching}
                >
                  Next
                </button>
              </div>
              <div className="flex justify-end">
                <Button
                  onClick={handleClickSkip}
                  variant="link"
                  className="w-[20%]"
                  type="button"
                  disabled={isFetching}
                >
                  Skip for now
                </Button>
              </div>
            </div>
          );
        }}
      />
    </>
  );
}
