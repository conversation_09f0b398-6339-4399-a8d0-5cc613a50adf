import { prisma } from '#server/utils/db.server';

export async function getLikesCounts(profileId: string) {
  const myLikesCountPromise = prisma.profileLike.count({
    where: { likerProfileId: profileId }
  });

  const likesMeCountPromise = prisma.profileLike.count({
    where: { likedProfileId: profileId }
  });

  const mutualLikesCountPromise = prisma.profileLike.count({
    where: {
      likerProfileId: profileId,
      likedProfile: {
        profileLikesOfOthers: {
          some: {
            likedProfileId: profileId
          }
        }
      }
    }
  });

  return Promise.all([myLikesCountPromise, likesMeCountPromise, mutualLikesCountPromise]);
}