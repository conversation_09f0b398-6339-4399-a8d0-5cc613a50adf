{
  "extends": "./tsconfig.json",
  "include": [
    "**/*.ts",
    "**/*.tsx",
    "**/*.json",
    "./shared/**/*.ts",
    "./data/**/*.json"
  ],
  // This is super messy and we need better structure so we
  // don't have to exclude things file by file
  "exclude": [
    "mobile/**/*",
    "node_modules/**/*",
    "web/node_modules/**/*",
    "web/app/**/*",
    "web/tailwind.config.ts",
    "web/tests/**/*"
  ],
  "compilerOptions": {
    "baseUrl": ".",
    "isolatedModules": true,
    "moduleDetection": "force",
    "esModuleInterop": true,
    "skipLibCheck": true,
    "noEmit": false,
    "noEmitOnError": false,
    "outDir": "./web/server-build",
    "allowImportingTsExtensions": false,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "plugins": [
      {
        "transform": "typescript-transform-paths",
        "afterDeclarations": true,
        "useExtensions": ".js"
      }
    ],
    "paths": {
      "#app/*": ["./web/app/*"],
      "#shared/*": ["./shared/*"],
      "#tests/*": ["./web/tests/*"],
      "#server/*": ["./web/server/*"],
      "#root/*": ["./web/*"],
      "#data/*": ["./data/*"],
      "@/icon-name": [
        "./web/app/components/ui/icons/name.d.ts",
        "./web/types/icon-name.d.ts"
      ]
    }
  }
}
