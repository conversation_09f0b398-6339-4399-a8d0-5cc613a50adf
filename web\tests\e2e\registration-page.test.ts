import { faker } from '@faker-js/faker';
import { expect, test } from '#tests/playwright-utils';

test('Test registration page', async ({ page }) => {
  await page.goto('/auth/register');

  const emailInput = page.getByLabel('Email');
  await emailInput.click();

  const passwordInput = page.getByLabel('Password', { exact: true });
  await passwordInput.click();

  const submitButton = page.getByRole('button', { name: 'Create account' });

  await expect(emailInput).toBeVisible();
  await expect(passwordInput).toBeVisible();
  await expect(submitButton).toBeVisible();
});

test('Create account', async ({ page }) => {
  await page.goto('/auth/register');

  const email = faker.internet.email();
  const password = faker.internet.password();

  await page.getByLabel('Email').click();
  await page.getByLabel('Email').fill(email);
  await page.getByLabel('Email').press('Tab');
  await page.locator('button[title="toggle-password"]').first().press('Tab');

  await page.getByLabel('Password', { exact: true }).fill(password);
  await page.getByLabel('Password', { exact: true }).press('Tab');
  await page.locator('button[title="toggle-password"]').last().press('Tab');

  await page.getByLabel('Confirm Password').fill(password);
  await page.getByRole('button', { name: 'Create account' }).click();

  await expect(page).toHaveURL('/profile/new');
});
