// REACT
import * as React from 'react';

// UTILS
import { cn } from '#app/utils/misc';

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  inputRef?: React.Ref<HTMLInputElement>;
}

const Input = ({ className, type, inputRef, ...props }: InputProps) => {
  return (
    <input
      type={type}
      className={cn(
        'flex h-12 w-full rounded-xl border border-gray-300 bg-transparent',
        'px-4 py-2 text-sm outline-none',
        'transition-colors duration-100 ease-in-out',
        'focus:border-2 focus:border-transparent focus:ring-0 focus:gradient-border',
        className,
      )}
      ref={inputRef}
      {...props}
    />
  );
};

Input.displayName = 'Input';

export { Input };
