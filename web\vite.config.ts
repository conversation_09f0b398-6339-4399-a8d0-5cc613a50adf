import dns from 'node:dns';
import path from 'node:path';
import { vitePlugin as remix } from '@remix-run/dev';
import { installGlobals } from '@remix-run/node';
import { sentryVitePlugin } from '@sentry/vite-plugin';
import { glob } from 'glob';
import { flatRoutes } from 'remix-flat-routes';
import { defineConfig } from 'vite';
import svgr from 'vite-plugin-svgr';

dns.setDefaultResultOrder('verbatim');

installGlobals({ nativeFetch: true });

// Load .env from the root directory

const MODE = process.env.NODE_ENV;

// Declare unstable_singleFetch for the future flag in Remix
declare module '@remix-run/server-runtime' {
  interface Future {
    v3_singleFetch: true;
  }
}

export default defineConfig({
  assetsInclude: ['**/*.webp', '#app/**/*.webp'],
  resolve: {
    alias: {
      '.prisma/client/index-browser': path.resolve(
        __dirname,
        '../node_modules/.prisma/client/index-browser.js',
      ),
      '#app': path.resolve(__dirname, 'app'),
      '#shared': path.resolve(__dirname, '../shared'),
      '#data': path.resolve(__dirname, '../data'),
      '#server': path.resolve(__dirname, 'server'),
      '#other': path.resolve(__dirname, 'other'),
    },
  },
  build: {
    cssMinify: MODE === 'production',
    rollupOptions: {
      external: [/node:.*/, 'stream', 'crypto', 'fsevents'],
    },
    assetsInlineLimit: (source) => {
      if (source.endsWith('sprite.svg')) {
        return false;
      }
    },
    sourcemap: true,
  },
  server: {
    middlewareMode: true, // Enable middleware mode
    watch: {
      ignored: ['**/playwright-report/**'],
    },
  },
  plugins: [
    remix({
      ignoredRouteFiles: ['**/*'],
      future: {
        v3_singleFetch: true,
        v3_fetcherPersist: true,
        v3_relativeSplatPath: true,
        v3_throwAbortReason: true,
      },
      serverModuleFormat: 'esm',
      routes: async (defineRoutes) => {
        return flatRoutes('routes', defineRoutes, {
          ignoredRouteFiles: [
            '.*',
            '**/*.css',
            '**/*.test.{js,jsx,ts,tsx}',
            '**/__*.*',
            '**/*.server.*',
            '**/*.client.*',
          ],
        });
      },
    }),
    process.env.SENTRY_AUTH_TOKEN
      ? sentryVitePlugin({
          disable: MODE !== 'production',
          authToken: process.env.SENTRY_AUTH_TOKEN,
          org: process.env.SENTRY_ORG,
          project: process.env.SENTRY_PROJECT,
          release: {
            name: process.env.COMMIT_SHA,
            setCommits: {
              auto: true,
            },
          },
          sourcemaps: {
            filesToDeleteAfterUpload: await glob([
              './build/**/*.map',
              '.server-build/**/*.map',
            ]),
          },
        })
      : null,
    svgr({
      svgrOptions: {
        svgo: true,
        exportType: 'default',
      },
      include: ['**/*.svg?react'],
    }),
  ],
});
