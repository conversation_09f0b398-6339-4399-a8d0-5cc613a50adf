import { MapPin } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { type SerializedFullProfile } from '#app/types/serialized-types';
import Typography from '../../typography';

type RecommendedProfile = Pick<
  SerializedFullProfile,
  'id' | 'firstName' | 'mainPhoto' | 'emailIsVerified' | 'age' | 'location'
>;

type Prop = {
  recommendedProfiles: RecommendedProfile[];
};

const RecommendedProfilesMap = (recommendedProfiles: RecommendedProfile[]) => {
  return recommendedProfiles.map((profile) => {
    return (
      <div key={profile.id}>
        <div className="flex items-center gap-2">
          <img
            src={profile.mainPhoto?.smallUrl}
            alt={profile.firstName}
            className="w-10 h-10 rounded-full"
          />
          <div className="flex flex-col gap-1">
            <Typography variant="h6">
              {profile.firstName}, {profile.age}
            </Typography>
            <div className="flex items-center gap-1">
              <MapPin className="w-4 h-4 text-red-600" />
              <Typography variant="body">
                {profile.location?.cityOrTown}
              </Typography>
            </div>
          </div>
        </div>
      </div>
    );
  });
};

const RecommendedProfilesWidget = ({ recommendedProfiles }: Prop) => {
  const { t } = useTranslation();

  const userRecommendedProfiles =
    recommendedProfiles.length > 0
      ? recommendedProfiles
      : [
          {
            id: '1',
            firstName: 'John',
            mainPhoto: {
              smallUrl:
                'https://www.gravatar.com/avatar/94d093eda664addd6e450d7e9881bcad?s=40&d=identicon&r=PG',
            },
            age: 25,
            location: {
              cityOrTown: 'Cebu, PH',
            },
          },
        ];

  return (
    <div className="flex flex-col gap-4 w-full">
      <Typography variant="h5" className="font-medium mb-2 !leading-none">
        {t('See more profiles')}
      </Typography>
      {/* horizontal rule */}
      <div className="w-full h-[1px] bg-gray-200" />
      <div className="">
        {/* @ts-expect-error TODO: fix this */}
        {RecommendedProfilesMap(userRecommendedProfiles)}
      </div>
    </div>
  );
};

export { RecommendedProfilesWidget };
