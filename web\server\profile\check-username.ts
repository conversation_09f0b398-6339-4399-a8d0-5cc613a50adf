import { type FastifyInstance } from 'fastify';
import { z } from 'zod';
import { t } from '#server/utils/api.server';
import { prisma } from '#server/utils/db.server';
import { ZodValidationError } from '#server/utils/errors.server';
import { handleFastifyRoute } from '#server/utils/fastify-helpers.server';
import { createUsernameValidator } from '#shared/utils/validators.js';

const usernameValidator = z.object({ username: createUsernameValidator(t) });

// currently not used
export async function checkUsernameRoute(fastify: FastifyInstance) {
  fastify.post(
    '/check-username',
    handleFastifyRoute(async ({ request, reply }) => {
      // Validate the request body
      const result = usernameValidator.safeParse(request.body);
      if (!result.success) {
        throw new ZodValidationError(result.error);
      }
      const { username } = result.data;
      // TODO: will need to add check if user re-uses their own username
      const existingUser = await prisma.profile.findUnique({
        where: { username },
      });
      // TODO: follow standard response format
      if (existingUser) {
        return await reply.send({ available: false, username });
      } else {
        return await reply.send({ available: true, username });
      }
    }),
  );
}
