import {
  type ActionFunction,
  type LoaderFunction,
  redirect as remixRedirect,
  json as remixJson,
} from '@remix-run/node';
import { type z, type SafeParseReturnType } from 'zod';
import { checkHoneypot } from '#app/utils/honeypot.server';
import { verifySessionStorage } from '#app/utils/verification.server';
import {
  type <PERSON>Hand<PERSON>,
  type ActionFormHandlerForLoggedInUser,
  type ActionFormHandlerForProfile,
  type LoaderHandlerForLoggedInUser,
  type LoaderForProfileRoute,
  type ActionHandlerForLoggedInUser,
  type CustomJson,
  type CustomRedirect,
  type LoaderHandler,
} from '#server/types/remix-helpers';
import { t } from '#server/utils/api.server';
import {
  RaisedError,
  ValidationError,
  ZodValidationError,
  NoUserProfileError,
  UserNotAdminError,
  UserNotLoggedInError,
} from '#server/utils/errors.server';
import {
  DEFAULT_REDIRECT_ROUTE,
  PROFILE_NEW_ROUTE,
  LOGIN_ROUTE,
} from '#shared/constants/routes';
import { type TranslationFunction } from '#shared/types/translations';
import { type ZodValidator } from '#shared/types/validators';
import { requireUser } from './remix-auth.server';

// eventually we can swap these out for our own functions
const customJson = remixJson;
const customRedirect = remixRedirect;

/**
 * Helpers
 */

function handleActionErrors({
  error,
  json,
  redirect,
}: {
  error: unknown;
  json: CustomJson;
  redirect: CustomRedirect;
}) {
  // send to Sentry
  console.log(error);
  // TODO: simplify
  if (error instanceof RaisedError) {
    if (error instanceof ValidationError) {
      return json(
        {
          error: error.message,
          errorType: error.name,
          errorDetail: error.errorDetail,
          success: false,
        },
        { status: error.statusCode },
      );
    } else if (error instanceof UserNotAdminError) {
      return redirect(DEFAULT_REDIRECT_ROUTE);
    }
    return json(
      {
        error: error.message,
        errorType: error.name,
        success: false,
      },
      { status: error.statusCode },
    );
  }
  return json(
    {
      error: 'Internal Server Error',
      errorType: 'InternalServerError',
      success: false,
    },
    { status: 500 },
  );
}

async function handleLoaderErrors({
  error,
  redirect,
}: {
  error: unknown;
  redirect: CustomRedirect;
}) {
  // send to Sentry
  console.log(error);
  // errors that we catch should only redirect
  if (error instanceof UserNotLoggedInError) {
    const urlParams = new URLSearchParams({ next: error.attemptedUrl });
    const urlToRedirect = LOGIN_ROUTE + '?' + urlParams.toString();
    return redirect(urlToRedirect, {
      headers: {
        'set-cookie': await verifySessionStorage.destroySession(
          error.authSession,
        ),
      },
    });
  }
  if (error instanceof UserNotAdminError) {
    return redirect(DEFAULT_REDIRECT_ROUTE);
  }
  if (error instanceof NoUserProfileError) {
    return redirect(PROFILE_NEW_ROUTE);
  }
  throw error;
}

// Loaders

export function handleLoader<TLoaderFunction extends LoaderFunction>(
  handler: LoaderHandler<TLoaderFunction>,
) {
  const redirect = remixRedirect;
  return async ({
    request,
    params,
    context,
  }: Parameters<LoaderFunction>[0]) => {
    try {
      return await handler({ request, params, context, redirect });
    } catch (error) {
      return handleLoaderErrors({ error, redirect });
    }
  };
}

export function handleLoaderForLoggedInUser<
  TLoaderFunction extends LoaderFunction,
  TLoaderFunctionReturn,
>(
  handler: LoaderHandlerForLoggedInUser<TLoaderFunction, TLoaderFunctionReturn>,
) {
  const redirect = remixRedirect;
  return async ({
    request,
    params,
    context,
  }: Parameters<LoaderFunction>[0]) => {
    try {
      const { user, privateUser, profile } = await requireUser({
        request,
        redirect,
        includeProfile: true,
      });
      return await handler({
        request,
        params,
        context,
        redirect,
        user,
        privateUser,
        profile,
      });
    } catch (error) {
      return handleLoaderErrors({ error, redirect });
    }
  };
}

export function handleLoaderForProfileRoute<
  TLoaderFunction extends LoaderFunction,
  TLoaderFunctionReturn extends ReturnType<TLoaderFunction>,
>(handler?: LoaderForProfileRoute<TLoaderFunction, TLoaderFunctionReturn>) {
  const redirect = remixRedirect;
  return async ({
    request,
    params,
    context,
  }: Parameters<LoaderFunction>[0]) => {
    try {
      const { user, privateUser, profile } = await requireUser({
        request,
        redirect,
        includeProfile: true,
      });
      if (!profile) {
        throw new NoUserProfileError();
      }
      // TODO: better way of doing optional handler
      const localHandler: LoaderForProfileRoute<
        TLoaderFunction,
        TLoaderFunctionReturn
      > = handler ?? ((() => ({})) as any);
      return await localHandler({
        request,
        params,
        context,
        redirect,
        user,
        profile,
        privateUser,
      });
    } catch (error) {
      return handleLoaderErrors({ error, redirect });
    }
  };
}

// admin functionaity
export function handleLoaderForAdmin<
  TLoaderFunction extends LoaderFunction,
  TLoaderFunctionReturn,
>(
  handler?: LoaderHandlerForLoggedInUser<
    TLoaderFunction,
    TLoaderFunctionReturn
  >,
) {
  const redirect = remixRedirect;
  return async ({
    request,
    params,
    context,
  }: Parameters<LoaderFunction>[0]) => {
    try {
      const { user, privateUser } = await requireUser({
        request,
        redirect,
        requireAdmin: true,
      });
      // TODO: do better typing
      const localHandler: LoaderHandlerForLoggedInUser<
        TLoaderFunction,
        TLoaderFunctionReturn
      > = handler ?? ((() => ({})) as any);
      return await localHandler({
        request,
        params,
        user,
        privateUser,
        context,
        redirect,
      });
    } catch (error) {
      return handleLoaderErrors({ error, redirect });
    }
  };
}

/**
 * Actions
 */

/**
 * This wraps an action function with error handling and redirects.
 * The other purpose of this is to make it easier to convert to the upcoming
 * defineAction function as we will only have to update this one function
 */

export function handleAction<TActionFunction extends ActionFunction>(
  handler?: ActionHandler<TActionFunction>,
) {
  const redirect = customRedirect;
  const json = customJson;
  return async ({
    request,
    params,
    context,
  }: Parameters<ActionFunction>[0]) => {
    try {
      const localHandler: ActionHandler<TActionFunction> =
        handler ?? ((() => ({})) as any);
      return await localHandler({ request, params, context, redirect, json });
    } catch (error) {
      return handleActionErrors({ error, json, redirect });
    }
  };
}

export function handleActionForLoggedInUser<
  TActionFunction extends ActionFunction,
  TActionFunctionReturn,
>(
  handler: ActionHandlerForLoggedInUser<TActionFunction, TActionFunctionReturn>,
  options?: {
    requireAdmin?: boolean;
  },
) {
  const redirect = remixRedirect;
  const json = customJson;
  const successWithMessage = (
    message: string,
    options?: {
      delayeRedirectTo?: string;
    },
  ) => {
    return json({ success: true, message, options });
  };
  return async ({
    request,
    params,
    context,
  }: Parameters<ActionFunction>[0]) => {
    try {
      const { user, privateUser, profile } = await requireUser({
        request,
        redirect,
        includeProfile: true,
        requireAdmin: options?.requireAdmin ?? false,
      });
      return await handler({
        request,
        params,
        context,
        redirect,
        user,
        privateUser,
        profile,
        json,
        successWithMessage,
      });
    } catch (error) {
      // maybe we want to pass in user here if we have it
      return handleActionErrors({ error, json, redirect });
    }
  };
}

// used for form actions that requrire a logged in user
export function handleFormActionForLoggedInUser<
  TActionFunction extends ActionFunction,
  TZodValidator extends ZodValidator,
  TActionFunctionReturn,
>(
  createValidator: (t: TranslationFunction) => TZodValidator,
  handler: ActionFormHandlerForLoggedInUser<
    TActionFunction,
    TZodValidator,
    TActionFunctionReturn
  >,
) {
  return handleActionForLoggedInUser(async (args) => {
    const formData = await args.request.formData();
    checkHoneypot(formData);
    const formDataObj = Object.fromEntries(formData.entries());
    const result = createValidator(t).safeParse(formDataObj);

    if (!result.success) {
      throw new ZodValidationError(result.error);
    }
    return await handler({ ...args, data: result.data });
  });
}

// used for profile updating routes
export function handleFormActionForProfile<
  TActionFunction extends ActionFunction,
  TZodValidator extends ZodValidator,
  TActionFunctionReturn,
>(
  createValidator: (t: TranslationFunction) => TZodValidator,
  handler: ActionFormHandlerForProfile<
    TActionFunction,
    TZodValidator,
    TActionFunctionReturn
  >,
) {
  return handleActionForLoggedInUser(async ({ profile, ...args }) => {
    if (!profile) {
      throw new NoUserProfileError();
    }
    const formData = await args.request.formData();
    checkHoneypot(formData);

    // Debug form entries
    console.log('Debugging form entries:');
    for (const [key, value] of formData.entries()) {
      console.log({
        key,
        valueType: typeof value,
        isFile: value instanceof File,
        constructor: value?.constructor?.name,
        properties:
          value instanceof File
            ? {
                name: value.name,
                type: value.type,
                size: value.size,
              }
            : Object.getOwnPropertyNames(value),
      });
    }

    // Process form data
    const formDataObj: Record<string, unknown> = {};

    // First pass: collect all entries
    for (const [key, value] of formData.entries()) {
      // Handle file fields (both single and multiple)
      if (
        value instanceof File ||
        (typeof value === 'object' && value !== null)
      ) {
        const files = formData
          .getAll(key)
          .map((val) => val)
          .filter((file): file is File => file !== null);

        // For 'attachments', always keep as array
        // For other file fields (like 'photo'), use single file if only one
        formDataObj[key] =
          key === 'attachments' ? files : files.length === 1 ? files[0] : files;
      } else {
        // Handle non-file fields
        formDataObj[key] = value;
      }
    }

    // Log processed form data
    console.log('Processed form data:', {
      ...formDataObj,
      // Log file fields
      ...Object.fromEntries(
        Object.entries(formDataObj)
          .filter(([_, v]) => v instanceof File || Array.isArray(v))
          .map(([k, v]) => [
            k,
            Array.isArray(v)
              ? v.map((f) => ({ name: f.name, type: f.type, size: f.size }))
              : v instanceof File
                ? { name: v.name, type: v.type, size: v.size }
                : v,
          ]),
      ),
    });

    const result = createValidator(t).safeParse(formDataObj);

    if (!result.success) {
      console.error('Validation failed:', result.error);
      throw new ZodValidationError(result.error);
    }

    return await handler({ profile, ...args, data: result.data });
  });
}

export function handleFormActionForAdmin<
  TActionFunction extends ActionFunction,
  TZodValidator extends ZodValidator,
  TActionFunctionReturn,
>(
  createValidator: (t: TranslationFunction) => TZodValidator,
  handler: ActionFormHandlerForLoggedInUser<
    TActionFunction,
    TZodValidator,
    TActionFunctionReturn
  >,
) {
  return handleActionForLoggedInUser(
    async (args) => {
      const formData = await args.request.formData();
      checkHoneypot(formData);
      const formDataObj = Object.fromEntries(formData.entries());
      const result = createValidator(t).safeParse(formDataObj);

      if (!result.success) {
        throw new ZodValidationError(result.error);
      }
      return await handler({ ...args, data: result.data });
    },
    {
      requireAdmin: true,
    },
  );
}

/**
 * Parses form data from a request and validates it using a provided Zod validator.
 */
export async function parseFormData<TZodValidator extends ZodValidator>(
  zodValidator: (t: TranslationFunction) => TZodValidator,
  request: Request,
): Promise<
  SafeParseReturnType<z.input<TZodValidator>, z.output<TZodValidator>>
> {
  const formData = await request.formData();
  checkHoneypot(formData);
  const formDataObj = Object.fromEntries(formData.entries());
  const result = zodValidator(t).safeParse(formDataObj);
  return result;
}


export interface LikesCounts {
  myLikes: number;
  likesMe: number;
  mutualLikes: number;
}