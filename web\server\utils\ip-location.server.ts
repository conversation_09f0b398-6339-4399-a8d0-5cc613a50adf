import { type PrivateUser } from '@prisma/client';
import { z } from 'zod';
import { ONLINE_TIME_THRESHOLD_SECONDS } from '#shared/constants/profile.js';
import { apiGet } from '#shared/utils/fetch.js';
import { cacheGet, cacheSet } from './cache.server';
import { IpLocationError, IpLocationApiError } from './errors.server';


const IpApiResponseSchema = z.object({
  ip: z.string(),
  city: z.string(),
  region_name: z.string(),
  country_name: z.string(),
  country_code: z.string().length(2), // ISO2 country code
  latitude: z.number(),
  longitude: z.number(),
  region_code: z.string(),
});

type IpApiResponse = z.infer<typeof IpApiResponseSchema>;

type IpApiErrorResponse = {
  error: {
    info?: string;
    message?: string;
  };
  success?: boolean;
};

type IpApiRawResponse = IpApiResponse | IpApiErrorResponse;

type CachedLocationData = {
  country: string;
  countryCode: string;
  region: string;
  regionCode: string;
  city: string;
  latitude: number;
  longitude: number;
};

type CachedLocationResult = 
  | { status: 'success'; data: CachedLocationData }
  | { status: 'error'; message: string };


export async function getIpLocationData(ip: string, privateUserId: string): Promise<CachedLocationData | null> {
  const cacheKey = `${privateUserId}:${ip}`;
  
  try {
    const cachedData = await cacheGet(cacheKey);
    
    if (cachedData) {
      const parsedData = JSON.parse(cachedData) as CachedLocationResult;
      if (parsedData.status === 'success') {
        return parsedData.data;
      }
      // If it's an error status, return null to indicate no location data
      return null;
    }
    
    const accessKey = process.env.IPAPI_ACCESS_KEY;
    if (!accessKey) {
      throw new IpLocationError('IPAPI_ACCESS_KEY environment variable is not set');
    }
    
    const url = `https://api.ipapi.com/api/${ip}?access_key=${accessKey}`;
    
    const rawData = await apiGet<IpApiRawResponse>(url, {
      handleError: async (response: Response) => {
        const errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        
        if (response.status >= 400 && response.status < 500) {
          throw new IpLocationApiError(`API request failed: ${errorMessage}`);
        }
        
        throw new IpLocationError(`Server error: ${errorMessage}`);
      }
    });
    
    if ('error' in rawData) {
      const error = rawData.error;
      throw new IpLocationApiError(`IP API error: ${error.info || error.message || 'Unknown error'}`);
    }

    if ('success' in rawData && !rawData.success) {
      throw new IpLocationApiError('IP API request was unsuccessful');
    }
    
    const validationResult = IpApiResponseSchema.safeParse(rawData);
    if (!validationResult.success) {
      throw new IpLocationApiError(`Invalid API response format: ${validationResult.error.message}`);
    }
    
    const data = validationResult.data;
    const locationData = createLocationDataFromApiResponse(data);

    const cacheResult: CachedLocationResult = {
      status: 'success',
      data: locationData
    };

    await cacheSet(cacheKey, JSON.stringify(cacheResult), ONLINE_TIME_THRESHOLD_SECONDS);
    
    return locationData;
  } catch (error) {
    const cacheResult: CachedLocationResult = {
      status: 'error',
      message: error instanceof Error ? error.message : 'Unknown error'
    };
    await cacheSet(cacheKey, JSON.stringify(cacheResult), ONLINE_TIME_THRESHOLD_SECONDS);
    
    // If it's already one of our specific error types, re-throw it as-is
    if (error instanceof IpLocationError || error instanceof IpLocationApiError) {
      throw error;
    }
    
    console.error('Unexpected error fetching IP location data:', error);
    throw new IpLocationError(`Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function getClientIpAddress(request: Request): Promise<string | null> {
  try {
    // Check for Fly.io client IP (production)
    const flyClientIp = request.headers.get('fly-client-ip');
    if (flyClientIp) {
      return flyClientIp;
    }

    // Check for forwarded headers
    const forwardedFor = request.headers.get('x-forwarded-for');
    if (forwardedFor) {
      const firstIp = forwardedFor.split(',')[0];
      const ip = firstIp ? firstIp.trim() : null;
      if (ip) {
        return ip;
      }
    }

    // For local development
    if (process.env.NODE_ENV === 'development') {
      try {
        const response = await fetch('https://api.ipify.org?format=json');
        const data = await response.json();
        return data.ip;
      } catch {
        return null;
      }
    }
    return null;
  } catch {
    return null;
  }
}

export async function cacheLocationDataForUser(
  privateUser: PrivateUser, 
  locationData: Partial<CachedLocationData>
): Promise<void> {
  const cacheKey = `user_location:${privateUser.id}`;
  const cacheResult: CachedLocationResult = {
    status: 'success',
    data: locationData as CachedLocationData
  };
  await cacheSet(cacheKey, JSON.stringify(cacheResult), ONLINE_TIME_THRESHOLD_SECONDS);
}

export async function getCachedLocationDataForUser(
  privateUser: PrivateUser
): Promise<Partial<CachedLocationData> | null> {
  const cacheKey = `user_location:${privateUser.id}`;
  const cachedData = await cacheGet(cacheKey);
  
  if (cachedData) {
    try {
      const parsedData = JSON.parse(cachedData) as CachedLocationResult;    
      if (parsedData.status === 'success') {
        return parsedData.data;
      }
      // If it's an error status, return null to indicate no location data
      console.warn('Returning null due to cached error status:', parsedData.message);
      return null;
    } catch (error) {
      console.warn('Failed to parse cached location data:', error);
      return null;
    }
  }
  
  return null;
}

function createLocationDataFromApiResponse(data: IpApiResponse): CachedLocationData {
  return {
    country: data.country_name,
    countryCode: data.country_code,
    region: data.region_name,
    regionCode: data.region_code,
    city: data.city,
    latitude: data.latitude,
    longitude: data.longitude,
  };
}

function createLocationData(
  locationData: Partial<CachedLocationData>,
  city?: string
): Partial<CachedLocationData> {
  return {
    country: locationData.country,
    countryCode: locationData.countryCode,
    region: locationData.region,
    regionCode: locationData.regionCode,
    city: city ?? locationData.city,
    latitude: locationData.latitude,
    longitude: locationData.longitude,
  };
}

export async function validateAndCleanLocationData(
  locationData: Partial<CachedLocationData>
): Promise<Partial<CachedLocationData>> {
  
  const { getGeocodeData } = await import('./geography');
  
  const addressParts = [
    locationData.city,
    locationData.region,
    locationData.country,
  ].filter(Boolean);
  
  if (addressParts.length < 2) {
    return createLocationData(locationData);
  }
  
  const combinedAddress = addressParts.join(', ');
  
  try {
    const geocodeResponse = await getGeocodeData(combinedAddress);
    
    if (geocodeResponse.status === 'OK' && geocodeResponse.results && geocodeResponse.results.length > 0) {
      const location = geocodeResponse.results[0];
      
      if (!location) {
        return createLocationData(locationData);
      }
      
      if (location.partial_match) {
        return createLocationData(locationData);
      }
      
      const cityComponent = location.address_components.find((component) =>
        component.types.includes('locality') || 
        component.types.includes('administrative_area_level_2') ||
        component.types.includes('sublocality')
      );
      
      const validatedCity = cityComponent ? cityComponent.long_name : '';
      
      return createLocationData(locationData, validatedCity);
    }
    
    // If validation fails, return data without city
    return createLocationData(locationData);
  } catch (error) {
    console.error('Geocoding validation failed:', error);
    return createLocationData(locationData);
  }
} 