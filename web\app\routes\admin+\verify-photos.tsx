import { useLoaderData } from '@remix-run/react';
import { useState } from 'react';
import VerificationPhoto from '#app/components/admin/verification-photo';
import { handleLoaderForAdmin } from '#app/utils/remix-helpers.server';
import { prisma } from '#server/utils/db.server';
import { attachImageUrlsToPhoto } from '#server/utils/media.server';
import { VerificationStatus } from '#shared/constants/enums';

export const loader = handleLoaderForAdmin(async ({}) => {
  let pendingPhotos = await prisma.parentPhoto.findMany({
    where: { verificationStatus: VerificationStatus.PENDING },
    include: { 
      profile: true,
      photoVariants: true, 
    },
  });

  const signedPendingPhotos = pendingPhotos.map((pendingPhoto) => { 
    return {
      pendingPhoto: attachImageUrlsToPhoto(pendingPhoto),
      profile: pendingPhoto.profile,
    };
  });
  return { signedPendingPhotos };
});

function PendingPhotos() {
  const { signedPendingPhotos: initialPendingPhotos } =
    useLoaderData<typeof loader>();
  const [pendingPhotos, setPendingPhotos] = useState(initialPendingPhotos);

  const handleSuccess = (photoId: string) => {
    setPendingPhotos((prevPhotos) =>
      prevPhotos.filter((photo) => photo.pendingPhoto.id !== photoId),
    );
  };

  return (
    <div>
      <h1>Pending Photos</h1>
      {pendingPhotos.length === 0 ? (
        <p>No pending photos for verification.</p>
      ) : (
        <ul>
          {pendingPhotos.map((photo) => (
            <li key={photo.pendingPhoto.id}>
              <VerificationPhoto
                photo={photo.pendingPhoto}
                onSuccess={() => handleSuccess(photo.pendingPhoto.id)}
                profile={photo.profile}
              />
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}

export default PendingPhotos;
