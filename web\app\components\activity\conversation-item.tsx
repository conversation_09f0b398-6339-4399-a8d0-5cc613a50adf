import { type Message } from '@prisma/client';
import { Link } from '@remix-run/react';
import { useTranslation } from 'react-i18next';
import Typography from '#app/components/typography';
import { Avatar, AvatarFallback, AvatarImage } from '#app/components/ui/avatar';
import { cn, timeAgo } from '#app/utils/misc';
import { type EnrichedOtherProfile } from '#shared/types/conversations';

interface ConversationItemProps {
  lastMessage: Message;
  otherProfile: EnrichedOtherProfile;
  hasUnreadMessages?: boolean;
}

const ConversationItem = ({
  lastMessage,
  otherProfile,
  hasUnreadMessages = false,
}: ConversationItemProps) => {
  const { t } = useTranslation();

  if (!otherProfile) {
    return null;
  }

  const mainPhotoUrl = otherProfile.mainPhoto?.mediumUrl;

  return (
    <Link to={`/messages/${otherProfile.username}`}>
      <div className="flex items-center gap-3 hover:bg-muted/50 cursor-pointer px-3 py-3 rounded-2xl">
        <Avatar className="h-16 w-16 flex-shrink-0">
          <AvatarImage src={mainPhotoUrl} />
          <AvatarFallback>
            {otherProfile.firstName.slice(0, 1).toUpperCase()}
          </AvatarFallback>
        </Avatar>

        <div className="min-w-0 flex-1">
          <div className="flex items-center justify-between mb-1">
            <Typography variant="h6" className="!leading-none truncate">
              {otherProfile.firstName}
            </Typography>

            {hasUnreadMessages && (
              <div className="w-2 h-2 rounded-full bg-red-500 flex-shrink-0" />
            )}
          </div>

          <div className="flex items-center text-muted-foreground gap-2 w-full">
            <Typography
              variant="h6"
              as="p"
              className={cn(
                '!leading-none font-normal truncate min-w-0 flex-1 text-sm sm:text-base',
                hasUnreadMessages && 'font-medium text-foreground',
              )}
            >
              {lastMessage.content
                ? t(lastMessage.content)
                : `📷 ${t('Photos')}`}
            </Typography>

            <div className="flex items-center flex-shrink-0">
              <span className="h-0.5 w-0.5 rounded-full bg-muted-foreground/60" />

              <Typography
                variant="small"
                as="span"
                className={cn(
                  'font-medium whitespace-nowrap ml-1 sm:ml-2 text-xs sm:text-sm',
                  hasUnreadMessages && 'text-foreground',
                )}
              >
                {timeAgo(lastMessage.createdAt)}
              </Typography>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default ConversationItem;
