import { type Profile } from '@prisma/client';
import { type FastifyInstance, type FastifyRequest } from 'fastify';
import { storiesRouter } from '#server/profile/stories';
import { prisma } from '#server/utils/db.server';
import {
  ForbiddenError,
  NoUserProfileError,
  NotFoundError,
} from '#server/utils/errors.server';
import { blockProfileRouter } from './block';
import { checkUsernameRoute } from './check-username';
import { fullProfileRouter } from './full';
import { likeProfileRouter } from './like';
import { notificationsRouter } from './notifications';
import { photoRouter } from './photos';
import { profileViewRouter } from './view';

declare module 'fastify' {
  interface FastifyRequest {
    profile: Profile;
    likedProfile: Profile;
    blockedProfile: Profile;
  }
}

async function checkProfileMiddleware(request: FastifyRequest) {
  // note we make another call to the database here instead of doing it when we grab the user
  const profile = await prisma.profile.findUnique({
    where: { userId: request.user.id },
  });

  if (!profile) {
    throw new NoUserProfileError();
  }

  request.profile = profile;
}

async function checkLikedProfileMiddleware(request: FastifyRequest) {
  const { profileId } = request.params as { profileId: string };
  const profile = await prisma.profile.findUnique({
    where: { id: profileId },
  });
  if (!profile) {
    throw new NotFoundError('Profile not found');
  }
  request.likedProfile = profile;
}

async function checkBlockedProfileMiddleware(request: FastifyRequest) {
  const { profileId } = request.params as { profileId: string };
  const profile = await prisma.profile.findUnique({
    where: { id: profileId },
  });
  if (!profile) {
    throw new NotFoundError('Profile not found');
  }
  request.blockedProfile = profile;
}

async function checkActionRestrictionMiddleware(request: FastifyRequest) {
  const { profile } = request;
  const otherProfile = request.likedProfile || request.blockedProfile;

  if (!otherProfile) return;

  const blockExists = await prisma.profileBlock.findFirst({
    where: {
      OR: [
        {
          blockerProfileId: otherProfile.id,
          blockedProfileId: profile.id,
        },
        {
          blockerProfileId: profile.id,
          blockedProfileId: otherProfile.id,
        },
      ],
    },
  });

  if (blockExists) {
    throw new ForbiddenError(
      'This action cannot be performed because you have been blocked by this user.',
    );
  }
}

export async function profileRouter(fastify: FastifyInstance) {
  fastify.addHook('preHandler', checkProfileMiddleware);
  fastify.register(photoRouter, { prefix: '/photos' });
  fastify.register(checkUsernameRoute); // currently not used
  fastify.register(notificationsRouter, { prefix: '/notifications' });
  fastify.register(async (likeProfileRoutes) => {
    likeProfileRoutes.addHook('preHandler', checkLikedProfileMiddleware);
    likeProfileRoutes.addHook('preHandler', checkActionRestrictionMiddleware);
    likeProfileRoutes.register(likeProfileRouter);
  });
  fastify.register(async (blockProfileRoutes) => {
    blockProfileRoutes.addHook('preHandler', checkBlockedProfileMiddleware);
    blockProfileRoutes.register(blockProfileRouter);
  });
  fastify.register(storiesRouter, { prefix: '/stories' });
  fastify.register(fullProfileRouter);
  fastify.register(async (profileViewRoutes) => {
    profileViewRoutes.addHook('preHandler', checkLikedProfileMiddleware);
    profileViewRoutes.addHook('preHandler', checkActionRestrictionMiddleware);
    profileViewRoutes.register(profileViewRouter);
  });
}
