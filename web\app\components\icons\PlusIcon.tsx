import * as React from 'react';

interface PlusIconProps extends React.SVGProps<SVGSVGElement> {
  className?: string;
  title?: string;
}

export const PlusIcon: React.FC<PlusIconProps> = ({
  className = '',
  title,
  ...props
}) => {
  return (
    <svg
      width="17"
      height="18"
      viewBox="0 0 17 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M8.52734 1.5V16.5M16.0273 9H1.02734"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default PlusIcon;
