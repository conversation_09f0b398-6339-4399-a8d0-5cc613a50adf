import { redirect } from '@remix-run/node';
import { getBasicInfoFromRequest } from '#server/utils/auth.server';
import { getBestRouteToLandUser } from '#server/utils/user.server';
import { LOGIN_ROUTE } from '#shared/constants';

export const loader = async ({request} : {request: Request}) => {
  const { user } = await getBasicInfoFromRequest({
    request,
    includeProfile: false,
    extendedProfileData: false,
  })

  const route = user ? await getBestRouteToLandUser(user) : LOGIN_ROUTE;
  return redirect(route);
}