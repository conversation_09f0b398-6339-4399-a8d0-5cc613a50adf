import {
  type DatingPreference,
  type Lifestyle,
  type Location,
  PhotoType,
  type Prisma,
  type Profile,
  VideoType,
} from '@prisma/client';
import { endOfYear, startOfYear, subYears } from 'date-fns';
import { prisma } from '#server/utils/db.server';
import { attachImageUrlsToPhoto } from '#server/utils/media.server';
import {
  enrichProfileForProfileCard,
  EXTRA_PROFILE_CARD_INCLUDES,
  getAllOnlineUsers,
  getOnlineStatusForProfileIds,
  getSerializableAppearance,
  getUserAges,
} from '#server/utils/profile.server';
import {
  ProfileCategoryEnum,
  VerificationStatus,
} from '#shared/constants/enums';
import {
  DEFAULT_PAGE,
  DEFAULT_PAGE_SIZE,
} from '#shared/constants/pagination.js';
import { AGES, ONLINE_STATUS_MAP } from '#shared/constants/profile';

// TODO: add location radius
export type SearchParameters = {
  gender: Profile['gender'];
  genderInterest: Profile['genderInterest'];
  minAge?: number;
  maxAge?: number;
  size?: number;
  page?: number;
  category?: ProfileCategoryEnum;
} & Partial<
  Pick<
    DatingPreference,
    | 'bodyTypes'
    | 'drinking'
    | 'smoking'
    | 'bodyTypes'
    | 'relationshipPreference'
  > &
    Pick<Location, 'countryIso2' | 'regionCode'> &
    Pick<Lifestyle, 'haveChildren' | 'wantChildren' | 'religion'>
>;

const getRequiredProfileFilters = (): Prisma.ProfileWhereInput => {
  return {
    // Has a verified profile photo
    parentPhotos: {
      some: {
        photoType: PhotoType.MAIN,
        verificationStatus: VerificationStatus.APPROVED,
      },
    },
    // In the future, we can add more filters here.
    // For example, to check for a verified email, we would use:
    // user: {
    //   verificationStatus: VerificationStatus.APPROVED
    // }
    // or
    // user: {
    //   verifiedAt: {
    //     not: null
    //   }
    // }
  };
};

export async function performSearch(
  profile: Profile,
  {
    minAge = AGES.MIN_AGE,
    maxAge = AGES.MAX_AGE,
    bodyTypes,
    drinking,
    smoking,
    relationshipPreference,
    page = DEFAULT_PAGE,
    size = DEFAULT_PAGE_SIZE,
    category = ProfileCategoryEnum.RECOMMENDED,
  }: Partial<SearchParameters>,
) {
  const isNewProfileCategory = category === ProfileCategoryEnum.NEW_PROFILES;
  const isMostLikedCategory = category === ProfileCategoryEnum.MOST_LIKED;
  const isOnlineNowCategory = category === ProfileCategoryEnum.ONLINE_NOW;

  const allOnlineUsers = isOnlineNowCategory ? await getAllOnlineUsers() : [];

  const [blockedByUsers, iHaveBlocked] = await Promise.all([
    prisma.profileBlock.findMany({
      where: { blockedProfileId: profile.id },
      select: { blockerProfileId: true },
    }),
    prisma.profileBlock.findMany({
      where: { blockerProfileId: profile.id },
      select: { blockedProfileId: true },
    }),
  ]);

  const blockedByUserIds = blockedByUsers.map(
    (block) => block.blockerProfileId,
  );
  const iHaveBlockedIds = iHaveBlocked.map((block) => block.blockedProfileId);

  const allBlockedUserIds = [...blockedByUserIds, ...iHaveBlockedIds];

  // relationship preferences are self matching
  const datingPreferenceQuery = relationshipPreference?.length
    ? {
        datingPreference: {
          relationshipPreference: {
            hasSome: relationshipPreference,
          },
        },
      }
    : {};

  const appearanceQuery = bodyTypes?.length
    ? {
        appearance: {
          bodyType: {
            in: bodyTypes,
          },
        },
      }
    : {};

  const lifeStyleQuery = {
    lifestyle: {
      ...(smoking?.length && { smoking: { in: smoking } }),
      ...(drinking?.length && { drinking: { in: drinking } }),
    },
  };
  // INSERT_YOUR_REWRITE_HERE

  // We get the date of birth range if minAge and maxAge are defined
  const today = new Date();
  const minDateOfBirth =
    maxAge !== undefined ? startOfYear(subYears(today, maxAge)) : undefined;
  const maxDateOfBirth =
    minAge !== undefined ? endOfYear(subYears(today, minAge)) : undefined;

  const dobQuery =
    minDateOfBirth && maxDateOfBirth
      ? {
          user: {
            privateUser: {
              dateOfBirth: {
                gte: minDateOfBirth,
                lte: maxDateOfBirth,
              },
            },
          },
        }
      : {};

  const orderBy: Prisma.ProfileOrderByWithRelationInput | undefined =
    isNewProfileCategory
      ? { createdAt: 'desc' }
      : isMostLikedCategory
        ? {
            profileLikesOfMe: {
              _count: 'desc',
            },
          }
        : undefined;

  const requiredFilters = getRequiredProfileFilters();

  const whereClause: Prisma.ProfileWhereInput = {
    id: {
      not: profile.id,
      ...(isOnlineNowCategory && { in: allOnlineUsers }),
      notIn: allBlockedUserIds,
    },
    ...dobQuery,
    ...appearanceQuery,
    ...lifeStyleQuery,
    ...datingPreferenceQuery,
    ...requiredFilters,
  };

  const profiles = await prisma.profile.findMany({
    where: whereClause,
    include: {
      // we can limit the fields we want to return later
      // make sure to keep in sync with getProfileCardInfoFromProfileIds
      ...EXTRA_PROFILE_CARD_INCLUDES,
      allVideos: {
        where: {
          videoType: VideoType.MAIN,
        },
      },
      profileLikesOfMe: true,
    },
    orderBy,
    take: size,
    skip: size * (page - 1),
  });

  const totalCount = await prisma.profile.count({
    where: whereClause,
  });

  const totalPages = Math.ceil(totalCount / size);

  const likerProfileId = profile.id;
  const profileIds = profiles.map((profile) => profile.id);
  const ages = await getUserAges(profileIds);
  const [allLikes, enrichedProfiles, allMainPhotos, onlineStatuses] =
    await Promise.all([
      prisma.profileLike.findMany({
        where: {
          likerProfileId: likerProfileId,
          likedProfileId: {
            in: profileIds,
          },
        },
      }),
      await Promise.all(
        profiles.map((profile) => {
          return enrichProfileForProfileCard({
            ...profile,
            allPhotos: profile.parentPhotos,
            age: ages[profile.id] ?? null,
          });
        }),
      ),
      prisma.parentPhoto.findMany({
        where: {
          photoType: PhotoType.MAIN,
          profileId: {
            in: profileIds,
          },
        },
        include: {
          photoVariants: true,
        },
      }),
      getOnlineStatusForProfileIds(profileIds),
    ]);
  const signedMainPhotos = allMainPhotos.map((photo) =>
    attachImageUrlsToPhoto(photo),
  );

  // merge the likes, the profiles and the online statuses
  return {
    data: enrichedProfiles.map((profile) => {
      const likesThisProfile = allLikes.some(
        (like) => like.likedProfileId === profile.id,
      );
      const onlineStatusWithTime = onlineStatuses[profile.id] ?? {
        onlineStatus: ONLINE_STATUS_MAP.OFFLINE,
        lastOnlineTime: null,
      };
      const mainPreviewPhoto = signedMainPhotos.find(
        (photo) => photo.id === profile.mainPhoto?.id,
      );
      return {
        ...profile,
        likesThisProfile,
        onlineStatus: onlineStatusWithTime,
        mainPreviewPhoto,
        appearance: getSerializableAppearance(profile.appearance),
      };
    }),
    total: totalCount,
    page,
    limit: size,
    totalPages,
  };
}
