// REACT
import React, { useState, useRef, useEffect } from 'react';

// COMPONENTS
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '#app/components/ui/select';

// UTILS
import { cn } from '#app/utils/misc.js';
interface Option<O extends string> {
  value: O;
  label: string;
}
interface SelectProps<O extends string>
  extends Omit<React.ComponentProps<typeof Select>, 'onValueChange'> {
  onChange: (value: O) => void;
  options: Option<O>[];
  name: string;
  placeholder?: string;
  inputRef?: React.Ref<HTMLSelectElement>;
  formValue?: O;
  'aria-invalid'?: boolean;
}

type MutableRefObject<T> = {
  current: T;
};

const GenericSelect = <O extends string>({
  onChange,
  inputRef,
  name,
  options,
  placeholder,
  formValue,
  ...props
}: SelectProps<O>) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const internalRef = useRef<HTMLButtonElement | null>(null);

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (open) {
      setIsFocused(true);
    }
  };

  const setRefs = (element: HTMLButtonElement | null) => {
    internalRef.current = element;
    if (!inputRef) return;
    if (typeof inputRef === 'function') inputRef(element as any);
    else if (inputRef && typeof inputRef === 'object')
      (inputRef as MutableRefObject<HTMLButtonElement | null>).current =
        element;
  };

  useEffect(() => {
    const handleBlur = () => setIsFocused(false);
    const handleFocus = () => setIsFocused(true);
    const element = internalRef.current;
    if (element) {
      element.addEventListener('blur', handleBlur);
      element.addEventListener('focus', handleFocus);
      return () => {
        element.removeEventListener('blur', handleBlur);
        element.removeEventListener('focus', handleFocus);
      };
    }
  }, []);

  // For rotating the input arrow
  useEffect(() => {
    if (!internalRef.current) return;
    const arrowElements = internalRef.current.querySelectorAll('svg');
    arrowElements.forEach((element) => {
      if (isOpen) element.style.transform = 'rotate(180deg)';
      else element.style.transform = 'rotate(0deg)';
    });
  }, [isOpen]);

  return (
    <>
      <Select
        {...props}
        onValueChange={onChange}
        onOpenChange={handleOpenChange}
      >
        <SelectTrigger
          ref={setRefs}
          className={cn(
            'rounded-xl h-12 px-4 transition-all duration-200 cursor-pointer',
            'focus:outline-none focus-visible:outline-none focus:ring-0 focus:ring-offset-0 focus:shadow-none',
            'outline-none',
            isOpen || isFocused
              ? 'border-2 border-transparent'
              : 'border border-gray-300',
          )}
          style={{
            background:
              isOpen || isFocused
                ? 'linear-gradient(white, white) padding-box, linear-gradient(90deg, #d33544, #2c80aa) border-box'
                : 'white',
          }}
          aria-label={name}
          aria-invalid={props['aria-invalid']}
        >
          <SelectValue placeholder={placeholder} className="text-gray-500" />
        </SelectTrigger>

        <SelectContent
          className={cn(
            'rounded-xl p-1 bg-white',
            'border border-gray-200',
            'focus:outline-none focus-visible:outline-none',
            'outline-none shadow-none',
          )}
          sideOffset={5}
          align="center"
        >
          {options.map((option) => (
            <CustomSelectItem key={option.value} value={option.value}>
              {option.label}
            </CustomSelectItem>
          ))}
        </SelectContent>
      </Select>

      <input type="hidden" name={name} value={formValue ?? props.value ?? ''} />
    </>
  );
};

interface CustomSelectItemProps
  extends React.ComponentPropsWithRef<typeof SelectItem> {
  children: React.ReactNode;
}

const CustomSelectItem = React.forwardRef<
  HTMLDivElement,
  CustomSelectItemProps
>(({ className, children, ...props }, forwardedRef) => {
  const itemRef = useRef<HTMLDivElement | null>(null);

  const setRefs = (element: HTMLDivElement | null) => {
    itemRef.current = element;
    if (typeof forwardedRef === 'function') forwardedRef(element);
    else if (forwardedRef) forwardedRef.current = element;
  };

  useEffect(() => {
    if (!itemRef.current) return;
    const icons = itemRef.current.querySelectorAll('svg');
    icons.forEach((icon) => {
      if (icon.parentNode) {
        icon.parentNode.removeChild(icon);
      }
    });
    const indicators = itemRef.current.querySelectorAll('[role="indicator"]');
    indicators.forEach((indicator) => {
      if (indicator.parentNode) {
        indicator.parentNode.removeChild(indicator);
      }
    });
  });

  return (
    <SelectItem
      ref={setRefs}
      className={cn(
        'rounded-xl py-2 px-3 mb-1 cursor-pointer transition-colors duration-150',
        'hover:bg-[#f8dce0] data-[highlighted]:bg-[#f8dce0] data-[state=checked]:bg-[#f8dce0]',
        'focus:outline-none focus-visible:outline-none outline-none',
        className,
      )}
      {...props}
    >
      {children}
    </SelectItem>
  );
});

CustomSelectItem.displayName = 'CustomSelectItem';

export { GenericSelect };
