services:
  postgres:
    image: postgis/postgis:17-3.5
    ports:
      - '${POSTGRES_PORT:-5432}:5432' # Dynamically set the port from the environment or default to 5432
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: filipina-meet-db
    volumes:
      - pgdata:/var/lib/postgresql/data

  redis:
    container_name: filipina-meet-redis
    image: redis:latest
    ports:
      - '${REDIS_PORT:-6379}:6379' # Dynamically set the port from the environment or default to 6380
    volumes:
      - redisdata:/data

volumes:
  pgdata:
  redisdata:
