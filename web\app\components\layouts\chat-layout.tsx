import { Outlet } from '@remix-run/react';
import Footer from '#app/components/footer';
import { Header } from '#app/components/header';
import { useMobile } from '#app/contexts/mobile-context';
import { type CommonLayoutProps } from '#app/types/layouts';

const ChatLayout = ({ user, profile, shouldShowFooter }: CommonLayoutProps) => {
  const { isMobile } = useMobile();
  const isDesktop = !isMobile;

  return (
    <div className="h-[100dvh] overflow-hidden">
      <div className="flex h-full gap-0">
        <div className="w-full bg-background h-full flex flex-col">
          {user && profile && isDesktop ? (
            <Header user={user} profile={profile} />
          ) : null}
          <Outlet />
        </div>
      </div>
      {shouldShowFooter && <Footer />}
    </div>
  );
};

export default ChatLayout;
