{"name": "filipina-meet-web", "private": true, "sideEffects": false, "type": "module", "imports": {"#app/*": "./app/*", "#tests/*": "./tests/*", "#shared/*": "./../shared/*", "#server/*": "./server/*", "#root/*": "./*"}, "scripts": {"build": "rm -rf server-build && run-s build:*", "build:icons": "tsx ./app/images/build-icons.ts", "build:remix": "remix vite:build", "build:server": "tsc --project ./../tsconfig.server.json --resolveJsonModule && npx tsc-alias -p ./../tsconfig.server.json --resolve-full-paths", "predev": "npm run build:icons --silent", "dev": "cross-env NODE_ENV=development node ./server/dev-server.js", "format": "prettier --write .", "lint": "eslint .", "lint:fix": "eslint . --fix", "prisma:migrate:dev": "prisma migrate dev", "prisma:generate": "prisma generate", "prisma:migrate:deploy": "prisma migrate deploy", "prisma:migrate:reset": "prisma migrate reset", "prisma:studio": "prisma studio", "setup": "npm run build && prisma generate && prisma migrate deploy && playwright install", "start": "cross-env NODE_ENV=production node ./server-build/web/server/index.js", "start:mocks": "cross-env NODE_ENV=production MOCKS=true tsx .", "test": "dotenv -e .env.test -- npx prisma migrate reset --force --skip-seed && vitest", "test:ci": "dotenv -e .env.test -- npx prisma migrate reset --force --skip-seed && vitest --run", "coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:dev": "playwright test --ui", "pretest:e2e:run": "npm run build", "test:e2e:run": "cross-env CI=true playwright test", "test:e2e:install": "npx playwright install --with-deps chromium", "validate": "run-p \"test -- --run\" lint typecheck test:e2e:run", "prepare": "husky", "seed": "tsx ./scripts/seed.ts"}, "eslintIgnore": ["/node_modules", "/build", "/public/build", "/playwright-report", "/server-build"], "dependencies": {"@aws-sdk/client-s3": "^3.693.0", "@aws-sdk/lib-storage": "^3.693.0", "@aws-sdk/s3-request-presigner": "^3.693.0", "@cloudinary/react": "^1.13.1", "@cloudinary/url-gen": "^1.21.0", "@conform-to/react": "^1.2.2", "@conform-to/zod": "^1.2.2", "@epic-web/cachified": "^5.2.0", "@epic-web/client-hints": "^1.3.5", "@epic-web/invariant": "^1.0.0", "@epic-web/remember": "^1.1.0", "@epic-web/totp": "^2.0.0", "@fastify/caching": "^9.0.1", "@fastify/compress": "^8.0.1", "@fastify/helmet": "^12.0.1", "@fastify/static": "^8.0.2", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.1.5", "@mcansh/remix-fastify": "^4.0.1", "@nasa-gcn/remix-seo": "^2.0.1", "@paralleldrive/cuid2": "^2.2.2", "@prisma/client": "^6.11.1", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.4", "@react-email/components": "0.0.28", "@remix-run/react": "2.14.0", "@sentry/profiling-node": "^9.38.0", "@sentry/remix": "^9.38.0", "@tanstack/react-query": "^5.60.5", "address": "^2.0.3", "bcryptjs": "^2.4.3", "chalk": "^5.3.0", "class-variance-authority": "^0.7.0", "close-with-grace": "^2.1.0", "cloudinary": "^2.5.1", "clsx": "^2.1.1", "compression": "^1.7.5", "cookie": "^1.0.1", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "execa": "^9.5.1", "express": "^4.21.1", "express-rate-limit": "^7.4.1", "fastify": "^5.1.0", "fastify-compress": "^4.1.0", "fastify-cors": "^6.1.0", "fastify-helmet": "^7.1.0", "fastify-print-routes": "^4.0.0", "filipina-meet-web": "file:", "get-port": "^7.1.0", "helmet": "^8.0.0", "i18next": "^23.16.5", "i18next-browser-languagedetector": "^8.0.0", "i18next-fs-backend": "^2.3.2", "i18next-http-backend": "^2.6.2", "input-otp": "^1.4.1", "intl-parse-accept-language": "^1.0.0", "ioredis": "^5.4.1", "isbot": "^5.1.17", "jsonwebtoken": "^9.0.2", "libphonenumber-js": "^1.11.19", "litefs-js": "^1.1.2", "lodash": "^4.17.21", "lucide-react": "^0.460.0", "moment": "^2.30.1", "morgan": "^1.10.0", "qrcode": "^1.5.4", "react-day-picker": "^9.3.1", "react-icons": "^5.4.0", "react-responsive": "^10.0.0", "remix-auth": "^3.7.0", "remix-auth-github": "^2.0.0", "remix-i18next": "^6.4.1", "remix-utils": "^7.7.0", "set-cookie-parser": "^2.7.1", "sharp": "^0.33.5", "sonner": "^1.7.0", "source-map-support": "^0.5.21", "spin-delay": "^2.0.1", "tailwind-merge": "^2.5.4", "tailwindcss": "^3.4.15", "tailwindcss-animate": "^1.0.7", "tailwindcss-radix": "^3.0.5", "telnyx": "^2.0.0-beta.5", "uuid": "^11.1.0", "ws": "^8.18.3", "yet-another-react-lightbox": "^3.21.8"}, "devDependencies": {"@epic-web/config": "^1.16.1", "@faker-js/faker": "^9.2.0", "@playwright/test": "^1.48.2", "@remix-run/dev": "2.14.0", "@remix-run/serve": "2.14.0", "@remix-run/testing": "2.14.0", "@sentry/vite-plugin": "^2.22.6", "@sly-cli/sly": "^1.14.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@total-typescript/ts-reset": "^0.6.1", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cookie": "^1.0.0", "@types/eslint": "^9.6.1", "@types/express": "^5.0.0", "@types/fs-extra": "^11.0.4", "@types/jsonwebtoken": "^9.0.7", "@types/morgan": "^1.9.9", "@types/node": "^22.9.0", "@types/qrcode": "^1.5.5", "@types/set-cookie-parser": "^2.4.10", "@types/source-map-support": "^0.5.10", "@types/ws": "^8.18.1", "@vitejs/plugin-react": "^4.3.3", "@vitest/coverage-v8": "^2.1.5", "autoprefixer": "^10.4.20", "enforce-unique": "^1.3.0", "eslint-plugin-import": "^2.31.0", "fs-extra": "^11.2.0", "husky": "^9.1.6", "jsdom": "^25.0.1", "msw": "2.6.5", "node-html-parser": "^6.1.13", "prettier": "^3.3.3", "prettier-plugin-sql": "^0.18.1", "prettier-plugin-tailwindcss": "^0.6.8", "remix-flat-routes": "^0.6.5", "tsc-alias": "^1.8.10", "tsx": "^4.19.2", "vite": "^5.4.11", "vitest": "^2.1.5"}, "bin": {"npm-run-all": "./bin/npm-run-all/index.js", "run-p": "./bin/run-p/index.js", "run-s": "./bin/run-s/index.js"}, "epic-stack": {"head": "06f908b603826819e5dd9e8938f1fd35fba21047", "date": "2024-08-03T00:22:28Z"}, "engines": {"node": "22"}}