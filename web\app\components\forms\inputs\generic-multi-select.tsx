// COMPONENTS
import {
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from '@headlessui/react';

// STYLES
import { ChevronUpDownIcon } from '@heroicons/react/20/solid';
import { CheckIcon } from '@heroicons/react/24/solid';

// REACT
import React, {
  Fragment,
  type ReactNode,
  useEffect,
  useState,
  useRef,
  useLayoutEffect,
} from 'react';

// UTILS
import { cn, removeEmptyStrings } from '#app/utils/misc.js';

interface Option<T extends string> {
  value: T;
  label: ReactNode;
  icon?: ReactNode;
}

interface MultiSelectProps<T extends string> {
  name: string;
  options: Option<T>[];
  value?: T[];
  onChange?: (selected: T[]) => void;
  onBlur?: () => void;
}

function GenericMultiSelect<T extends string>({
  name,
  options,
  value = [],
  onChange,
  onBlur,
}: MultiSelectProps<T>) {
  const [selectedValues, setSelectedValues] = useState<T[]>(
    value.length ? removeEmptyStrings(value) : [],
  );
  const [visible, setVisible] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const buttonRef = useRef<HTMLButtonElement | null>(null);
  const [dropUp, setDropUp] = useState(false);

  useEffect(() => {
    if (value.length) setSelectedValues(removeEmptyStrings(value));
  }, [value]);

  useEffect(() => {
    const handleBlur = () => {
      setIsFocused(false);
      onBlur?.();
    };

    const handleFocus = () => {
      setIsFocused(true);
    };

    const element = buttonRef.current;
    if (element) {
      element.addEventListener('blur', handleBlur);
      element.addEventListener('focus', handleFocus);
      return () => {
        element.removeEventListener('blur', handleBlur);
        element.removeEventListener('focus', handleFocus);
      };
    }
  }, [onBlur]);

  useLayoutEffect(() => {
    if (!visible || !buttonRef.current) {
      setDropUp(false);
      return;
    }
    const rect = buttonRef.current.getBoundingClientRect();
    const spaceBelow = window.innerHeight - rect.bottom;
    const spaceAbove = rect.top;
    const dropdownHeight = 320;
    if (spaceBelow < dropdownHeight && spaceAbove > spaceBelow) {
      setDropUp(true);
    } else {
      setDropUp(false);
    }
  }, [visible]);

  const handleSelect = (selected: T[]) => {
    setSelectedValues(selected);
    onChange?.(selected);
  };

  const toggleDropdown = () => {
    setVisible(!visible);
  };

  const handleRemoveItem = (val: T, e: React.MouseEvent) => {
    e.stopPropagation();
    const newSelected = selectedValues.filter((v) => v !== val);
    setSelectedValues(newSelected);
    onChange?.(newSelected);
  };

  const handleRemoveItemByKeyboard = (val: T, e: React.KeyboardEvent) => {
    e.preventDefault();
    e.stopPropagation();
    const newSelected = selectedValues.filter((v) => v !== val);
    setSelectedValues(newSelected);
    onChange?.(newSelected);
  };

  const handleSelectAll = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (selectedValues.length === options.length) {
      setSelectedValues([]);
      onChange?.([]);
    } else {
      const allValues = options.map((opt) => opt.value);
      setSelectedValues(allValues);
      onChange?.(allValues);
    }
  };

  return (
    <div className="relative w-full">
      <Listbox value={selectedValues} onChange={handleSelect} multiple>
        {() => (
          <>
            <div className="relative">
              <ListboxButton
                ref={buttonRef}
                onClick={toggleDropdown}
                className={cn(
                  'relative w-full cursor-pointer rounded-xl transition-all duration-200 text-left',
                  'focus:outline-none focus-visible:outline-none focus:ring-0 focus:ring-offset-0 focus:shadow-none',
                  'outline-none',
                  visible || isFocused
                    ? 'border-2 border-transparent'
                    : 'border border-gray-300',
                )}
                style={{
                  background:
                    visible || isFocused
                      ? 'linear-gradient(white, white) padding-box, linear-gradient(90deg, #d33544, #2c80aa) border-box'
                      : 'white',
                }}
              >
                <div
                  className={cn(
                    'flex flex-wrap gap-1.5 p-2',
                    selectedValues.length === 0 ? 'h-12' : 'min-h-[42px]',
                  )}
                >
                  {selectedValues.length > 0 && (
                    <div className="flex flex-wrap gap-1.5 w-full">
                      {selectedValues.map((val) => {
                        const option = options.find((opt) => opt.value === val);
                        return (
                          <div
                            key={val}
                            className={cn(
                              'bg-[#fcd6df] text-gray-800 rounded-full px-2 py-0.5 flex items-center',
                            )}
                          >
                            {option?.icon && (
                              <span className="mr-1 text-xs">
                                {option.icon}
                              </span>
                            )}

                            <span className="text-xs">{option?.label}</span>

                            <span
                              role="button"
                              tabIndex={0}
                              onClick={(e) => handleRemoveItem(val, e)}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter' || e.key === ' ') {
                                  e.preventDefault();
                                  handleRemoveItemByKeyboard(val, e);
                                }
                              }}
                              className={cn(
                                'ml-1 text-sm text-[#1E1E1E] cursor-pointer',
                              )}
                              aria-label={`Remove ${option?.label}`}
                            >
                              <span className="text-xl">×</span>
                            </span>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>

                <span
                  className={cn(
                    'absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none',
                  )}
                >
                  <ChevronUpDownIcon
                    className="h-5 w-5 text-gray-400"
                    aria-hidden="true"
                  />
                </span>
              </ListboxButton>

              <ListboxOptions
                className={cn(
                  'absolute z-10 w-full overflow-auto rounded-lg bg-white',
                  'shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none',
                  dropUp ? 'bottom-full mb-2 mt-0' : 'top-full mt-1',
                  'max-h-[60vh] sm:max-h-60',
                )}
              >
                <div
                  className={cn(
                    'flex items-center p-3 cursor-pointer hover:bg-[#fcd6df] border-b border-gray-200',
                  )}
                  onClick={handleSelectAll}
                >
                  <div
                    className={cn(
                      'flex items-center justify-center w-5 h-5 border-2 rounded',
                      selectedValues.length === options.length
                        ? 'bg-[#d33544] border-[#d33544]'
                        : 'border-gray-300',
                    )}
                  >
                    {selectedValues.length === options.length && (
                      <CheckIcon className="h-3.5 w-3.5 text-white" />
                    )}
                  </div>

                  <span className="ml-2 text-sm font-medium">Select all</span>
                </div>

                {options.map((option) => (
                  <ListboxOption
                    key={option.value}
                    value={option.value}
                    as={Fragment}
                  >
                    {({ selected }) => (
                      <li
                        className={cn(
                          'flex items-center p-3 cursor-pointer hover:bg-[#fcd6df] border-b border-gray-100',
                          selected ? 'bg-red-50' : '',
                        )}
                      >
                        <div
                          className={cn(
                            'flex items-center justify-center w-5 h-5 border-2 rounded',
                            selected
                              ? 'bg-[#d33544] border-[#d33544]'
                              : 'border-gray-300',
                          )}
                        >
                          {selected && (
                            <CheckIcon className="h-3.5 w-3.5 text-white" />
                          )}
                        </div>

                        {option.icon && (
                          <span className="ml-2 mr-1">{option.icon}</span>
                        )}

                        <span className="ml-2 text-sm">{option.label}</span>
                      </li>
                    )}
                  </ListboxOption>
                ))}
              </ListboxOptions>
            </div>
          </>
        )}
      </Listbox>
      <input type="hidden" name={name} value={selectedValues} />
    </div>
  );
}

export { GenericMultiSelect };
