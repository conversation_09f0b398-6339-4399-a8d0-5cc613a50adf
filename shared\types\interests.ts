// Enum for Interest Types
export enum InterestType {
  Adventure = 'Adventure',
  Art = 'Art',
  Books = 'Books',
  Cooking = 'Cooking',
  Dance = 'Dance',
  Fitness = 'Fitness',
  Gaming = 'Gaming',
  Gardening = 'Gardening',
  Hiking = 'Hiking',
  Meditation = 'Meditation',
  Movies = 'Movies',
  Music = 'Music',
  Nature = 'Nature',
  Photography = 'Photography',
  Poetry = 'Poetry',
  Reading = 'Reading',
  Running = 'Running',
  Shopping = 'Shopping',
  Sports = 'Sports',
  Surfing = 'Surfing',
  Swimming = 'Swimming',
  Technology = 'Technology',
  Theater = 'Theater',
  Travel = 'Travel',
  Volunteering = 'Volunteering',
  Wine = 'Wine',
  Yoga = 'Yoga',
  CookingClasses = 'CookingClasses',
  Languages = 'Languages',
  Festivals = 'Festivals',
}

type InterestWithIcon = {
  label: string;
  icon: string;
  value: InterestType;
}

export const InterestWithIcons: InterestWithIcon[] = [
  {
    label: 'Adventure',
    icon: '🌄',
    value: InterestType.Adventure,
  },
  {
    label: 'Art',
    icon: '🎨',
    value: InterestType.Art,
  },
  {
    label: 'Books',
    icon: '📚',
    value: InterestType.Books,
  },
  {
    label: 'Cooking',
    icon: '🍳',
    value: InterestType.Cooking,
  },
  {
    label: 'Dance',
    icon: '💃',
    value: InterestType.Dance,
  },
  {
    label: 'Fitness',
    icon: '💪',
    value: InterestType.Fitness,
  },
  {
    label: 'Gaming',
    icon: '🎮',
    value: InterestType.Gaming,
  },
  {
    label: 'Gardening',
    icon: '🌻',
    value: InterestType.Gardening,
  },
  {
    label: 'Hiking',
    icon: '🥾',
    value: InterestType.Hiking,
  },
  {
    label: 'Meditation',
    icon: '🧘',
    value: InterestType.Meditation,
  },
  {
    label: 'Movies',
    icon: '🎬',
    value: InterestType.Movies,
  },
  {
    label: 'Music',
    icon: '🎶',
    value: InterestType.Music,
  },
  {
    label: 'Nature',
    icon: '🌳',
    value: InterestType.Nature,
  },
  {
    label: 'Photography',
    icon: '📸',
    value: InterestType.Photography,
  },  
  {
    label: 'Poetry',
    icon: '📜',
    value: InterestType.Poetry,
  },
  {
    label: 'Reading',
    icon: '📖',
    value: InterestType.Reading,
  },
  {
    label: 'Running',
    icon: '🏃',
    value: InterestType.Running,
  },  
  {
    label: 'Shopping',
    icon: '🛍️',
    value: InterestType.Shopping,
  },
  {
    label: 'Sports',
    icon: '🏅',
    value: InterestType.Sports,
  },
  {
    label: 'Surfing',
    icon: '🏄',
    value: InterestType.Surfing,
  },
  {
    label: 'Swimming',
    icon: '🏊',
    value: InterestType.Swimming,
  },
  {
    label: 'Technology',
    icon: '💻',
    value: InterestType.Technology,
  },
  {
    label: 'Theater',
    icon: '🎭',
    value: InterestType.Theater,
  },
  {
    label: 'Travel',
    icon: '✈️',
    value: InterestType.Travel,
  },
  {
    label: 'Volunteering',
    icon: '🤝',
    value: InterestType.Volunteering,
  },
  {
    label: 'Wine',
    icon: '🍷',
    value: InterestType.Wine,
  },
  {
    label: 'Yoga',
    icon: '🧘‍♀️',
    value: InterestType.Yoga,
  },
  {
    label: 'Cooking Classes',
    icon: '👩‍🍳',
    value: InterestType.CookingClasses,
  },
  {
    label: 'Languages',
    icon: '🗣️',
    value: InterestType.Languages,
  },
  {
    label: 'Festivals',
    icon: '🎉',
    value: InterestType.Festivals,
  },
]
