import React from 'react';

import { useTranslation } from 'react-i18next';
import { type PhotoWithUrls } from '#shared/types/media';
import Typography from '../../typography';

interface ReadonlyGalleryPhotosProps {
  galleryPhotos: PhotoWithUrls[];
}

const ReadonlyGalleryPhotos = ({
  galleryPhotos,
}: ReadonlyGalleryPhotosProps) => {
  const { t } = useTranslation();
  // Photo slots configuration
  const photoSlots = [
    { size: 'large' as const, position: 0 },
    { size: 'large' as const, position: 1 },
    { size: 'small' as const, position: 2 },
    { size: 'small' as const, position: 3 },
    { size: 'small' as const, position: 4 },
    { size: 'small' as const, position: 5 },
  ];

  // Separate slots by size
  const largeSlots = photoSlots.filter((slot) => slot.size === 'large');
  const smallSlots = photoSlots.filter((slot) => slot.size === 'small');

  const largePhotoSlotStyle = {
    className:
      'relative h-64 w-64 rounded-3xl bg-neutral-100 flex items-center justify-center overflow-hidden',
  };
  const smallPhotoSlotsStyle = {
    className:
      'relative w-[133px] h-[133px] rounded-3xl bg-neutral-100 flex items-center justify-center overflow-hidden',
  };

  return (
    <div className="flex flex-col gap-2">
      <Typography variant="h5" className="font-medium mb-2 !leading-none">
        {t('Gallery photos')}
      </Typography>
      <div className="flex flex-col sm:flex-col md:flex-col lg:flex-row gap-5 items-start">
        {/* Large gallery photos (2) */}
        <div className="flex flex-col md:flex-row lg:flex-row gap-3">
          {largeSlots.map((slot) => (
            <div key={`large-${slot.position}`} {...largePhotoSlotStyle}>
              {galleryPhotos[slot.position] && (
                <img
                  src={galleryPhotos?.[slot.position]?.mediumUrl}
                  alt={`Gallery photo ${slot.position + 1}`}
                  className="w-full h-full object-cover"
                />
              )}
            </div>
          ))}
        </div>

        {/* Small gallery photos (4) */}
        <div className="flex flex-col md:flex-row lg:flex-col gap-1 sm:gap-3">
          {/* Top row */}
          <div className="flex gap-3 ">
            {smallSlots.slice(0, 2).map((slot) => (
              <div key={`small-${slot.position}`} {...smallPhotoSlotsStyle}>
                {galleryPhotos[slot.position] && (
                  <img
                    src={galleryPhotos?.[slot.position]?.mediumUrl}
                    alt={`Gallery photo ${slot.position + 1}`}
                    className="w-full h-full object-cover"
                  />
                )}
              </div>
            ))}
          </div>

          {/* Bottom row */}
          <div className="flex gap-3 ">
            {smallSlots.slice(2, 4).map((slot) => (
              <div key={`small-${slot.position}`} {...smallPhotoSlotsStyle}>
                {galleryPhotos[slot.position] && (
                  <img
                    src={galleryPhotos?.[slot.position]?.mediumUrl}
                    alt={`Gallery photo ${slot.position + 1}`}
                    className="w-full h-full object-cover"
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReadonlyGalleryPhotos;
