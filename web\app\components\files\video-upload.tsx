import {
  PlusCircleIcon,
  VideoCameraIcon,
  XCircleIcon,
} from '@heroicons/react/20/solid';
import { Loader2 } from 'lucide-react';
import { useCallback, useState } from 'react';
import { Input } from '#app/components/forms/inputs';
import { Button } from '#app/components/ui/button';
import {
  useFileDeleteMutation,
  useFileUploadMutation,
} from '#app/mutations/files';

type VideoUploadProps = {
  onChange: (fileId: string) => void; // Callback to handle file changes
  value?: string | null; // Current value or preview URL
  onClear?: () => void; // Callback to handle clearing the video
  videoSize?: 'medium' | 'large'; // Size enum
  hidePreview?: boolean; // New prop to hide the uploaded video preview
  defaultVideoUrl?: string;
  onDelete?: () => void; // Callback to handle video deletion
  path: string;
};

const sizeClasses = {
  medium: 'h-32 w-32', // Medium size classes
  large: 'h-64 w-64', // Large size classes
};

const VideoUpload = ({
  onChange,
  value,
  videoSize = 'large', // Default to medium size
  hidePreview = false, // Default to showing the preview
  defaultVideoUrl, // Default to no default video
  onDelete, // Callback to handle video deletion
  path,
  ...props
}: VideoUploadProps) => {
  const [video, setVideo] = useState<string | null | undefined>(
    value || defaultVideoUrl,
  );
  const [fileInputKey, setFileInputKey] = useState<number>(0);
  const { mutate: uploadVideo, isPending: isUploading } =
    useFileUploadMutation();
  const { mutate: deleteVideo, isPending: isDeleting } =
    useFileDeleteMutation();

  const handleVideoChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      uploadVideo(
        { file, fileType: 'video', path },
        {
          onSuccess(data) {
            setVideo(data.url);
            onChange(data.fileId);
          },
        },
      );
    },
    [uploadVideo, onChange, path],
  );

  const clearFileInput = useCallback(() => {
    deleteVideo(
      { fileId: value || '', type: 'video' },
      {
        onSuccess: () => {
          setVideo(null);
          setFileInputKey((prevKey) => prevKey + 1); // Change the key to force the file input to reset
          onChange(''); // Notify parent component that the file has been cleared
          onDelete?.();
        },
      },
    );
  }, [deleteVideo, value, onChange, onDelete]);

  const openCameraDialog = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'video/*';
    input.capture = 'user';
    // maybe we shouldn't type case
    input.onchange = (event) =>
      handleVideoChange(
        event as unknown as React.ChangeEvent<HTMLInputElement>,
      );
    input.click();
  };

  const inputField = (
    <Input
      type="file"
      accept="video/*"
      className="inset-0 h-full w-full cursor-pointer opacity-0"
      onChange={handleVideoChange}
      key={fileInputKey}
      {...props}
    />
  );

  if (hidePreview) {
    return (
      <div className="relative h-12 w-12 cursor-pointer overflow-hidden">
        <PlusCircleIcon className="h-12 w-12 cursor-pointer text-gray-400" />
        <Button
          asChild
          size="icon"
          variant="ghost"
          className="absolute inset-0 cursor-pointer opacity-0"
        >
          {inputField}
        </Button>
      </div>
    );
  }

  return (
    <div
      className={`relative overflow-hidden rounded-lg border-2 border-gray-300 ${sizeClasses[videoSize]}`}
    >
      {(isUploading || isDeleting) && (
        <div className="absolute inset-0 z-10 flex items-center justify-center bg-black bg-opacity-50">
          <Loader2 className="h-12 w-12 animate-spin" />
        </div>
      )}
      {video ? (
        <>
          <video src={video} controls className="h-full w-full object-cover" />
          <Button
            variant="destructive"
            size="icon"
            onClick={clearFileInput}
            className="absolute right-1 top-1"
          >
            <XCircleIcon className="h-6 w-6 text-white" />
          </Button>
          {inputField} {/* Keep the input field always present but hidden */}
        </>
      ) : (
        <div className="relative flex h-full w-full items-center justify-center bg-gray-100">
          <Button
            variant="default"
            onClick={openCameraDialog}
            className="m-auto"
          >
            <VideoCameraIcon className="mr-1 h-5 w-5" />
          </Button>
          <Button
            asChild
            size="icon"
            variant="ghost"
            className="absolute inset-0 h-0 w-0 cursor-pointer opacity-0"
          >
            {inputField}
          </Button>
        </div>
      )}
    </div>
  );
};

export { VideoUpload };
