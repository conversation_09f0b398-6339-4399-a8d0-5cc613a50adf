import { useState } from 'react';
import {
  IoCheckmarkCircle,
  IoCloseCircle,
  IoInformationCircle,
  IoWarning,
} from 'react-icons/io5';
import Typography from '#app/components/typography';
import { Button } from '#app/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '#app/components/ui/dialog';
import { handleLoaderForLoggedInUser } from '#app/utils/remix-helpers.server';

export const loader = handleLoaderForLoggedInUser(async ({ privateUser }) => {
  return { privateUser };
});

const ActionCard = ({
  title,
  description,
  buttonText,
  buttonVariant = 'outline',
  onAction,
}: {
  title: string;
  description: string;
  buttonText: string;
  buttonVariant?: 'outline' | 'destructive';
  onAction: () => void;
}) => {
  return (
    <div className="border border-gray-200 rounded-lg p-6 space-y-4">
      <div>
        <Typography variant="h4" className="font-semibold text-gray-900 mb-2">
          {title}
        </Typography>
        <Typography variant="body" className="text-gray-600 text-sm">
          {description}
        </Typography>
      </div>

      <Button variant={buttonVariant} size="sm" onClick={onAction}>
        {buttonText}
      </Button>
    </div>
  );
};

const DeactivateModal = ({
  isOpen,
  onClose,
  onConfirm,
}: {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[480px] p-0 overflow-hidden">
        {/* Header with gradient background */}
        <div className="bg-gradient-to-r from-amber-50 to-orange-50 px-6 py-6 border-b border-amber-100">
          <DialogHeader className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="flex-shrink-0 w-10 h-10 bg-amber-100 rounded-full flex items-center justify-center">
                <IoInformationCircle className="w-6 h-6 text-amber-600" />
              </div>
              <div>
                <DialogTitle className="text-xl font-semibold text-amber-900 text-left">
                  Deactivate Account
                </DialogTitle>
                <DialogDescription className="text-amber-700 text-left">
                  Take a temporary break from FilipinaMeet
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
        </div>

        {/* Content */}
        <div className="px-6 py-6 space-y-6">
          <div className="space-y-4">
            <Typography className="text-gray-700 leading-relaxed">
              When you deactivate your account, here's what happens:
            </Typography>

            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-5 h-5 bg-amber-100 rounded-full flex items-center justify-center mt-0.5">
                  <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                </div>
                <span className="text-gray-700 text-sm">
                  Your profile becomes invisible to other users
                </span>
              </div>
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-5 h-5 bg-amber-100 rounded-full flex items-center justify-center mt-0.5">
                  <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                </div>
                <span className="text-gray-700 text-sm">
                  All notifications and messages are paused
                </span>
              </div>
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-5 h-5 bg-amber-100 rounded-full flex items-center justify-center mt-0.5">
                  <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                </div>
                <span className="text-gray-700 text-sm">
                  Your data and conversations are safely preserved
                </span>
              </div>
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                  <IoCheckmarkCircle className="w-3 h-3 text-green-600" />
                </div>
                <span className="text-gray-700 text-sm font-medium">
                  You can reactivate anytime by logging back in
                </span>
              </div>
            </div>
          </div>

          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
            <div className="flex gap-3">
              <IoInformationCircle className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
              <div>
                <Typography className="text-amber-800 text-sm font-medium">
                  Need a break?
                </Typography>
                <Typography className="text-amber-700 text-sm mt-1">
                  Deactivation is perfect for temporary breaks. Your account
                  will be waiting when you're ready to return.
                </Typography>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <DialogFooter className="px-6 py-4 bg-gray-50 gap-3">
          <Button variant="outline" onClick={onClose} className="px-6">
            Cancel
          </Button>
          <Button
            onClick={onConfirm}
            className="bg-amber-500 hover:bg-amber-600 text-white px-6"
          >
            Deactivate Account
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

const DeleteModal = ({
  isOpen,
  onClose,
  onConfirm,
}: {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[520px] p-0 overflow-hidden">
        {/* Header with gradient background */}
        <div className="bg-gradient-to-r from-red-50 to-pink-50 px-6 py-6 border-b border-red-100">
          <DialogHeader className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                <IoWarning className="w-6 h-6 text-red-600" />
              </div>
              <div>
                <DialogTitle className="text-xl font-semibold text-red-900 text-left">
                  Delete Account Permanently
                </DialogTitle>
                <DialogDescription className="text-red-700 text-left">
                  This action cannot be undone
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
        </div>

        {/* Content */}
        <div className="px-6 py-6 space-y-6">
          {/* Main warning */}
          <div className="bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg">
            <div className="flex gap-3">
              <IoCloseCircle className="w-6 h-6 text-red-500 flex-shrink-0 mt-0.5" />
              <div>
                <Typography className="text-red-800 font-semibold text-sm">
                  Permanent Account Deletion
                </Typography>
                <Typography className="text-red-700 text-sm mt-1">
                  Once deleted, your account and all associated data will be
                  permanently removed from our servers.
                </Typography>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <Typography className="text-gray-700 font-medium">
              The following will be permanently deleted:
            </Typography>

            <div className="grid gap-3">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-5 h-5 bg-red-100 rounded-full flex items-center justify-center mt-0.5">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                </div>
                <span className="text-gray-700 text-sm">
                  Your complete profile and personal information
                </span>
              </div>
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-5 h-5 bg-red-100 rounded-full flex items-center justify-center mt-0.5">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                </div>
                <span className="text-gray-700 text-sm">
                  All photos, videos, and media uploads
                </span>
              </div>
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-5 h-5 bg-red-100 rounded-full flex items-center justify-center mt-0.5">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                </div>
                <span className="text-gray-700 text-sm">
                  Conversation history and messages
                </span>
              </div>
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-5 h-5 bg-red-100 rounded-full flex items-center justify-center mt-0.5">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                </div>
                <span className="text-gray-700 text-sm">
                  Match history and dating preferences
                </span>
              </div>
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-5 h-5 bg-red-100 rounded-full flex items-center justify-center mt-0.5">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                </div>
                <span className="text-gray-700 text-sm">
                  Account settings and configurations
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <DialogFooter className="px-6 py-4 bg-gray-50 gap-3">
          <Button variant="outline" onClick={onClose} className="px-6">
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={onConfirm}
            className="px-6 bg-red-600 hover:bg-red-700"
          >
            Yes, Delete Permanently
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default function AccountPresence() {
  const [showDeactivateModal, setShowDeactivateModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const handleDeactivateAccount = () => {
    setShowDeactivateModal(true);
  };

  const handleDeleteAccount = () => {
    setShowDeleteModal(true);
  };

  const confirmDeactivate = () => {
    // TODO: Implement deactivate account functionality
    console.log('Account deactivated');
    setShowDeactivateModal(false);
  };

  const confirmDelete = () => {
    // TODO: Implement delete account functionality
    console.log('Account deleted');
    setShowDeleteModal(false);
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Typography variant="h3" className="text-2xl font-bold">
          Account presence
        </Typography>
        <Typography variant="body" className="text-gray-600">
          Manage your account visibility and data. These actions will affect
          your profile presence on the platform.
        </Typography>
      </div>

      <div className="space-y-6">
        <div className="space-y-4">
          <ActionCard
            title="Deactivate Account"
            description="Temporarily disable your account. Your profile will be hidden from other users, but your data will be preserved. You can reactivate your account at any time."
            buttonText="Deactivate Account"
            buttonVariant="outline"
            onAction={handleDeactivateAccount}
          />

          <ActionCard
            title="Delete Account"
            description="Permanently delete your account and all associated data. This action cannot be undone. All your messages, photos, and profile information will be permanently removed."
            buttonText="Delete Account"
            buttonVariant="destructive"
            onAction={handleDeleteAccount}
          />
        </div>

        {/* Professional Modals */}
        <DeactivateModal
          isOpen={showDeactivateModal}
          onClose={() => setShowDeactivateModal(false)}
          onConfirm={confirmDeactivate}
        />

        <DeleteModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={confirmDelete}
        />
      </div>
    </div>
  );
}
