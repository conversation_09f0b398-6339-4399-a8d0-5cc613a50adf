import { type Message } from '@prisma/client';
import { type OnlineStatusWithTime } from '#shared/types/profile';

export type PhotoWithUrls = {
  id: string;
  url?: string;
  thumbnailUrl?: string;
  mediumUrl?: string;
  [key: string]: any;
};

export type EnrichedOtherProfile =
  | {
      id: string;
      username: string;
      firstName: string;
      likesThisProfile: boolean;
      onlineStatus: OnlineStatusWithTime;
      mainPhoto?: PhotoWithUrls;
      hasVerifiedPhoto: boolean;
      emailIsVerified: boolean;
      appearance?: {
        height: number | null;
        weight: number | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        profileId: string;
        hairColor: string | null;
        eyeColor: string | null;
        bodyType: string | null;
        ethnicity: string[];
      } | null;
      age: number | null;
      bio?: {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        profileId: string;
        tagline: string | null;
        aboutMe: string | null;
        occupation: string | null;
        content?: string;
      } | null;
      location?: {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        profileId: string;
        latitude: number;
        longitude: number;
        cityOrTown: string;
        regionCode: string;
        countryIso2: string;
        openToRelocation: boolean | null;
        city?: string;
        state?: string;
        country?: string;
      } | null;
      datingPreference?: any;
      lifestyle?: any;
      allPhotos: any[];
      allVideos: any[];
    }
  | undefined;

export interface EnrichedConversation {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  subject: string | null;
  initiatorProfileId: string;
  receiverProfileId: string;
  lastMessage: Message;
  otherProfile: EnrichedOtherProfile;
}

export interface SocketMessagePayload {
  userId: string;
  conversationId: string;
  message: string | null;
  attachment: string | null;
  messageId: string | null;
  attachmentId: string | null;
}
