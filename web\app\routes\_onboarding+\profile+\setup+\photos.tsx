import { $Enums } from '@prisma/client';
import {
  useFetcher,
  useFetchers,
  useLoaderD<PERSON>,
  useNavigate,
} from '@remix-run/react';
import { useTranslation } from 'react-i18next';
import { FaArrowLeftLong } from 'react-icons/fa6';
import { z } from 'zod';
import {
  FormFromConfig,
  FormType,
} from '#app/components/forms/form-from-config.js';
import { Button } from '#app/components/ui/button.js';
import { LayoutType } from '#app/types/layouts.js';
import { cn } from '#app/utils/misc.js';
import {
  handleFormActionForProfile,
  handleLoaderForProfileRoute,
} from '#app/utils/remix-helpers.server.js';
import { prisma } from '#server/utils/db.server.js';
import { BadRequestError } from '#server/utils/errors.server.js';
import {
  deletePhotoFromCloudandDB,
  transformImageAndUploadToR2AndDatabase,
} from '#server/utils/image.server.js';
import {
  attachImageUrlsToPhoto,
  getImageDimensions,
} from '#server/utils/media.server.js';
import { PhotoType } from '#shared/constants/enums.js';
import { MIN_PHOTO_DIMENSION } from '#shared/constants/media.js';
import { PROFILE_SETUP_COMPLETE_ROUTE } from '#shared/constants/routes.js';
import { type FieldsFromConfig } from '#shared/types/forms.js';
import { type TranslationFunction } from '#shared/types/translations.js';
import { createPhotoValidator } from '#shared/utils/validators.js';

const nextRoute = PROFILE_SETUP_COMPLETE_ROUTE;

export const handle = { layout: LayoutType.NONE };

export const loader = handleLoaderForProfileRoute(async ({ profile }) => {
  const parentPhotos = await prisma.parentPhoto.findMany({
    where: {
      profileId: profile.id,
      photoType: {
        in: [$Enums.PhotoType.GALLERY, $Enums.PhotoType.MAIN],
      },
    },
    include: {
      photoVariants: true,
    },
  });

  const mainPhoto = parentPhotos.find(
    (parentPhoto) => parentPhoto.photoType === $Enums.PhotoType.MAIN,
  );

  const galleryPhotos = parentPhotos.filter(
    (parentPhoto) => parentPhoto.photoType === $Enums.PhotoType.GALLERY,
  );

  return {
    mainPhoto:
      mainPhoto !== undefined ? attachImageUrlsToPhoto(mainPhoto) : null,
    galleryPhotos: galleryPhotos.map(attachImageUrlsToPhoto),
  };
});

const createPhotoValidators = (t: TranslationFunction) =>
  z.object({
    photo: createPhotoValidator(t),
    photoGallery1: createPhotoValidator(t),
    photoGallery2: createPhotoValidator(t),
    photoGallery3: createPhotoValidator(t),
    photoGallery4: createPhotoValidator(t),
    photoGallery5: createPhotoValidator(t),
    photoGallery6: createPhotoValidator(t),
    photoGallery7: createPhotoValidator(t),
    photoGallery8: createPhotoValidator(t),
  });

export const action = handleFormActionForProfile(
  createPhotoValidators,
  async ({ redirect, data, request, profile }) => {
    const {
      photo,
      photoGallery1,
      photoGallery2,
      photoGallery3,
      photoGallery4,
      photoGallery5,
      photoGallery6,
      photoGallery7,
      photoGallery8,
    } = data;

    const photoGallery = [
      photoGallery1,
      photoGallery2,
      photoGallery3,
      photoGallery4,
      photoGallery5,
      photoGallery6,
      photoGallery7,
      photoGallery8,
    ];

    if (request.method === 'POST') {
      await Promise.all([
        new Promise<void>(async (resolve, reject) => {
          const existingPhoto = await prisma.parentPhoto.findFirst({
            where: {
              profileId: profile.id,
              photoType: PhotoType.MAIN,
            },
          });

          // If photo is already uploaded, skip upload.
          // Possible if the user goes back to this step after uploading.
          if (existingPhoto) {
            resolve();
            return;
          }

          const arrayBuffer: ArrayBuffer = await photo.arrayBuffer();
          if (arrayBuffer.byteLength === 0) {
            reject(`No main photo was uploaded`);
            return;
          }
          const buffer = Buffer.from(arrayBuffer);
          const { width, height } = await getImageDimensions(buffer);

          if (width < MIN_PHOTO_DIMENSION || height < MIN_PHOTO_DIMENSION) {
            reject(
              `Photo must be at least ${MIN_PHOTO_DIMENSION}px wide and ${MIN_PHOTO_DIMENSION}px high`,
            );
            return;
          }

          await transformImageAndUploadToR2AndDatabase({
            file: photo,
            photoType: $Enums.PhotoType.MAIN,
            profileId: profile.id,
          });
          resolve();
          return;
        }),
        ...photoGallery.map((photo) => {
          return new Promise<void>(async (resolve, reject) => {
            if (!photo) {
              resolve();
              return;
            }
            const arrayBuffer: ArrayBuffer = await photo.arrayBuffer();
            if (arrayBuffer.byteLength === 0) {
              resolve();
              return;
            }
            const buffer = Buffer.from(arrayBuffer);
            const { width, height } = await getImageDimensions(buffer);

            if (width < MIN_PHOTO_DIMENSION || height < MIN_PHOTO_DIMENSION) {
              reject(
                `Photo must be at least ${MIN_PHOTO_DIMENSION}px wide and ${MIN_PHOTO_DIMENSION}px high`,
              );
              return;
            }

            await transformImageAndUploadToR2AndDatabase({
              file: photo,
              photoType: $Enums.PhotoType.GALLERY,
              profileId: profile.id,
            });
            resolve();
            return;
          });
        }),
      ]).catch((reason) => {
        throw new BadRequestError(reason);
      });
    }

    if (request.method === 'DELETE') {
      const allPhotos = [photo, ...photoGallery];

      const targetPhotoId = await allPhotos.find((photo) => {
        return photo !== undefined;
      });

      await deletePhotoFromCloudandDB({
        parentPhotoId: targetPhotoId,
      });
      return null;
    }

    return redirect(nextRoute);
  },
);

export default function SetupPhotosRoute() {
  const { t } = useTranslation();
  const { mainPhoto, galleryPhotos: initialGalleryPhotos } =
    useLoaderData<typeof loader>();
  const fetcher = useFetcher();
  const fetchers = useFetchers();

  const isFetching =
    fetchers.length !== 0 &&
    fetchers.some((fetcher) => fetcher.state !== 'idle');

  const galleryFields = Array.from({ length: 8 }).map((_, index) => {
    return {
      name: `photoGallery${index + 1}`,
      type: 'photo-upload',
      defaultImageUrl: initialGalleryPhotos[index]?.mediumUrl,
      hidePreview: false,
      imageSize: 'medium',
      onDelete: () => {
        const galleryPhoto = initialGalleryPhotos[index];
        if (!galleryPhoto?.id) return;
        const formData = new FormData();
        formData.append(`photoGallery${index + 1}`, galleryPhoto?.id);
        fetcher.submit(formData, {
          method: 'DELETE',
          action: '/profile/setup/photos',
        });
      },
      showLoadingSpinner:
        fetchers.length !== 0 &&
        fetchers.some((fetcher) => {
          const get = fetcher.formData?.get(`photoGallery${index + 1}`) as
            | File
            | undefined;
          return get && get.name !== '';
        }),
      disableDelete: isFetching,
    };
  }) as FieldsFromConfig<typeof createPhotoValidators>;

  const fields: FieldsFromConfig<typeof createPhotoValidators> = [
    {
      name: 'photo',
      type: 'photo-upload',
      // triggerFormSubmitOnChange: true,
      defaultImageUrl: mainPhoto?.largeUrl ?? undefined,
      hidePreview: false,
      imageSize: 'large',
      onDelete: () => {
        if (!mainPhoto?.id) return;
        const formData = new FormData();
        formData.append('photo', mainPhoto?.id);
        fetcher.submit(formData, {
          method: 'DELETE',
          action: '/profile/setup/photos',
        });
      },
      showLoadingSpinner:
        fetchers.length !== 0 &&
        fetchers.some((fetcher) => {
          const get = fetcher.formData?.get('photo') as File | undefined;
          return get && get.name !== '';
        }),
      disableDelete: isFetching,
    },
    ...galleryFields,
  ];

  function handleClickBack() {
    if (window.history.length > 1) {
      window.history.back();
    }
  }

  const navigate = useNavigate();
  function handleClickSkip() {
    navigate(nextRoute);
  }

  return (
    <>
      <div className="mb-6 md:mb-8">
        <h1
          className={cn(
            'text-3xl md:text-4xl font-extrabold text-gray-900',
            'dark:text-white md:tracking-tight',
          )}
        >
          {t("Don't miss a connection")}
        </h1>
        <p className="mt-1 text-sm text-gray-500">
          {t('Add your mobile number for SMS alerts on new messages')}
        </p>
      </div>
      <FormFromConfig
        fields={fields}
        method="POST"
        action="/profile/setup/photos"
        encType="multipart/form-data"
        formType={FormType.FETCHER}
        renderFields={({ fields }) => {
          return (
            <div className="flex flex-col justify-between">
              <div className="flex justify-between">
                <div>
                  <label>Main Photo</label>
                  {fields.photo.node}
                </div>
                <div>
                  <label>Gallery Photos</label>
                  <div className="flex flex-wrap">
                    {fields.photoGallery1.node}
                    {fields.photoGallery2.node}
                    {fields.photoGallery3.node}
                    {fields.photoGallery4.node}
                    {fields.photoGallery5.node}
                    {fields.photoGallery6.node}
                    {fields.photoGallery7.node}
                    {fields.photoGallery8.node}
                  </div>
                </div>
              </div>
              <div className="flex justify-between mt-auto pt-8">
                <Button
                  onClick={handleClickBack}
                  className="text-black"
                  variant="link"
                  type="button"
                  disabled={isFetching}
                >
                  <FaArrowLeftLong className="mr-2" />
                  Back
                </Button>
                <button
                  type="submit"
                  className={cn(
                    'w-[20%] rounded-lg bg-[#D33544] py-3 text-center font-medium text-white',
                    'transition-colors hover:bg-[#B82C3A] focus:outline-none focus:ring-2',
                    'focus:ring-[#D33544] focus:ring-offset-2',
                  )}
                  disabled={isFetching}
                >
                  Next
                </button>
              </div>
              <div className="flex justify-end">
                <Button
                  onClick={handleClickSkip}
                  variant="link"
                  className="w-[20%]"
                  type="button"
                  disabled={isFetching}
                >
                  Skip for now
                </Button>
              </div>
            </div>
          );
        }}
        createValidator={createPhotoValidators}
      />
    </>
  );
}
