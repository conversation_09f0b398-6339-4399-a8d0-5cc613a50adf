import { type Location } from '@prisma/client';
import { useTranslation } from 'react-i18next';

function LocationWidget({
  location,
  country,
}: {
  location: Location;
  country: string;
}) {
  const { t } = useTranslation();
  return (
    <div>
      <p>
        <strong>{t('City/Town')}:</strong> {location.cityOrTown}
      </p>
      <p>
        <strong>{t('Region Code')}:</strong> {location.regionCode}
      </p>
      <p>
        <strong>{t('Country')}:</strong> {country}
      </p>
      <p>
        <strong>{t('Open to Relocation')}:</strong>{' '}
        {location.openToRelocation ? t('Yes') : t('No')}
      </p>
    </div>
  );
}

export { LocationWidget };
