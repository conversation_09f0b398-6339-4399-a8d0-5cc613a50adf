import type React from 'react';
import { useState, useCallback, useRef, useEffect } from 'react';
import { HiPhoto } from 'react-icons/hi2';
import { Button } from '#app/components/ui/button';

type TriggerType = 'default' | 'chat';

type AttachmentUploadProps = {
  onChange: (files: File[]) => void;
  value?: File[];
  name: string;
  multiple?: boolean;
  accept?: string;
  inputRef?: React.Ref<HTMLInputElement>;
  triggerType?: TriggerType;
};

export const AttachmentUpload = ({
  onChange,
  value = [],
  multiple = true,
  accept = 'image/*',
  name,
  inputRef,
  triggerType = 'default',
  ...props
}: AttachmentUploadProps) => {
  const [files, setFiles] = useState<File[]>(value);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const updateFormInputs = useCallback(
    (currentFiles: File[]) => {
      const form = fileInputRef.current?.closest('form');
      if (!form) return;

      const existingInputs = form.querySelectorAll(`input[name="${name}"]`);
      existingInputs.forEach((input) => input.remove());

      currentFiles.forEach((file) => {
        const input = document.createElement('input');
        input.type = 'file';
        input.name = name;
        input.style.display = 'none';

        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(file);
        input.files = dataTransfer.files;

        form.appendChild(input);
      });
    },
    [name],
  );

  const handleFileChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      try {
        const selectedFiles = Array.from(event.target.files || []);
        if (selectedFiles.length === 0) return;

        const updatedFiles = multiple
          ? [...files, ...selectedFiles]
          : selectedFiles;
        setFiles(updatedFiles);

        updateFormInputs(updatedFiles);
        onChange(updatedFiles);

        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      } catch (error) {
        console.error('Error handling file change:', error);
      }
    },
    [files, multiple, onChange, updateFormInputs],
  );

  const removeFile = useCallback(
    (index: number) => {
      setFiles((prevFiles) => {
        const newFiles = [...prevFiles];
        newFiles.splice(index, 1);

        updateFormInputs(newFiles);
        onChange(newFiles);
        return newFiles;
      });
    },
    [onChange, updateFormInputs],
  );

  useEffect(() => {
    updateFormInputs(files);
  }, [updateFormInputs, files]);

  useEffect(() => {
    if (value && JSON.stringify(value) !== JSON.stringify(files)) {
      setFiles(value);
    }
  }, [files, value]);

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Trigger
          type={triggerType}
          onClick={() => fileInputRef.current?.click()}
        />

        <input
          type="file"
          ref={fileInputRef}
          accept={accept}
          multiple={multiple}
          onChange={handleFileChange}
          className="hidden"
          {...props}
        />
      </div>

      {files.length > 0 && triggerType !== 'chat' && (
        <div className="grid grid-cols-2 gap-2 sm:grid-cols-3 md:grid-cols-4">
          {files.map((file, index) => (
            <div key={index} className="relative group">
              {file.type.startsWith('image/') ? (
                <div className="h-24 w-24 bg-gray-100 flex items-center justify-center rounded-md">
                  <span className="text-sm text-gray-500">Image</span>
                </div>
              ) : (
                <div className="h-24 w-24 flex items-center justify-center bg-gray-100 rounded-md">
                  <span className="text-sm text-gray-500">Video</span>
                </div>
              )}
              <Button
                type="button"
                variant="destructive"
                size="icon"
                onClick={() => removeFile(index)}
                className="absolute -top-2 -right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
              ></Button>
              <span className="text-xs mt-1 truncate block">{file.name}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const Trigger = ({
  type,
  onClick,
}: {
  type: TriggerType;
  onClick: () => void;
}) => {
  switch (type) {
    case 'chat':
      return (
        <Button
          onClick={onClick}
          size={'icon'}
          variant={'link'}
          className="p-0"
          type="button"
        >
          <HiPhoto className="text-2xl sm:text-3xl" />
        </Button>
      );
    default:
      return (
        <Button
          type="button"
          variant="outline"
          onClick={onClick}
          className="flex items-center gap-2"
        >
          <span>Attach Files</span>
        </Button>
      );
  }
};
