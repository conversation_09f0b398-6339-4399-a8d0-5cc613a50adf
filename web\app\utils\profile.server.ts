import { PhotoType } from '@prisma/client';
import {
  getProfileProgress,
  getProfileWithExtrasByProfileId,
} from '#server/utils/profile.server';

export async function getProfileProgressData(profileId: string) {
  const profile: Awaited<ReturnType<typeof getProfileWithExtrasByProfileId>> =
    await getProfileWithExtrasByProfileId(profileId);

  const { appearance, lifestyle, datingPreference, parentPhotos: allPhotos } = profile;
  const mainPhoto = allPhotos.find(
    (photo) => photo.photoType === PhotoType.MAIN,
  );
  const galleryPhotos = allPhotos.filter(
    (photo) => photo.photoType === PhotoType.GALLERY,
  );

  const profileProgress = getProfileProgress({
    mainPhoto,
    galleryPhotos,
    appearance,
    lifestyle,
    datingPreference,
  });

  return { profile, profileProgress };
}
