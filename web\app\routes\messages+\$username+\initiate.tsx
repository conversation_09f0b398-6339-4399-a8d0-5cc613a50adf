import { z } from 'zod';
import {
  createConversationAndMessage,
  getProfileByUsername,
} from '#app/utils/conversations.server';
import { handleFormActionForProfile } from '#app/utils/remix-helpers.server';
import { t } from '#server/utils/api.server';
import { BadRequestError } from '#server/utils/errors.server';
import { type TranslationFunction } from '#shared/types/translations';

function createNewConversationValidator(t: TranslationFunction) {
  return z.object({
    subject: z.string({ message: t('Enter subject here') }).optional(),
    message: z
      .string({ message: t('Message is required') })
      .min(1, t('Message cannot be empty')),
  });
}

export const action = handleFormActionForProfile(
  createNewConversationValidator,
  async ({ profile, data, successWithMessage, params }) => {
    const { username } = params; // target username
    if (!username) {
      throw new BadRequestError('Invalid username');
    }
    const { message } = data;
    const receiverProfile = await getProfileByUsername({
      username,
      viewerProfileId: profile.id,
    });

    if (receiverProfile.isBlocked) {
      throw new BadRequestError('Failed to send message.');
    }

    await createConversationAndMessage(profile, receiverProfile, message);

    return successWithMessage(t('Success'));
  },
);
