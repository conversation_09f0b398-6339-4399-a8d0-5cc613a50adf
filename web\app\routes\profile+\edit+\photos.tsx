import { $Enums } from '@prisma/client';
import { useFetcher, useFetchers, useLoaderData } from '@remix-run/react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import {
  FormFromConfig,
  FormType,
} from '#app/components/forms/form-from-config.js';
import { ProfileLayout } from '#app/components/profile/profile-layout';

import {
  handleFormActionForProfile,
  handleLoaderForProfileRoute,
} from '#app/utils/remix-helpers.server';
import { prisma } from '#server/utils/db.server.js';
import {
  deletePhotoFromCloudandDB,
  transformImageAndUploadToR2AndDatabase,
} from '#server/utils/image.server.js';
import { attachImageUrlsToPhoto } from '#server/utils/media.server';
import { PhotoType } from '#shared/constants/enums';
import { type FieldFromConfigType } from '#shared/types/forms.js';
import { type TranslationFunction } from '#shared/types/translations';
import { createPhotoValidator } from '#shared/utils/validators';

const createPhotoValidators = (t: TranslationFunction) =>
  z.object({
    photo: createPhotoValidator(t),
    photoType: z.nativeEnum(PhotoType),
    reference: z.string(),
  });

// ensure the user is logged in and has a profile
export const loader = handleLoaderForProfileRoute(async ({ profile }) => {
  let photos = await prisma.parentPhoto.findMany({
    where: {
      profileId: profile.id,
    },
    include: {
      photoVariants: true,
    },
  });

  // let mainPhoto = photos
  //   .filter((photo) => photo.photoType === PhotoType.MAIN)
  //   .map(attachImageUrlsToPhoto)[0];

  const mainPhoto = photos.find(
    (photo) => photo.photoType === $Enums.PhotoType.MAIN,
  );

  const galleryPhotos = photos
    .filter((photo) => photo.photoType === $Enums.PhotoType.GALLERY)
    .map(attachImageUrlsToPhoto);

  return {
    mainPhoto: mainPhoto ? attachImageUrlsToPhoto(mainPhoto) : undefined,
    galleryPhotos,
  };
});

export const action = handleFormActionForProfile(
  createPhotoValidators,
  async ({ successWithMessage, data, profile, request }) => {
    const { photo, photoType } = data;

    if (request.method === 'DELETE') {
      await deletePhotoFromCloudandDB({
        parentPhotoId: photo,
      });
    }

    if (request.method === 'POST') {
      await transformImageAndUploadToR2AndDatabase({
        file: photo,
        photoType: photoType,
        profileId: profile.id,
      });
    }

    return successWithMessage('Photo uploaded successfully');
  },
);

export default function EditPhotos() {
  // TODO: optimistic update when the photo is uploaded
  const { mainPhoto, galleryPhotos: initialGalleryPhotos } =
    useLoaderData<typeof loader>();
  const fetcher = useFetcher();
  const fetchers = useFetchers();

  const { t } = useTranslation();

  type Schema = z.infer<ReturnType<typeof createPhotoValidators>>;

  function photoFieldBuilder({
    photo,
    photoType,
    imageSize,
    key,
  }: {
    photo?: ReturnType<typeof attachImageUrlsToPhoto>;
    photoType: $Enums.PhotoType;
    imageSize: 'large' | 'medium' | 'small';
    key: string;
  }): FieldFromConfigType<Schema>[] {
    return [
      {
        name: 'photo',
        type: 'photo-upload',
        triggerFormSubmitOnChange: true,
        defaultImageUrl: photo?.largeUrl,
        hidePreview: false,
        imageSize: imageSize,
        onDelete: () => {
          if (!photo?.id) return;
          const formData = new FormData();
          formData.append('photo', photo.id);
          formData.append('photoType', photoType);
          formData.append('reference', key);
          fetcher.submit(formData, {
            method: 'DELETE',
            action: '/profile/edit/photos',
          });
        },
        showLoadingSpinner:
          fetchers.length !== 0 &&
          fetchers.some((fetcher) => {
            const getRef = fetcher.formData?.get('reference') as string;
            console.log(`${getRef} === ${key}`);
            return getRef === key;
          }),
      },
      {
        name: 'photoType',
        type: 'hidden',
        defaultValue: photoType,
      },
      // using this for showing spinner
      {
        name: 'reference',
        type: 'hidden',
        defaultValue: key,
      },
    ];
  }

  return (
    <ProfileLayout>
      <h2>{t('Main Photo')}</h2>
      <FormFromConfig
        fields={photoFieldBuilder({
          imageSize: 'large',
          photo: mainPhoto,
          photoType: PhotoType.MAIN,
          key: 'main',
        })}
        createValidator={createPhotoValidators}
        method="POST"
        action="/profile/edit/photos"
        encType="multipart/form-data"
        key={mainPhoto?.id}
        formType={FormType.FETCHER}
      />
      <h2 className="mb-[-20px] text-neutral-900">{t('Gallery Photos')}</h2>
      <p className="mb-0 text-neutral-400">
        {t('Share more about yourself with additional photos')}
      </p>
      <div className="flex gap-3">
        <div className="flex gap-3">
          {Array.from({ length: 2 }).map((_, index) => (
            <FormFromConfig
              fields={photoFieldBuilder({
                imageSize: 'large',
                photo: initialGalleryPhotos[index],
                photoType: PhotoType.GALLERY,
                key: index.toString(),
              })}
              createValidator={createPhotoValidators}
              method="POST"
              action="/profile/edit/photos"
              encType="multipart/form-data"
              key={initialGalleryPhotos[index]?.id ?? index}
              formType={FormType.FETCHER}
            />
          ))}
        </div>
        <div className="columns-2 gap-3">
          {Array.from({ length: 4 }).map((_, index) => {
            const targetIndex = index + 2;

            return (
              <FormFromConfig
                fields={photoFieldBuilder({
                  imageSize: 'medium',
                  photo: initialGalleryPhotos[targetIndex],
                  photoType: PhotoType.GALLERY,
                  key: targetIndex.toString(),
                })}
                createValidator={createPhotoValidators}
                method="POST"
                action="/profile/edit/photos"
                encType="multipart/form-data"
                key={initialGalleryPhotos[targetIndex]?.id ?? targetIndex}
                formType={FormType.FETCHER}
              />
            );
          })}
        </div>
      </div>
    </ProfileLayout>
  );
}
