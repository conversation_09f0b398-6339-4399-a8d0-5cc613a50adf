# fly.toml app configuration file generated for filipina-meet-app on 2024-08-05T21:44:55+08:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'filipina-meet-app'
primary_region = 'nrt'
kill_signal = 'SIGINT'
kill_timeout = '5s'
swap_size_mb = 512

[experimental]
  auto_rollback = true

[build]
  dockerfile = '/docker/web/Dockerfile'

[[mounts]]
  source = 'data'
  destination = '/data'

[[services]]
  protocol = 'tcp'
  internal_port = 8080
  processes = ['app']

  [[services.ports]]
    port = 80
    handlers = ['http']
    force_https = true

  [[services.ports]]
    port = 443
    handlers = ['tls', 'http']

  [services.concurrency]
    type = 'requests'
    hard_limit = 100
    soft_limit = 80

  [[services.tcp_checks]]
    interval = '15s'
    timeout = '2s'
    grace_period = '1s'

  [[services.http_checks]]
    interval = '10s'
    timeout = '2s'
    grace_period = '5s'
    method = 'get'
    path = '/resources/healthcheck'
    protocol = 'http'
    tls_skip_verify = false

[[vm]]
  size = 'shared-cpu-1x'
  memory = "1gb"
