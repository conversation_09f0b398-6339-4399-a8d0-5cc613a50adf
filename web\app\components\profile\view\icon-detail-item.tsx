import React from 'react';
import Typography from '#app/components/typography';
import { cn } from '#app/utils/misc';

function IconDetailItem({
  icon,
  label,
  containerClassName,
}: {
  label: string;
  icon: React.ReactNode;
  containerClassName?: string;
}) {
  return (
    <div className={cn('flex items-center gap-1.5', containerClassName)}>
      {icon}
      <Typography variant="small">{label}</Typography>
    </div>
  );
}

export { IconDetailItem };
