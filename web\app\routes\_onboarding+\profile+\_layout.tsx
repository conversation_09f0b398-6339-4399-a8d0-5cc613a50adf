import { Outlet, useLocation, useMatches } from "@remix-run/react";
import { useMemo } from "react";
import { StepBuilder } from "#app/components/stepper/stepbuilder.js";
import gradientMeshUrl from '#app/images/gradient-mesh.webp';
import MainLogoSVG from '#app/images/svg-icons/logos/main.svg?react';
import WhiteLogoSVG from '#app/images/svg-icons/logos/white.svg?react';
import { cn } from '#app/utils/misc.js';
import { 
  PROFILE_NEW_ROUTE, 
  PROFILE_SETUP_BIO_ROUTE, 
  PROFILE_SETUP_PHOTOS_ROUTE,
  PROFILE_SETUP_COMPLETE_ROUTE,
  PROFILE_SETUP_VERIFICATION_ROUTE, 
} from "#shared/constants/routes.js";

export default function ProfileSetupLayout() {
  const location = useLocation();
  const matches = useMatches();
  const currentRoute = matches[matches.length - 1];

  const stepRoutes = useMemo(() => [
    PROFILE_NEW_ROUTE,
    PROFILE_SETUP_BIO_ROUTE,
    PROFILE_SETUP_VERIFICATION_ROUTE,
    PROFILE_SETUP_PHOTOS_ROUTE,
  ], []);

  if (currentRoute?.pathname === PROFILE_SETUP_COMPLETE_ROUTE) {
    return ( 
      <div className="flex flex-col md:col-span-2 p-5 md:p-8 h-screen justify-center m-auto w-[65%]">
        <MainLogoSVG className="tablet:h-9 absolute top-10 left-10" />
        <Outlet />
      </div>
    )
  }

  return (
    <div className="min-h-screen md:flex md:items-center md:justify-center">
      <div className="mx-auto w-full">
        <div className="dark:bg-gray-800 md:rounded-xl md:bg-white md:shadow-xl">
          <div className="flex md:grid md:grid-cols-3 md:min-h-[600px]">
            {/* LEFT CONTENT */}
            <div className="flex flex-col md:col-span-2 p-5 md:p-8 h-screen justify-center m-auto w-[70%]">
              <MainLogoSVG className="tablet:h-9 absolute top-10 left-10" />
              <StepBuilder 
                steps={stepRoutes.length} 
                currentStep={stepRoutes.indexOf(location.pathname)} 
              />
              <div className='mt-10'></div>
              <Outlet />
            </div>

            {/* RIGHT CONTENT */}
            <div className="relative h-52 md:h-auto">
              <img
                src={gradientMeshUrl}
                alt="Gradient Background"
                className="h-full w-full object-cover md:rounded-l-xl"
              />
              <div
                className={cn(
                  'absolute left-1/2 top-1/4 md:top-1/3 flex -translate-x-1/2 -translate-y-1/2',
                  'flex-col items-center gap-3',
                )}
              >
                <WhiteLogoSVG className="h-14 w-14 md:h-20 md:w-20" />
                <h1 className="text-center font-serif text-3xl italic text-white md:text-4xl">
                  filipinameet
                </h1>
              </div>

              <div className="absolute bottom-6 left-1/2 -translate-x-1/2 text-center md:bottom-8">
                <p className="text-sm text-white/80 md:text-base">
                  Connect with singles from around the world!
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}