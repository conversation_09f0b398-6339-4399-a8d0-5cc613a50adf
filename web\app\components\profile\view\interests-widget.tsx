import { useTranslation } from 'react-i18next';
import { IconDetailItem } from '#app/components/profile/view';
import Typography from '#app/components/typography';
import { InterestType, InterestWithIcons } from '#shared/types/interests.js';

type Prop = {
  interests?: InterestType[];
};

const InterestMap = InterestWithIcons.reduce(
  (acc, interest) => {
    acc[interest.value] = (
      <IconDetailItem
        key={interest.value}
        containerClassName="bg-red-200 px-3 py-2 rounded-xl"
        label={interest.label}
        icon={interest.icon}
      />
    );
    return acc;
  },
  {} as Record<InterestType, React.ReactNode>,
);

function InterestsWidget({ interests }: Prop) {
  const { t } = useTranslation();

  const userInterests = interests?.length
    ? interests
    : [InterestType.Hiking, InterestType.Cooking];

  return (
    <div>
      <Typography variant="h5" className="font-medium mb-2 !leading-none">
        {t('Interests')}
      </Typography>

      <div className=" px-3 py-3 flex flex-wrap gap-2 rounded-xl">
        {userInterests.map((interest) => InterestMap[interest])}
      </div>
    </div>
  );
}

export { InterestsWidget };
