# Base node image
FROM node:22-bookworm-slim as base

# Set environment variables
ENV NODE_ENV production

# Install openssl for Prisma
RUN apt-get update && apt-get install -y openssl ca-certificates

# Install production dependencies
FROM base as deps

# Set working directory to the root of the monorepo
WORKDIR /myapp

# Add node_modules to the PATH
ENV PATH /myapp/web/node_modules/.bin:$PATH

# Copy root and 'web' package.json and lock files
COPY package.json package-lock.json ./
COPY web/package.json web/package.json

# Remove 'prepare' script from package.json to avoid running <PERSON>sky in production
RUN sed -i '/"prepare":/d' web/package.json
RUN sed -i '/"prepare":/d' package.json

# Install root and 'web' production dependencies
RUN npm ci --include=dev --legacy-peer-deps

# Build the app
FROM base as build

ARG COMMIT_SHA
ENV COMMIT_SHA=$COMMIT_SHA

# Set working directory to the root of the monorepo
WORKDIR /myapp

# Copy production dependencies
COPY --from=deps /myapp/node_modules /myapp/node_modules

# Copy the rest of your application code
COPY . .

# Remove 'prepare' script from package.json to avoid running <PERSON>sky in production
# need to do this again because we copied ove the files again
RUN sed -i '/"prepare":/d' web/package.json
RUN sed -i '/"prepare":/d' package.json

# Add node_modules to the PATH
ENV PATH /myapp/node_modules/.bin:$PATH


# Generate Prisma client
RUN npm run prisma:generate 

# Add these lines to fix the Prisma browser client issue
RUN mkdir -p /myapp/web/node_modules/.prisma/client
RUN echo "// Empty file created as build workaround" > /myapp/web/node_modules/.prisma/client/index-browser.js

# Run Prisma migrations after generating Prisma client
# RUN npm run prisma:migrate:deploy

# TODO: figure out a way we don't need this
RUN npm install -g npm-run-all tsx

# use a different tsconfig for the web build
RUN mv tsconfig.web.json tsconfig.json

# Delete the server-build directory before the build step
RUN rm -rf /myapp/web/server-build

# Build the application
RUN npm run build:web

# Remove any temporary dependencies installed for build
RUN npm prune --production && npm uninstall npm-run-all tsx

# Final production image
FROM base

ENV FLY="true"
ENV INTERNAL_PORT="8080"
ENV PORT="8080"
ENV NODE_ENV="production"


# Copy node_modules and build artifacts from the build stage
COPY --from=build /myapp/node_modules /myapp/node_modules
COPY --from=build /myapp/web/build /myapp/web/build
COPY --from=build /myapp/web/server-build /myapp/web/server-build
COPY --from=build /myapp/web/package.json /myapp/web/package.json
COPY --from=build /myapp/package.json /myapp/package.json 

# Copy prisma directory and migrations
COPY --from=build /myapp/web/prisma /myapp/web/prisma
COPY --from=build /myapp/web/prisma/migrations /myapp/web/prisma/migrations

# Check the copied files (optional for debugging)
RUN ls -al /myapp
RUN ls -al /myapp/web

WORKDIR /myapp

# Start the application and dynamically generate the INTERNAL_COMMAND_TOKEN at runtime
CMD ["sh", "-c", "export INTERNAL_COMMAND_TOKEN=$(openssl rand -hex 32) && npm run start:web"]