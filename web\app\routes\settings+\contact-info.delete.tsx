import { handleActionForLoggedInUser } from '#app/utils/remix-helpers.server';
import { t } from '#server/utils/api.server';
import { prisma } from '#server/utils/db.server';

export const action = handleActionForLoggedInUser(
  async ({ successWithMessage, privateUser }) => {
    await prisma.privateUser.update({
      data: { phoneNumber: null },
      where: { id: privateUser.id },
    });
    return successWithMessage(t('Phone number removed successfully'));
  },
);
