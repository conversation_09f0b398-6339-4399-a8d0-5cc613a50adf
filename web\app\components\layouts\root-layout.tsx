import { Outlet, useMatches } from '@remix-run/react';
import BottomNavigation from '#app/components/bottom-nav';
import Footer from '#app/components/footer';
import { Header } from '#app/components/header';
import ChatLayout from '#app/components/layouts/chat-layout';
import { type CommonLayoutProps, LayoutType } from '#app/types/layouts';

interface RouteHandler {
  layout?: LayoutType;
  shouldShowFooter?: boolean;
}

const RootLayout = ({ user, profile }: CommonLayoutProps) => {
  const matches = useMatches();

  const currentRoute = matches[matches.length - 1];
  const routeHandler = currentRoute?.handle as RouteHandler;

  const layoutType: LayoutType = routeHandler?.layout || LayoutType.ROOT;
  const shouldShowFooter = !!routeHandler?.shouldShowFooter;

  return (
    <>
      {layoutType === LayoutType.ROOT && (
        <DefaultLayout
          user={user}
          profile={profile}
          shouldShowFooter={shouldShowFooter}
        />
      )}

      {layoutType === LayoutType.CHAT && (
        <ChatLayout
          user={user}
          profile={profile}
          shouldShowFooter={shouldShowFooter}
        />
      )}

      {layoutType === LayoutType.NONE && (
        <Outlet />
      )}
    </>
  );
};

const DefaultLayout = ({
  user,
  profile,
  shouldShowFooter,
}: CommonLayoutProps) => {
  return (
    <div className="flex h-[100dvh] flex-col justify-between">
      {user && profile ? <Header user={user} profile={profile} /> : null}
      <div className="flex-1 tablet-min:pb-0 pb-14">
        <Outlet />
      </div>
      {user && profile && <BottomNavigation />}
      {shouldShowFooter && <Footer />}
    </div>
  );
};

export default RootLayout;
