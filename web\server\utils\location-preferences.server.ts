import { type Prisma, type SpecialLocationSearchPreference, type LocationType } from '@prisma/client';
import { prisma } from '#server/utils/db.server';

// Define specific country lists for special preferences
const COUNTRY_MAPPING = {
  WESTERN_COUNTRIES: [
    'US', 'CA', 'GB', 'AU', 'NZ', 'DE', 'FR', 'IT', 'ES', 'NL', 'BE', 'AT', 'CH', 
    'SE', 'NO', 'DK', 'FI', 'IE', 'PT', 'GR', 'PL', 'CZ', 'HU', 'SK', 'SI', 'HR', 
    'BG', 'RO', 'LT', 'LV', 'EE', 'LU', 'MT', 'CY', 'IS', 'LI', 'MC', 'SM', 'VA',
    'AD', 'FO', 'GI', 'JE', 'GG', 'IM', 'AX'
  ],
  ASIAN_COUNTRIES: [
    'JP', 'KR', 'CN', 'TH', 'SG', 'MY', 'ID', 'PH', 'VN', 'IN', 'BD', 'LK',
    'NP', 'MM', 'KH', 'LA', 'MN', 'TW', 'HK', 'MO', 'BN', 'TL', 'MV', 'BT',
    'AF', 'AM', 'AZ', 'BH', 'GE', 'IL', 'JO', 'KZ', 'KG', 'KW', 'LB', 'OM',
    'PK', 'QA', 'SA', 'SY', 'TJ', 'TM', 'AE', 'UZ', 'YE', 'IR', 'IQ'
  ]
} as const;

/**
 * Get countries by special preference type
 */
function getCountriesByPreference(preference: 'WESTERN_COUNTRIES' | 'ASIAN_COUNTRIES'): readonly string[] {
  return COUNTRY_MAPPING[preference];
}

export type LocationPreferenceResult = {
  hasPreferences: boolean;
  specialPreferences: SpecialLocationSearchPreference[];
  locationSelections: Array<{
    locationType: LocationType;
    countryIso2: string;
    regionCode?: string;
    cityOrTown?: string;
  }>;
};

/**
 * Get location preferences for a profile
 * Returns null if no preferences exist (worldwide state)
 */
export async function getLocationPreferences(profileId: string): Promise<LocationPreferenceResult | null> {
  const locationPreference = await prisma.locationPreference.findUnique({
    where: { profileId },
    include: {
      locationSelections: true,
    },
  });

  if (!locationPreference) {
    return null; // Worldwide state
  }

  return {
    hasPreferences: true,
    specialPreferences: locationPreference.specialLocationSearchPreferences,
    locationSelections: locationPreference.locationSelections.map(selection => ({
      locationType: selection.locationType,
      countryIso2: selection.countryIso2,
      regionCode: selection.regionCode || undefined,
      cityOrTown: selection.cityOrTown || undefined,
    })),
  };
}

/**
 * Build location filter for search queries
 * Returns null if no filter should be applied (worldwide)
 */
export function buildLocationFilter(preferences: LocationPreferenceResult | null): Prisma.ProfileWhereInput | null {
  if (!preferences || !preferences.hasPreferences) {
    return null; // Worldwide - no filter
  }

  const { specialPreferences, locationSelections } = preferences;
  const locationFilters: Prisma.ProfileWhereInput[] = [];

  // Handle special preferences
  specialPreferences.forEach(preference => {
    if (preference === 'WESTERN_COUNTRIES' || preference === 'ASIAN_COUNTRIES') {
      const countries = getCountriesByPreference(preference);
      locationFilters.push({
        location: {
          countryIso2: { in: [...countries] },
        },
      });
    } else {
      console.warn(`Unknown SpecialLocationSearchPreference: ${preference}`);
    }
  });

  // Handle specific location selections
  type FilterAccumulator = {
    countryFilters: Prisma.ProfileWhereInput[];
    regionFilters: Prisma.ProfileWhereInput[];
    cityFilters: Prisma.ProfileWhereInput[];
  };

  const filters = locationSelections.reduce((acc: FilterAccumulator, selection) => {
    const baseFilter = { location: { countryIso2: selection.countryIso2 } };
    
    switch (selection.locationType) {
      case 'COUNTRY':
        acc.countryFilters.push(baseFilter);
        break;
      case 'REGION':
        acc.regionFilters.push({ ...baseFilter, location: { ...baseFilter.location, regionCode: selection.regionCode } });
        break;
      case 'CITY_OR_TOWN':
        acc.cityFilters.push({ ...baseFilter, location: { ...baseFilter.location, regionCode: selection.regionCode, cityOrTown: selection.cityOrTown } });
        break;
    }
    return acc;
  }, { countryFilters: [], regionFilters: [], cityFilters: [] } as FilterAccumulator);

  if (filters.countryFilters.length > 0 || filters.regionFilters.length > 0 || filters.cityFilters.length > 0) {
    locationFilters.push({
      OR: [...filters.countryFilters, ...filters.regionFilters, ...filters.cityFilters],
    });
  }

  // If no filters were created, return null (worldwide)
  if (locationFilters.length === 0) {
    return null;
  }

  // Combine filters with OR logic
  return {
    OR: locationFilters,
  };
} 