{"private": true, "type": "module", "workspaces": ["web", "mobile"], "scripts": {"dev:web": "npm --prefix ./web run dev", "dev:mobile": "npm --prefix ./mobile run start", "build:web": "npm --prefix ./web run build", "build:mobile": "npm --prefix ./mobile run build", "format": "npm --prefix ./web run format", "lint:web": "npm --prefix ./web run lint", "lint:web:fix": "npm --prefix ./web run lint:fix", "lint": "eslint", "test": "npm run test:web", "test:web": "npm --prefix ./web run test", "test:web:ci": "npm --prefix ./web run test:ci", "test:mobile": "npm --prefix ./mobile run test", "setup": "npm --prefix ./web run setup", "start:web": "npm --prefix ./web run start", "start:mobile": "npm --prefix ./mobile run start", "prepare": "husky", "build:icons": "npm --prefix ./web run build:icons", "build:remix": "npm --prefix ./web run build:remix", "build:server": "npm --prefix ./web run build:server", "predev": "npm --prefix ./web run predev", "prisma:migrate:dev": "npm --prefix ./web run prisma:migrate:dev", "prisma:generate": "npm --prefix ./web run prisma:generate", "prisma:migrate:deploy": "npm --prefix ./web run prisma:migrate:deploy", "prisma:migrate:reset": "npm --prefix ./web run prisma:migrate:reset", "prisma:studio": "npm --prefix ./web run prisma:studio", "coverage": "npm --prefix ./web run coverage", "test:e2e": "npm --prefix ./web run test:e2e", "test:e2e:dev": "npm --prefix ./web run test:e2e:dev", "pretest:e2e:run": "npm --prefix ./web run pretest:e2e:run", "test:e2e:run": "npm --prefix ./web run test:e2e:run", "test:e2e:install": "npm --prefix ./web run test:e2e:install", "typecheck:web": "npm --prefix ./web run typecheck", "typecheck": "tsc -b", "validate": "npm --prefix ./web run validate", "seed": "npm --prefix ./web run seed", "db:reset": "npm run prisma:migrate:reset && npm run prisma:migrate:dev && npm run seed"}, "dependencies": {"@aws-sdk/client-s3": "^3.741.0", "@fastify/multipart": "^9.0.1", "@fastify/websocket": "^11.0.2", "@hookform/resolvers": "^3.9.1", "@prisma/client": "^6.11.1", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.1", "@react-navigation/native": "^7.0.3", "@react-navigation/native-stack": "^7.0.3", "@react-navigation/stack": "^7.0.3", "@remix-run/node": "^2.14.0", "@sentry/node": "^9.38.0", "@sentry/profiling-node": "^9.38.0", "@shadcn/ui": "^0.0.4", "ajv": "^8.17.1", "ajv-keywords": "^5.1.0", "commonjs-extension-resolution-loader": "^0.1.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.5.2", "intl-pluralrules": "^2.0.1", "jsonwebtoken": "^9.0.2", "libphonenumber-js": "^1.11.14", "lodash": "^4.17.21", "prisma": "^6.11.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.2", "react-i18next": "^15.1.1", "react-native-paper": "^5.12.5", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.4.9", "typescript": "~5.6.3", "zod": "^3.23.8"}, "devDependencies": {"@epic-web/config": "^1.16.1", "@sentry/vite-plugin": "^2.22.6", "@types/glob": "^8.1.0", "@types/lodash": "^4.17.13", "@types/node": "^22.9.0", "@types/react": "~18.3.12", "@types/react-dom": "~18.3.1", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "dotenv-cli": "^7.4.2", "eslint": "^9.15.0", "glob": "^11.0.0", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^16.1.2", "npm-run-all": "^4.1.5", "prettier": "^3.3.3", "react-test-renderer": "18.3.1", "tsc-alias": "^1.8.10", "vite-plugin-svgr": "^4.3.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "npm run lint:web:fix"], "*.{json,md,css,html}": ["prettier --write"]}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.27.2"}, "prisma": {"schema": "./prisma/schema.prisma"}, "engines": {"node": "22"}, "volta": {"node": "22.7.0"}}