import { Link } from '@remix-run/react';
import AutoPlay from 'embla-carousel-autoplay';
import { useTranslation } from 'react-i18next';
import { LinkButton } from '#app/components/buttons/link-button';
import { FormFromConfig } from '#app/components/forms/form-from-config';
import Typography from '#app/components/typography/index.js';
import { Button } from '#app/components/ui/button.js';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from '#app/components/ui/carousel.js';
import { Separator } from '#app/components/ui/separator.js';
import Background from '#app/images/gradient-mesh.webp';
import FacebookLogoSVG from '#app/images/svg-icons/facebook.svg?react';
import GoogleLogoSVG from '#app/images/svg-icons/google.svg?react';
import MainLogoSVG from '#app/images/svg-icons/logos/main.svg?react';
import Word<PERSON>ogoWhiteSVG from '#app/images/svg-icons/logos/white-word.svg?react';
import MainLogoWhiteSVG from '#app/images/svg-icons/logos/white.svg?react';
import WordLogoSVG from '#app/images/svg-icons/logos/word.svg?react';
import { checkHoneypot } from '#app/utils/honeypot.server';
import { handleNewSession } from '#app/utils/remix-auth.server';
import { handleAction } from '#app/utils/remix-helpers.server';
import { handleSignup } from '#server/utils/auth-helpers.server';
import {
  FACEBOOK_OAUTH_INITIATE_ROUTE,
  GOOGLE_OAUTH_INITIATE_ROUTE,
  LOGIN_ROUTE,
  PROFILE_NEW_ROUTE,
  REGISTER_ROUTE,
} from '#shared/constants/routes';
import { type FieldsFromConfig } from '#shared/types/forms';
import { type SignupData } from '#shared/types/index.js';
import { createSignupValidator } from '#shared/utils/validators';

export const action = handleAction(async ({ request, redirect }) => {
  const formData = await request.formData();
  checkHoneypot(formData);
  const formDataObj = Object.fromEntries(formData.entries());
  const session = await handleSignup(formDataObj as SignupData, request);

  return handleNewSession({
    request,
    session,
    redirectTo: PROFILE_NEW_ROUTE,
    redirect,
  });
});

export default function RegisterRoute() {
  const { t } = useTranslation();

  const heroes = [
    '/img/fm-register-hero-1.webp',
    '/img/fm-register-hero-2.webp',
    '/img/fm-register-hero-3.webp',
  ];

  const fields: FieldsFromConfig<typeof createSignupValidator> = [
    {
      name: 'email',
      type: 'email',
      label: t('Email'),
      placeholder: '<EMAIL>',
    },
    {
      name: 'password',
      type: 'password',
      label: t('Password'),
    },
    {
      name: 'verifyPassword',
      type: 'password',
      label: t('Confirm Password'),
    },
  ] as const;

  return (
    <div>
      <div
        style={{ backgroundImage: `url(${Background})` }}
        className="rotate-180 bg-[position:60%_100%] p-4 h-[90px] items-center sm:grid md:grid lg:hidden grid"
      >
        <WordLogoWhiteSVG className="rotate-180 h-5 w-full" />
        <MainLogoWhiteSVG className="rotate-180 h-9 w-full" />
      </div>

      <div className="px-4 sm:h-full md:h-full lg:h-[100dvh] flex flex-col max-w-full mx-auto justify-center items-center w-full">
        <div className="w-full sm:pt-6 pt-0 ">
          <div className="items-center sm:hidden md:hidden lg:flex hidden lg:pl-28">
            <MainLogoSVG className="tablet:h-9" />
            <WordLogoSVG className="tablet:h-5 w-auto" />
          </div>
        </div>

        <div className="grow grid grid-cols-1 lg:grid-cols-7 gap-2 w-full overflow-hidden py-8 lg:py-0">
          <div className="hidden lg:flex lg:items-center xl:items-end lg:justify-center lg:pl-14  lg:col-span-3 lg:pt-4 ">
            <Carousel
              opts={{ loop: true }}
              plugins={[AutoPlay({ delay: 3000 })]}
              className="w-full max-w-[400px] xl:max-w-[500px] 2xl:max-w-[600px]"
            >
              <CarouselContent>
                {heroes.map((hero, i) => (
                  <CarouselItem
                    key={i}
                    className="flex justify-center items-center"
                  >
                    <div className="aspect-[3/4] w-full">
                      <img
                        src={hero}
                        alt="fm-hero"
                        className="w-full h-full rounded-[2rem] xl:rounded-b-none object-cover"
                      />
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
            </Carousel>
          </div>

          <div className="w-full flex justify-center items-center lg:col-span-4">
            <div className="sm:w-full md:w-full w-[80%]">
              <div className="justify-items-center lg:justify-items-start mb-5">
                <Typography
                  variant="h1"
                  className="sm:text-[1.5rem] text-[1.2rem] font-bold !leading-none mb-2"
                >
                  {t('Ready to meet someone special?')}
                </Typography>
                <Typography
                  variant="h4"
                  className="sm:text-[1rem] text-[.9rem] font-thin"
                >
                  {t('Register now on FilipinaMeet!')}
                </Typography>
              </div>
              <FormFromConfig
                method="POST"
                action={REGISTER_ROUTE}
                fields={fields}
                createValidator={createSignupValidator}
                className="w-full"
                renderFields={({ fields }) => (
                  <>
                    <div className="space-y-6 w-full lg:max-w-screen-sm">
                      <div>{fields.email.node}</div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>{fields.password.node}</div>
                        <div>{fields.verifyPassword.node}</div>
                      </div>
                      <div className="w-full space-y-3">
                        <Button type="submit" className="w-full">
                          {t('Create account')}
                        </Button>
                      </div>

                      <Typography className="text-center hidden lg:block">
                        {t('Already have an account?')}{' '}
                        <Link
                          className="text-brand-primary font-semibold"
                          to={LOGIN_ROUTE}
                        >
                          {t('Login now')}
                        </Link>
                      </Typography>

                      <div className="flex w-full items-center gap-4">
                        <Separator className="grow w-auto" />
                        <Typography className="text-xs text-[#A2A2A2]">
                          {t('OR')}
                        </Typography>
                        <Separator className="grow w-auto" />
                      </div>

                      <div className="w-full flex flex-col md:flex-row gap-4">
                        <LinkButton
                          to={GOOGLE_OAUTH_INITIATE_ROUTE}
                          variant={'outline'}
                          className="grow"
                        >
                          <GoogleLogoSVG /> {t('Continue with Google')}
                        </LinkButton>
                        <LinkButton
                          to={FACEBOOK_OAUTH_INITIATE_ROUTE}
                          variant={'outline'}
                          className="grow"
                        >
                          <FacebookLogoSVG /> {t('Continue with Facebook')}
                        </LinkButton>
                      </div>

                      <Typography className="text-center lg:hidden">
                        {t('Already have an account?')}{' '}
                        <Link
                          className="text-brand-primary font-semibold"
                          to={LOGIN_ROUTE}
                        >
                          {t('Login now')}
                        </Link>
                      </Typography>
                    </div>
                  </>
                )}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
