import { Honeypot, SpamError } from 'remix-utils/honeypot/server';
import { HoneypotError } from '#server/utils/errors.server';

export const honeypot = new Honeypot({
  validFromFieldName: process.env.NODE_ENV === 'test' ? null : undefined,
  encryptionSeed: process.env.HONEYPOT_SECRET,
});

export function checkHoneypot(formData: FormData) {
  try {
    honeypot.check(formData);
  } catch (error) {
    if (error instanceof SpamError) {
      throw new HoneypotError();
    }
    throw error;
  }
}
