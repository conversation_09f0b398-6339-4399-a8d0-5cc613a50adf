import { PlusCircleIcon, XCircleIcon } from '@heroicons/react/20/solid';
import { Loader2Icon } from 'lucide-react';
import { useState, useCallback, useMemo } from 'react';
import { Input } from '#app/components/forms/inputs';
import { Button } from '#app/components/ui/button';

type PhotoUploadProps = {
  onChange: (file: File | null) => void; // Callback to handle file changes
  value?: string | null; // Current value or preview URL
  onClear?: () => void; // Callback to handle clearing the photo
  name: string;
  imageSize?: 'medium' | 'large' | 'small'; // Size enum
  hidePreview?: boolean; // New prop to hide the uploaded image preview
  defaultImageUrl?: string;
  onDelete?: () => void; // Callback to handle photo deletion
  showLoadingSpinner?: boolean;
  disableDelete?: boolean;
};

const sizeClasses = {
  medium: 'h-32 w-32', // Medium size classes
  large: 'h-64 w-64', // Large size classes
  small: 'h-16 w-16',
};

const PhotoUpload = ({
  onChange,
  value,
  imageSize = 'medium', // Default to medium size
  hidePreview = false, // Default to showing the preview
  defaultImageUrl, // Default to no default image
  onDelete, // Callback to handle photo deletion
  showLoadingSpinner,
  disableDelete,
  ...props
}: PhotoUploadProps) => {
  const [photo, setPhoto] = useState<string | null | undefined>(
    value || defaultImageUrl,
  );
  const [fileInputKey, setFileInputKey] = useState<number>(0);

  const handlePhotoChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      // just use the first file
      const file = event.target.files?.[0];
      if (file) {
        const photoUrl = URL.createObjectURL(file);
        setPhoto(photoUrl);
        onChange(file);
      } else {
        setPhoto(null);
        onChange(null);
      }
    },
    [onChange],
  );

  const clearFileInput = () => {
    setPhoto(null);
    setFileInputKey((prevKey) => prevKey + 1); // Change the key to force the file input to reset
    onChange(null); // Notify parent component that the file has been cleared
    onDelete?.();
  };

  const inputField = useMemo(() => (
    <Input
      type="file"
      accept="image/*"
      className="inset-0 h-full w-full cursor-pointer opacity-0"
      onChange={handlePhotoChange}
      key={fileInputKey}
      {...props}
    />
  ), [handlePhotoChange, fileInputKey, props]);

  if (hidePreview) {
    // TODO: fix hover state, should be a normal button
    return (
      <div className="relative h-12 w-12 cursor-pointer overflow-hidden">
        <PlusCircleIcon className="h-12 w-12 cursor-pointer text-gray-400" />
        <Button
          asChild
          size="icon"
          variant="ghost"
          className="absolute inset-0 cursor-pointer opacity-0"
        >
          {inputField}
        </Button>
      </div>
    );
  }

  return (
    <div
      className={`relative overflow-hidden rounded-lg border-2 border-gray-300 ${sizeClasses[imageSize]}`}
    >
      {
        showLoadingSpinner && (
          <div className="absolute inset-0 left-0 top-0 z-10 flex h-full w-full items-center justify-center bg-black opacity-50">
            <Loader2Icon className="h-12 w-12 animate-spin text-white" />
          </div>
        )
      }
      {
        photo && (
          <Button
            variant="destructive"
            size="icon"
            onClick={clearFileInput}
            className="absolute right-1 top-1"
            disabled={disableDelete ?? false}
          >
            <XCircleIcon className="h-6 w-6 text-white" />
          </Button>
        )
      }
      {
        photo && (
          <img
            src={photo}
            alt="Uploaded Preview"
            className="h-full w-full object-cover"
          />
        )
      }
      {
        // Keep the inputField present and do not render it conditionally
        // else its formData will disappear during rerenders when photo state change
        <div className="relative flex h-full w-full items-center justify-center bg-gray-100">
          <PlusCircleIcon className="h-12 w-12 text-gray-400 absolute" />
          <Button
            asChild
            size="icon"
            variant="ghost"
            className="cursor-pointer opacity-0"
          >
            {inputField}
          </Button> 
        </div>
      }
    </div>
  );
};

export { PhotoUpload };
