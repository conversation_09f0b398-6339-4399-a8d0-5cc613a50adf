import {
  isValidPhoneNumber,
  parsePhoneNumberWithError,
} from 'libphonenumber-js';
import { z } from 'zod';
import { RelationshipEnum } from '#shared/constants/enums';
import {
  GENDER_LIST,
  NEVER_SOMETIMES_OFTEN_LIST,
  RELIGION_ENUM_LIST,
  RESERVED_PROFILE_NAMES,
  YES_NO_LIST,
  YES_NO_NOT_SURE_LIST,
} from '#shared/constants/profile';
import { SEARCH_CATEGORIES } from '#shared/constants/search';
import { type TranslationFunction } from '#shared/types/translations';

export const createEmailValidator = (t: TranslationFunction) => {
  return z
    .string({ message: t('Email is required') })
    .email({ message: t('Invalid email address') });
};

export const createPasswordValidator = (t: TranslationFunction) => {
  return z.string({ message: t('Password is required') }).min(6, {
    message: t('Password must be at least 6 characters'),
  });
};

export const createSelectValidator = (t: TranslationFunction) => {
  return z.string({ message: t('Select an option') });
};

export const createMultiSelectValidator = (t: TranslationFunction) => {
  return z
    .array(z.string())
    .min(1, { message: t('Select at least one option') });
};

export const createAgeValidator = (t: TranslationFunction) => {
  return z
    .union([
      z.number({ message: t('Age must be a number') }),
      z
        .string({ message: t('Age is required') })
        .refine((val) => !isNaN(Number(val)), {
          message: t('Age must be a number'),
        })
        .transform((val) => Number(val)),
    ])
    .refine((val) => typeof val === 'number' && !isNaN(val), {
      message: t('Age must be a number'),
    })
    .refine((val) => val >= 18, {
      message: t('Age must be at least 18'),
    })
    .refine((val) => val <= 120, {
      message: t('Age must be at most 120'),
    });
};

export const createGenderValidator = (t: TranslationFunction) => {
  return z.enum(GENDER_LIST, {
    message: t('Not a valid Gender'),
  });
};

export const createGenderInterestValidator = (t: TranslationFunction) => {
  // handle a comma separated string or array
  return z.union([
    z
      .array(createGenderValidator(t))
      .nonempty(t('Select at least one gender interest')),
    z
      .string()
      .transform((val) => val.split(',').filter(Boolean))
      .pipe(
        createGenderValidator(t)
          .array()
          .nonempty(t('Select at least one gender interest')),
      ),
  ]);
};

export const createUsernameValidator = (t: TranslationFunction) => {
  return z
    .string({ message: t('Username is required') })
    .min(3, {
      message: t('Username must be at least 3 characters'),
    })
    .regex(/^[a-zA-Z0-9_]+$/, {
      message: t('Username can only contain letters, numbers, and underscores'),
    })
    .refine((val) => !RESERVED_PROFILE_NAMES.includes(val), {
      message: t('This username is reserved'),
    });
};

export const createChildrenPreferenceValidator = (t: TranslationFunction) => {
  return z.enum(YES_NO_LIST, {
    message: t('Invalid option for children preference'),
  });
};

export const createWantChildrenPreferenceValidator = (
  t: TranslationFunction,
) => {
  return z.enum(YES_NO_NOT_SURE_LIST, {
    message: t('Invalid option for want children preference'),
  });
};

export const createReligionPreferenceValidator = (t: TranslationFunction) => {
  return z.enum(RELIGION_ENUM_LIST, {
    message: t('Invalid option for religion preference'),
  });
};

export const createSmokingHabitsPreferenceValidator = (
  t: TranslationFunction,
) => {
  return z.enum(NEVER_SOMETIMES_OFTEN_LIST, {
    message: t('Invalid option for smoking habits preference'),
  });
};

export const createDrinkingHabitsPreferenceValidator = (
  t: TranslationFunction,
) => {
  return z.enum(NEVER_SOMETIMES_OFTEN_LIST, {
    message: t('Invalid option for drinking habits preference'),
  });
};

export const createSearchCategoryValidator = (t: TranslationFunction) => {
  return z.enum(SEARCH_CATEGORIES, {
    message: t('Invalid option for search category'),
  });
};

export const createFirstNameValidator = (t: TranslationFunction) => {
  return z.string({ message: t('First name is required') }).min(2, {
    message: t('First name must be at least 2 characters long'),
  });
};

export const createDateOfBirthValidator = (t: TranslationFunction) => {
  return z
    .string({ message: t('Date of birth is required') })
    .min(1, { message: t('Date of birth is required') })
    .refine(
      (val) => {
        const dateOfBirth = new Date(val);
        const today = new Date();
        let age = today.getFullYear() - dateOfBirth.getFullYear();
        const monthDiff = today.getMonth() - dateOfBirth.getMonth();
        if (
          monthDiff < 0 ||
          (monthDiff === 0 && today.getDate() < dateOfBirth.getDate())
        ) {
          age--;
        }
        return age >= 18;
      },
      { message: t('You must be at least 18 years old') },
    );
};

export function createOptionalNumericValidator(errorMessage: string) {
  return z
    .union([
      z.number({ message: errorMessage }).optional(),
      z
        .string()
        .transform((val) => (val === '' ? undefined : parseInt(val, 10)))
        .optional(),
    ])
    .refine((val) => val === undefined || !isNaN(val), {
      message: errorMessage,
    });
}

export function createOptionalEnumValidator<T extends string>(
  enumValues: Record<string, T>,
  errorMessage: string,
) {
  return z
    .union([
      z.nativeEnum(enumValues, {
        message: errorMessage,
      }),
      z
        .string()
        .refine((val) => !/\d/.test(val), {
          message: errorMessage,
        })
        .transform((val) => (val === '' ? undefined : val))
        .optional(),
    ])
    .optional() as z.ZodOptional<z.ZodType<T>>;
}

// TODO: allow for empty string
export function createMultiSelectEnumValidator<T extends string>(
  enumValues: Record<string, T>,
  errorMessage: string,
) {
  return z.array(z.nativeEnum(enumValues, { message: errorMessage })).or(
    z
      .string()
      .transform((val) => (val === '' ? [] : val.split(',')))
      .pipe(z.array(z.nativeEnum(enumValues, { message: errorMessage }))),
  ) as z.ZodType<T[]>;
}

export function createAgeRangeValidator(t: TranslationFunction) {
  // if we want this to work with the api, we will need a union with an object
  return z
    .string()
    .refine(
      (val) => {
        const [minAge, maxAge] = val.split(',').map(Number);
        return (
          typeof minAge === 'number' &&
          typeof maxAge === 'number' &&
          !isNaN(minAge) &&
          !isNaN(maxAge) &&
          minAge >= 0 &&
          maxAge >= minAge
        );
      },
      {
        // definitely just a message that
        message: t('Invalid age range'),
      },
    )
    .transform((val) => {
      const [minAge, maxAge] = val.split(',').map(Number);
      return { minAge, maxAge };
    });
}

export function createPhotoValidator(_t: TranslationFunction) {
  // temporary fix
  return z.any();
  // 	return z
  // 		.union([
  // 			z.instanceof(File).refine((file) => {
  // 				if (!(file instanceof File)) {
  // 					console.error('Validation failed: Not a valid File instance');
  // 					return false;
  // 				}
  // 				return true;
  // 			}, {
  // 				message: t('Please upload a valid photo'),
  // 			}), // Original File type
  // 			z.object({
  // 				blobLike: z.instanceof(File).refine((file) => {
  // 					if (!(file instanceof File)) {
  // 						console.error('Validation failed: blobLike is not a valid File instance');
  // 						return false;
  // 					}
  // 					return true;
  // 				}, {
  // 					message: t('Please upload a valid photo'),
  // 				}), // Handling the wrapped file
  // 				name: z.string(),
  // 				type: z.string(),
  // 				lastModified: z.number(),
  // 			}),
  // 		])
  // 		.transform((data) => {
  // 			if (data instanceof File) {
  // 				return data
  // 			} else if (data.blobLike instanceof File) {
  // 				return new File([data.blobLike], data.name, {
  // 					type: data.type,
  // 					lastModified: data.lastModified,
  // 				})
  // 			}
  // 			throw new Error(t('Please upload a valid photo'))
  // 		})
}

export const createVideoValidator = (_t: TranslationFunction) => {
  return z.any();
};

/**
 * Full form validators
 */

// this is used when creating a new profile
export function createProfileValidator(t: TranslationFunction) {
  return z.object({
    firstName: createFirstNameValidator(t),
    username: createUsernameValidator(t),
    gender: createGenderValidator(t),
    dateOfBirth: createDateOfBirthValidator(t),
    genderInterest: createGenderInterestValidator(t),
  });
}

//this is used when updating a profile. Note that the form fields are not the same as the createProfileValidator
export function updateProfileValidator(t: TranslationFunction) {
  return z.object({
    firstName: createFirstNameValidator(t),
    username: createUsernameValidator(t),
    gender: createGenderValidator(t),
    dateOfBirth: createDateOfBirthValidator(t),

    // Bio
    tagline: z.string({ message: t('Tagline must be a string') }).optional(),
    aboutMe: z.string({ message: t('About Me must be a string') }).optional(),
    occupation: z
      .string({ message: t('Occupation must be a string') })
      .optional(),

    // Location
    country: z.string().optional(),
    region: z.string().optional(),
    cityOrTown: z.string().optional(),
    genderInterest: createGenderInterestValidator(t).optional(),
  });
}

export function createSignupValidator(t: TranslationFunction) {
  return z
    .object({
      email: createEmailValidator(t),
      password: createPasswordValidator(t),
      verifyPassword: createPasswordValidator(t),
    })
    .refine((data) => data.password === data.verifyPassword, {
      message: t('Passwords do not match'),
      path: ['verifyPassword'],
    });
}

export function createSearchValidator(t: TranslationFunction) {
  return z.object({
    ageRange: createAgeRangeValidator(t).optional(),
    genderInterest: createGenderInterestValidator(t).optional(),
    relationshipPreference: createMultiSelectEnumValidator(
      RelationshipEnum,
      t('Invalid option for relationship preference'),
    ).optional(),
    childrenPreference: createChildrenPreferenceValidator(t).optional(),
    wantChildrenPreference: createWantChildrenPreferenceValidator(t).optional(),
    religionPreference: createReligionPreferenceValidator(t).optional(),
    smokingHabitsPreference:
      createSmokingHabitsPreferenceValidator(t).optional(),
    drinkingHabitsPreference:
      createDrinkingHabitsPreferenceValidator(t).optional(),
  });
}

export const changePhoneNumberValidator = (t: TranslationFunction) => {
  return z.object({
    phoneNumber: z
      .string()
      .refine(
        (value) => {
          const parsedNumber = parsePhoneNumberWithError(value);
          return isValidPhoneNumber(parsedNumber.number);
        },
        {
          message: t('Invalid phone number.'),
        },
      )
      .optional(),
  });
};

export const changePasswordValidator = (t: TranslationFunction) => {
  return z
    .object({
      currentPassword: createPasswordValidator(t),
      newPassword: createPasswordValidator(t),
      confirmPassword: createPasswordValidator(t),
    })
    .refine((data) => data.newPassword === data.confirmPassword, {
      message: t('Passwords do not match'),
      path: ['confirmPassword'],
    });
};
