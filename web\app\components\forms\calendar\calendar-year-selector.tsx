// REACT
import { useMemo, useCallback, memo } from 'react';

// STYLES
import {
  COLORS,
  STYLES,
  yearContainerStyle,
  baseYearStyle,
} from '#app/styles/calendar-picker-styles';

// TYPES
import { type YearItemProps } from '#app/types/calendar-types';

// UTILS
import { MIN_AGE_DATE } from '#app/utils/calendar-helpers';

interface CalendarYearSelectorProps {
  viewDate: Date;
  setViewDate: (date: Date) => void;
  setYearSelectOpen: (open: boolean) => void;
}

export function CalendarYearSelector({
  viewDate,
  setViewDate,
  setYearSelectOpen,
}: CalendarYearSelectorProps) {
  const currentYear = viewDate.getFullYear();

  const years = useMemo(() => {
    const endYear = MIN_AGE_DATE.getFullYear();
    const startYear = Math.max(1900, endYear - 120);
    const yearItems: JSX.Element[] = [];

    for (let year = endYear; year >= startYear; year--) {
      yearItems.push(
        <YearItem
          key={year}
          year={year}
          currentYear={currentYear}
          viewDate={viewDate}
          setViewDate={setViewDate}
          setYearSelectOpen={setYearSelectOpen}
        />,
      );
    }

    return yearItems;
  }, [currentYear, viewDate, setViewDate, setYearSelectOpen]);

  return <div style={yearContainerStyle}>{years}</div>;
}

const YearItem = memo(
  ({
    year,
    currentYear,
    viewDate,
    setViewDate,
    setYearSelectOpen,
  }: YearItemProps) => {
    const isActive = year === currentYear;

    const handleYearClick = useCallback(() => {
      const newDate = new Date(viewDate);
      newDate.setFullYear(year);

      // Adjust the date if it exceeds the minimum age date
      if (
        year === MIN_AGE_DATE.getFullYear() &&
        newDate.getMonth() > MIN_AGE_DATE.getMonth()
      ) {
        newDate.setMonth(MIN_AGE_DATE.getMonth());
        if (newDate.getDate() > MIN_AGE_DATE.getDate()) {
          newDate.setDate(MIN_AGE_DATE.getDate());
        }
      }

      setViewDate(newDate);
      setYearSelectOpen(false);
    }, [year, viewDate, setViewDate, setYearSelectOpen]);

    const style = isActive
      ? { ...baseYearStyle, ...STYLES.activeElement }
      : baseYearStyle;

    const handleMouseOver = useCallback(
      (e: React.MouseEvent<HTMLDivElement>) => {
        if (!isActive) {
          e.currentTarget.style.backgroundColor = COLORS.primaryLight;
          e.currentTarget.style.color = COLORS.primary;
          e.currentTarget.style.boxShadow =
            '0 4px 10px rgba(211, 53, 68, 0.15)';
        }
      },
      [isActive],
    );

    const handleMouseOut = useCallback(
      (e: React.MouseEvent<HTMLDivElement>) => {
        if (!isActive) {
          e.currentTarget.style.backgroundColor = 'transparent';
          e.currentTarget.style.color = '#1E1E1E';
          e.currentTarget.style.boxShadow = 'none';
          e.currentTarget.style.border = 'none';
        }
      },
      [isActive],
    );

    return (
      <div
        onClick={handleYearClick}
        style={style}
        onMouseOver={handleMouseOver}
        onMouseOut={handleMouseOut}
      >
        {year}
      </div>
    );
  },
);

YearItem.displayName = 'YearItem';
