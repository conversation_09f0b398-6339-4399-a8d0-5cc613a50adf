# flyctl launch added from .gitignore
**/node_modules
**/.DS_store

build
public/build
server-build
**/.env
**/.cache

prisma/data.db
prisma/data.db-journal
tests/prisma

test-results
playwright-report
playwright/.cache
tests/fixtures/email
coverage

# Easy way to create temporary files/folders that won't accidentally be added to git
**/*.local.*

# generated files
app/components/ui/icons
fly.toml
husky
