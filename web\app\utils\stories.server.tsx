import { VideoType, type Profile } from '@prisma/client';
import { endOfYear, startOfYear, subDays, subYears } from 'date-fns';
import { prisma } from '#server/utils/db.server';
import {
  attachImageUrlsToPhoto,
  attachVideoUrlFromVideo,
  uploadMediaFileAndStoreToDB,
} from '#server/utils/media.server';
import { AGES } from '#shared/constants';
import { PhotoType } from '#shared/constants/enums.js';

// TODO [JB] - Include Dating Preferences
export async function performStoriesSearch(profile: Profile) {
  const datingPreference = await prisma.datingPreference.findUnique({
    where: {
      profileId: profile.id,
    },
  });

  const today = new Date();

  // sync with default values on dating-preference.tsx
  const maxAge = datingPreference?.maxAge ?? AGES.MAX_AGE;
  const minAge = datingPreference?.minAge ?? AGES.MIN_AGE;

  const minDateOfBirth = startOfYear(subYears(today, maxAge));
  const maxDateOfBirth = endOfYear(subYears(today, minAge));

  const dobQuery = {
    dateOfBirth: {
      gte: minDateOfBirth,
      lte: maxDateOfBirth,
    },
  };

  const dateToday = new Date();
  const minStoryDate = subDays(dateToday, 7);
  const maxStoryDate = dateToday;

  const stories = await prisma.video.findMany({
    where: {
      profile: {
        id: {
          not: profile.id,
        },
        gender: {
          in: profile.genderInterest,
        },
        genderInterest: {
          has: profile.gender,
        },
        user: {
          privateUser: {
            ...dobQuery,
          },
        },
      },
      // Query dates within the last 7 days/1 week
      createdAt: {
        gte: minStoryDate,
        lte: maxStoryDate,
      },
    },
    include: {
      profile: {
        select: {
          username: true,
          user: {
            select: {
              verificationStatus: true,
              privateUser: true,
            },
          },
          parentPhotos: {
            include: {
              photoVariants: true,
            },
          },
        },
      },
    },
  });

  const allLikes = await prisma.videoLike.findMany({
    where: {
      videoLikerProfileId: profile.id,
      likedVideoId: {
        in: stories.map((story) => story.id),
      },
    },
  });

  const extendedStories = await Promise.all(
    stories.map(async (story) => {
      const { id, profileId, createdAt, profile } = story;

      const transformedStory = attachVideoUrlFromVideo(story);
      const likesThisStory = allLikes.some((like) => like.likedVideoId === id);
      const mainPhoto = profile.parentPhotos.find(
        (photo) => photo.photoType === PhotoType.MAIN,
      );

      return {
        id: transformedStory.id,
        url: transformedStory.url,
        profileId,
        likesThisStory,
        createdAt,
        profile: {
          ...profile,
          mainPhoto: mainPhoto ? attachImageUrlsToPhoto(mainPhoto) : null,
        },
      };
    }),
  );

  const groupedStories = Object.values(
    extendedStories.reduce(
      (acc: Record<string, (typeof extendedStories)[number][]>, story) => {
        if (!acc[story.profileId]) {
          acc[story.profileId] = []; // Ensure it is initialized
        }
        acc[story.profileId]!.push(story); // The `!` tells TypeScript it's safe to access
        return acc;
      },
      {},
    ),
  );

  return groupedStories;
}

export async function uploadStoryAndStoreToDB({
  story,
  profileId,
}: {
  story: File;
  profileId: string;
}) {
  await uploadMediaFileAndStoreToDB({
    file: story,
    fileType: VideoType.STORY,
    profileId,
  });
}
