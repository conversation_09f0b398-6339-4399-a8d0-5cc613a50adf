import { Href, <PERSON> } from 'expo-router';
import * as <PERSON><PERSON><PERSON><PERSON> from 'expo-web-browser';
import React from 'react';
import { Platform } from 'react-native';

export function ExternalLink(
  props: Omit<React.ComponentProps<typeof Link>, 'href'> & { href: string },
) {
  return (
    <Link
      target="_blank"
      {...props}
      href={props.href as unknown as Href<string | object>}
      onPress={(e) => {
        if (Platform.OS !== 'web') {
          // Prevent the default behavior of linking to the default browser on native.
          e.preventDefault();
          // Open the link in an in-app browser.
          WebBrowser.openBrowserAsync(props.href as string);
        }
      }}
    />
  );
}
