import {
  PhotoType,
  type PrivateUser,
  type Profile,
  type SocialProvider,
  VerificationStatus,
} from '@prisma/client';
// this should be the only remix import here since we use it for Fastify routes
import { Authenticator } from 'remix-auth';
import { connectionSessionStorage } from '#server/utils/connections.server';
import { prisma } from '#server/utils/db.server';
import { hashPassword } from '#server/utils/password.server';
import { type ProviderUser } from '#server/utils/providers/provider.js';
import { authSessionStorage } from '#server/utils/session.server';
import {
  genUniqueIdFromEmail,
  generateUniqueIdFromSocial,
} from '#server/utils/user.server';
import { SESSION_KEY } from '#shared/constants/auth';
import {
  type ParentPhotoWithPhotoVariants,
  type PhotoWithUrls,
} from '#shared/types/media.js';
import { attachImageUrlsToPhoto } from './media.server';

export const SESSION_EXPIRATION_TIME = 1000 * 60 * 60 * 24 * 30;
export const getSessionExpirationDate = () =>
  new Date(Date.now() + SESSION_EXPIRATION_TIME);

export const authenticator = new Authenticator<ProviderUser>(
  connectionSessionStorage,
);

export async function getPasswordHash(password: string) {
  return await hashPassword(password);
}

export async function signupWithEmail({
  email,
  password,
}: {
  email: string;
  password: string;
}) {
  const hashedPassword = await getPasswordHash(password);

  const session = await prisma.session.create({
    data: {
      expirationDate: getSessionExpirationDate(),
      privateUser: {
        create: {
          email: email.toLowerCase(),
          uniqueId: genUniqueIdFromEmail(email),
          password: {
            create: {
              hash: hashedPassword,
            },
          },
          user: {
            create: {},
          },
        },
      },
    },
    select: {
      id: true,
      expirationDate: true,
      privateUserId: true,
      privateUser: true,
    },
  });
  return session;
}

export async function signupWithSocialConnection({
  providerId,
  providerName,
  email,
  firstName,
  oAuthTokenData,
}: {
  providerId: string;
  providerName: SocialProvider;
  email?: string;
  firstName?: string;
  oAuthTokenData: {
    accessToken: string;
    expiryDate: Date;
    scope: string;
    refresToken?: string;
  };
}) {
  // note the email is NOT unique here
  // if you sign up via email and social with the same email, you'll have two accounts

  return prisma.$transaction(async (prisma) => {
    const updatePrivateUserFields = {
      email: email?.toLowerCase(),
    };

    // upsert the privateUser
    const privateUser = await prisma.privateUser.upsert({
      where: {
        uniqueId: generateUniqueIdFromSocial(providerName, providerId),
      },
      update: updatePrivateUserFields,
      create: {
        uniqueId: generateUniqueIdFromSocial(providerName, providerId),
        ...updatePrivateUserFields,
      },
    });

    await prisma.user.upsert({
      where: {
        privateUserId: privateUser.id,
      },
      update: {
        verificationStatus: VerificationStatus.APPROVED,
      },
      create: {
        verificationStatus: VerificationStatus.APPROVED,
        privateUserId: privateUser.id,
      },
    });

    const socialConnectionUpdateFields = {
      firstName,
    };

    // first genereate the socialConnection
    const socialConnection = await prisma.socialConnection.upsert({
      where: {
        providerName_providerId: { providerName, providerId },
      },
      update: socialConnectionUpdateFields,
      create: {
        providerId,
        providerName,
        ...socialConnectionUpdateFields,
        privateUser: {
          connect: {
            id: privateUser.id,
          },
        },
      },
    });

    // now genereate the oAuthToken
    const oAuthUpdateFields = {
      accessToken: oAuthTokenData.accessToken,
      refreshToken: oAuthTokenData.refresToken,
      scope: oAuthTokenData.scope,
      expiryDate: oAuthTokenData.expiryDate,
    };

    await prisma.oAuthToken.upsert({
      where: {
        socialConnectionId: socialConnection.id,
      },
      update: oAuthUpdateFields,
      create: {
        ...oAuthUpdateFields,
        socialConnectionId: socialConnection.id,
      },
    });

    // now genereate the session
    const session = await prisma.session.create({
      data: {
        expirationDate: getSessionExpirationDate(),
        privateUser: {
          connect: {
            id: privateUser.id,
          },
        },
      },
      select: {
        id: true,
        expirationDate: true,
        privateUserId: true,
        privateUser: true,
      },
    });
    return session;
  });
}

export async function createSessionFromPrivateUser({
  privateUser,
}: {
  privateUser: PrivateUser;
}) {
  return await prisma.session.create({
    data: {
      expirationDate: getSessionExpirationDate(),
      privateUserId: privateUser.id,
    },
    select: { id: true, expirationDate: true, privateUserId: true },
  });
}

export async function getBasicInfoFromRequest<
  IncludeProfile extends boolean,
  ExtendedProfileData extends boolean,
>({
  request,
  includeProfile,
  extendedProfileData,
}: {
  request: Request;
  includeProfile?: IncludeProfile;
  extendedProfileData?: ExtendedProfileData;
}) {
  const authSession = await authSessionStorage.getSession(
    request.headers.get('cookie'),
  );
  const sessionId = authSession.get(SESSION_KEY);
  if (!sessionId) return { session: null, authSession };

  const session = await prisma.session.findUnique({
    where: { id: sessionId, expirationDate: { gt: new Date() } },
    include: {
      privateUser: includeProfile
        ? {
            include: {
              user: {
                include: {
                  profile: extendedProfileData
                    ? {
                        include: {
                          parentPhotos: {
                            include: {
                              photoVariants: true,
                            },
                          },
                        },
                      }
                    : true,
                },
              },
            },
          }
        : { include: { user: true } },
    },
  });

  const privateUser = session?.privateUser ?? undefined;
  const user = privateUser?.user ?? undefined;
  let profile =
    //@ts-ignore
    (user?.profile as IncludeProfile extends true
      ? ExtendedProfileData extends true
        ? // not sure if we need to enrich the other photos with urls
          Profile & {
            mainPhoto?: PhotoWithUrls;
            parentPhotos: ParentPhotoWithPhotoVariants[];
            dateOfBirth?: Date;
          }
        : Profile & { dateOfBirth?: Date }
      : undefined) ?? undefined;

  if (profile && extendedProfileData && 'parentPhotos' in profile) {
    const mainPhoto = profile.parentPhotos.find(
      (photo) => photo.photoType === PhotoType.MAIN,
    );
    if (mainPhoto) {
      profile.mainPhoto = attachImageUrlsToPhoto(mainPhoto);
    }
  }

  if (profile && privateUser?.dateOfBirth) {
    profile.dateOfBirth = privateUser.dateOfBirth;
  }

  return { session, user, privateUser, profile, authSession };
}
