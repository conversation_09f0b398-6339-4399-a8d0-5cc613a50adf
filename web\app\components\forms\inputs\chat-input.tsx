import type React from 'react';
import { useState } from 'react';
import { PiPaperPlaneTiltFill } from 'react-icons/pi';
import { v4 as uuidv4 } from 'uuid';
import { AutosizeTextarea } from '#app/components/ui/autosize-textarea';
import { Button } from '#app/components/ui/button.js';
import { cn } from '#app/utils/misc';

declare global {
  interface Window {
    sendWebSocketMessage?: (
      message: string,
      id: string,
      attachments?: Array<{
        id: string;
        base64Data?: string;
        type?: string;
        name?: string;
      }>,
    ) => void;
  }
}

type FileWithPreview = {
  id: string;
  file: File;
  previewUrl: string;
};

interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  inputRef?: React.Ref<HTMLTextAreaElement>;
  filesWithPreviews: FileWithPreview[];
}

const ChatInput = ({
  className,
  value,
  onChange,
  filesWithPreviews,
  ...props
}: TextareaProps) => {
  const [inputValue, setInputValue] = useState('');

  const fileToBase64 = async (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });
  };

  const submitForm = async (element: HTMLElement | null) => {
    const form = element?.closest('form');

    if (
      filesWithPreviews.length === 0 &&
      (!inputValue || (typeof inputValue === 'string' && !inputValue.trim()))
    )
      return;

    const messageId = uuidv4();

    try {
      const attachmentsPromises = filesWithPreviews.map(async (item) => {
        const base64Data = item.file.type.startsWith('image/')
          ? await fileToBase64(item.file)
          : '';

        return {
          id: item.id,
          base64Data: base64Data,
          type: item.file.type,
          name: item.file.name,
        };
      });

      const processedAttachments = await Promise.all(attachmentsPromises);

      if (window.sendWebSocketMessage) {
        window.sendWebSocketMessage(
          String(inputValue),
          messageId,
          processedAttachments,
        );
      } else {
        console.warn(
          'WebSocket function not available - message will only be saved to database',
        );
        setTimeout(async () => {
          if (window.sendWebSocketMessage) {
            window.sendWebSocketMessage(
              String(inputValue),
              messageId,
              processedAttachments,
            );
          }
        }, 500);
      }

      setInputValue('');

      if (form) form.requestSubmit();
    } catch (error) {
      console.error('Error preparing attachments:', error);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      void submitForm(e.currentTarget);
    }
  };

  const handleSubmit = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    void submitForm(e.currentTarget);
  };

  return (
    <div className="flex items-center w-full gap-2 sm:gap-4">
      <AutosizeTextarea
        {...props}
        value={inputValue}
        onChange={(e) => {
          setInputValue(e.target.value);
          if (onChange) {
            onChange(e);
          }
        }}
        onKeyDown={handleKeyDown}
        maxHeight={200}
        className={cn('w-full resize-none rounded-[1.7rem]', className)}
        rows={1}
      />

      <Button
        size={'icon'}
        variant={'link'}
        className="p-0"
        onClick={handleSubmit}
      >
        <PiPaperPlaneTiltFill className="text-2xl sm:text-3xl" />
      </Button>
    </div>
  );
};

export { ChatInput };
