import { useState, useMemo } from 'react';
import Lightbox from 'yet-another-react-lightbox';
import Thumbnails from 'yet-another-react-lightbox/plugins/thumbnails';
import 'yet-another-react-lightbox/styles.css';
import 'yet-another-react-lightbox/plugins/thumbnails.css';
import { useMobile } from '#app/contexts/mobile-context';

type ChatImageProps = {
  slides: Array<{
    src: string;
    alt: string;
  }>;
  initialSlide?: number;
};

export function ChatImage({ slides, initialSlide = 0 }: ChatImageProps) {
  const [open, setOpen] = useState(false);
  const { isMobile } = useMobile();
  const hasMultipleSlides = slides.length > 2;

  const plugins = useMemo(
    () => (hasMultipleSlides ? [Thumbnails] : []),
    [hasMultipleSlides],
  );

  const styles = useMemo(
    () => ({
      container: {
        backgroundColor: 'rgba(0, 0, 0, 0.75)',
        backdropFilter: 'blur(20px)',
        WebkitBackdropFilter: 'blur(20px)',
      },
      thumbnailsContainer: {
        backgroundColor: 'rgba(0, 0, 0, 0.75)',
        backdropFilter: 'blur(20px)',
        WebkitBackdropFilter: 'blur(20px)',
      },
      navigationPrev: {
        left: 'max(5%, 0.5rem)',
        display: hasMultipleSlides && !isMobile ? 'flex' : 'none',
      },
      navigationNext: {
        right: 'max(5%, 0.5rem)',
        display: hasMultipleSlides && !isMobile ? 'flex' : 'none',
      },
    }),
    [hasMultipleSlides, isMobile],
  );

  return (
    <>
      <div onClick={() => setOpen(true)} className="cursor-pointer">
        <img
          src={slides[initialSlide]?.src ?? ''}
          alt={slides[initialSlide]?.alt ?? ''}
          className="w-full object-cover rounded-[10px]"
          style={{ maxWidth: '300px' }}
        />
      </div>

      <Lightbox
        styles={styles}
        open={open}
        close={() => setOpen(false)}
        plugins={plugins}
        slides={slides}
        index={initialSlide}
        controller={{
          closeOnBackdropClick: true,
        }}
      />
    </>
  );
}
