import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
} from '@aws-sdk/client-s3';
import { nanoid } from 'nanoid';

// Cloudflare R2 Configuration
export const s3 = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_ENDPOINT || '',
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.CLOUDFLARE_SECRET_ACCESS_KEY || '',
  },
});

// File size limits in bytes
const FILE_SIZE_LIMITS = {
  image: 15 * 1024 * 1024, // 15 MB
  video: 150 * 1024 * 1024, // 150 MB
  default: 10 * 1024 * 1024, // 10 MB default for other file types
};

export async function uploadAttachment(
  file: File,
  folder: string = 'attachments',
): Promise<string> {
  // Check file size based on file type
  const fileType = file.type.split('/')[0]; // Gets 'image', 'video', etc.
  const sizeLimit =
    FILE_SIZE_LIMITS[fileType as keyof typeof FILE_SIZE_LIMITS] ||
    FILE_SIZE_LIMITS.default;

  if (file.size > sizeLimit) {
    const limitInMB = sizeLimit / (1024 * 1024);
    throw new Error(
      `File size exceeds the limit of ${limitInMB} MB for ${fileType} files.`,
    );
  }

  const bytes = await file.arrayBuffer();
  const buffer = Buffer.from(bytes);

  const extension = file.name.split('.').pop();
  const key = `${folder}/${nanoid()}.${extension}`;

  await s3.send(
    new PutObjectCommand({
      Bucket: process.env.CLOUDFLARE_BUCKET_NAME,
      Key: key,
      Body: buffer,
      ContentType: file.type,
    }),
  );

  return key;
}

export async function uploadFileToR2({
  file,
  key,
}: {
  file: File;
  key: string;
}) {
  const arrayBuffer = await file.arrayBuffer();
  const buffer = Buffer.from(arrayBuffer);

  await s3.send(
    new PutObjectCommand({
      Bucket: process.env.CLOUDFLARE_BUCKET_NAME,
      Key: key,
      Body: buffer,
      ContentType: file.type,
    }),
  );
}

export async function deleteFileFromR2({ key }: { key: string }) {
  await s3.send(
    new DeleteObjectCommand({
      Bucket: process.env.CLOUDFLARE_BUCKET_NAME,
      Key: key,
    }),
  );
}
