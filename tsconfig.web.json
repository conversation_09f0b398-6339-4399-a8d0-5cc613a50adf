{"compilerOptions": {"module": "ESNext", "moduleResolution": "Node", "isolatedModules": true, "jsx": "react-jsx", "target": "ES2022", "strict": true, "skipLibCheck": true, "allowImportingTsExtensions": true, "noUncheckedIndexedAccess": true, "noEmit": true, "composite": true, "paths": {"#shared/*": ["./shared/*"], "#data/*": ["./data/*"]}}, "references": [{"path": "./web/tsconfig.json"}], "include": []}