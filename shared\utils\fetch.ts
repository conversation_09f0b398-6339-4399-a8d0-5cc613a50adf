import { type HttpMethod } from '#shared/types/api';
import { generateFullUrl } from '#shared/utils/misc';

interface FetchOptions extends RequestInit {
  headers?: HeadersInit;
  queryParams?: Record<string, string | number | undefined>;
  handleError?: (response: Response) => Promise<void>;
}

export async function apiFetch<T>(
  url: string,
  method: HttpMethod,
  rawOptions: FetchOptions | undefined,
): Promise<T> {
  const { queryParams, ...options } = rawOptions || {};
  // handle query params if they exist
  if (queryParams) {
    url = generateFullUrl(url, queryParams);
  }

  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    ...(options?.headers || {}),
  };
  const response = await fetch(url, {
    method,
    headers,
    ...options,
  });
  if (!response.ok) {
    let errorResponse: any;
    try {
      errorResponse = await response.json();
    } catch {
      errorResponse = { message: response.statusText };
    }
    console.error('API Fetch Error:', errorResponse);
    // special callback to handle errors
    if (options?.handleError) {
      await options.handleError(response);
    }
    // TODO: handler errors like 400s etc
    throw new Error(response.statusText);
  }

  const data = await response.json();
  return data as T;
}

// Boilerplate code that exists in many files
export async function apiGet<T>(
  url: string,
  options?: FetchOptions,
): Promise<T> {
  return apiFetch<T>(url, 'GET', options);
}

export async function apiPost<T>(
  url: string,
  body: any,
  options?: FetchOptions,
): Promise<T> {
  return apiFetch<T>(url, 'POST', { ...options, body: JSON.stringify(body) });
}

export async function apiPut<T>(
  url: string,
  body: any,
  options?: FetchOptions,
): Promise<T> {
  return apiFetch<T>(url, 'PUT', { ...options, body: JSON.stringify(body) });
}

export async function apiDelete<T>(
  url: string,
  options?: FetchOptions,
): Promise<T> {
  return apiFetch<T>(url, 'DELETE', options);
}

export async function apiPatch<T>(
  url: string,
  body: any,
  options?: FetchOptions,
): Promise<T> {
  return apiFetch<T>(url, 'PATCH', { ...options, body: JSON.stringify(body) });
}
