import React from 'react';
import { useTranslation } from 'react-i18next';
import { getGenderFormOptions } from '#shared/utils/form-options.js';
import { GenericMultiCheckbox } from './generic-multi-checkbox';

type GenderMultiCheckboxProps = Omit<
  React.ComponentProps<typeof GenericMultiCheckbox>,
  'options' | 'formValue'
>;

const GenderMultiCheckbox = (props: GenderMultiCheckboxProps) => {
  const { t } = useTranslation();

  const translatedOptions = getGenderFormOptions(t);

  return <GenericMultiCheckbox options={translatedOptions} {...props} />;
};

export { GenderMultiCheckbox };
