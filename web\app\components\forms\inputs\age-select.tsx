import React from 'react';
import { useTranslation } from 'react-i18next';
import { AGES } from '#shared/constants/profile';
import { GenericSelect } from './generic-select';

const ageOptions = Array.from(
  { length: AGES.MAX_AGE - AGES.MIN_AGE + 1 },
  (_, i) => {
    const age = AGES.MIN_AGE + i;
    return { value: age.toString(), label: age.toString() };
  },
);

ageOptions.push({ value: 'f', label: 'f' });

type AgeSelectProps = Omit<
  React.ComponentProps<typeof GenericSelect>,
  | 'options'
  | 'value'
  | 'onChange'
  | 'defaultValue'
  | 'placeholder'
  | 'formValue'
> & {
  value?: number;
  onChange?: (value: number) => void;
};

const AgeSelect = ({
  value: inputValue,
  onChange: inputOnChange,
  ...props
}: AgeSelectProps) => {
  const { t } = useTranslation();
  const onChange = (value: string) => {
    if (inputOnChange) {
      inputOnChange(Number(value));
    }
  };
  const value = inputValue ? inputValue.toString() : undefined;

  return (
    <GenericSelect
      options={ageOptions}
      onChange={onChange}
      value={value}
      placeholder={t('Select your age')}
      formValue={value}
      {...props}
    />
  );
};

export { AgeSelect };
