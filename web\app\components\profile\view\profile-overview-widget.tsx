// import { But<PERSON> } from '@headlessui/react';
import { CheckCircleIcon, HeartIcon } from '@heroicons/react/20/solid';
import { type Location } from '@prisma/client';
import { MessageCircleIcon } from 'lucide-react';
import { FaBriefcase } from 'react-icons/fa';
import { FaLocationDot } from 'react-icons/fa6';
import Typography from '#app/components/typography';
import { Button } from '#app/components/ui/button.js';

interface Props {
  firstName: string;
  age: number | null;
  occupation?: string | null;
  location: Location | null;
  isVerified: boolean;
  tagLine?: string | null;
  bio?: string | null;
  isOnline: boolean;
}

function ProfileOverviewWidget({
  firstName,
  age,
  occupation,
  location,
  isVerified,
  tagLine,
  bio,
  isOnline,
}: Props) {
  function formatLocation(location?: Location | null) {
    if (!location) return 'None';

    const { cityOrTown, countryIso2 } = location;

    return [cityOrTown, countryIso2].filter(Boolean).join(', ');
  }
  return (
    <div className="flex flex-col gap-4 w-full mx-4 justify-around">
      <div className="flex flex-col">
        <div className="flex items-center gap-1.5 justify-between">
          <div className="flex items-center gap-1.5">
            <Typography variant="h3">
              {firstName}, {age || 'None'}
            </Typography>

            {isVerified && (
              <div className="h-6 w-6 tablet:h-5 tablet:w-5 text-[#2F80ED] relative rounded-full">
                <CheckCircleIcon className="relative z-[1]" />
                <div className="bg-white absolute h-3 w-3 tablet:h-2 tablet:w-2 rounded-full top-1.5 right-1.5" />
              </div>
            )}
          </div>

          <div className="flex items-center gap-1 bg-[#1E1E1E80] rounded-sm px-2 py-1 text-white">
            {isOnline && (
              <div className="h-2 w-2 bg-green-500 outline-2 outline-white rounded-full" />
            )}
            {!isOnline && <div className="h-2 w-2 bg-slate-500 rounded-full" />}
            <span>{isOnline ? 'Online' : 'Offline'}</span>
          </div>
        </div>
        {tagLine && (
          <Typography
            variant="h5"
            className="font-medium italic text-brand-primary"
          >
            "{tagLine}"
          </Typography>
        )}
      </div>
      <div>
        <div className="flex items-center gap-2">
          <FaBriefcase className="text-brand-accent-secondary" />
          <Typography>{occupation || 'None'}</Typography>
        </div>
        <div className="flex items-center gap-2">
          <FaLocationDot className="text-brand-accent-secondary" />
          <Typography>{formatLocation(location)}</Typography>
        </div>
      </div>
      <div>
        <Typography variant="h5">My Bio</Typography>
        <Typography variant="body">{bio || 'None'}</Typography>
      </div>

      <div className="flex gap-2">
        <Button
          variant="secondary"
          className="w-1/4 bg-[#2C60AA] text-white py-7"
        >
          <MessageCircleIcon className="w-4 h-4" />
          <span>Message</span>
        </Button>
        <Button variant="default" className="w-1/4  py-7">
          <HeartIcon className="w-4 h-4" />
          <span>Like</span>
        </Button>
      </div>
    </div>
  );
}

export { ProfileOverviewWidget };
