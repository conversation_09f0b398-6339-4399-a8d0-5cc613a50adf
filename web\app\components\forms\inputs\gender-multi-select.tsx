import React from 'react';
import { useTranslation } from 'react-i18next';
import { getGenderFormOptions } from '#shared/utils/form-options';
import { GenericMultiSelect } from './generic-multi-select';

type GenderMultiSelectProps = Omit<
  React.ComponentProps<typeof GenericMultiSelect>,
  'options' | 'formValue'
>;

const GenderMultiSelect = (props: GenderMultiSelectProps) => {
  const { t } = useTranslation();

  const translatedOptions = getGenderFormOptions(t);

  return <GenericMultiSelect options={translatedOptions} {...props} />;
};

export { GenderMultiSelect };
