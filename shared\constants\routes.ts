// TODO: prefix with AUTH_
export const LOGIN_ROUTE = '/auth/login';
export const REGISTER_ROUTE = '/auth/register';
export const LOGOUT_ROUTE = '/auth/logout';
export const FACEBOOK_OAUTH_INITIATE_ROUTE = '/auth/facebook/initiate';
export const GOOGLE_OAUTH_INITIATE_ROUTE = '/auth/google/initiate';
// profile routes
export const PROFILE_NEW_ROUTE = '/profile/new';
export const PROFILE_SETUP_ROUTE = '/profile/setup';
export const PROFILE_SETUP_VERIFICATION_ROUTE = '/profile/setup/verification';
export const PROFILE_SETUP_PHONE_ROUTE = '/profile/setup/phone';
export const PROFILE_SETUP_BIO_ROUTE = '/profile/setup/bio';
export const PROFILE_SETUP_PHOTOS_ROUTE = '/profile/setup/photos';
export const PROFILE_SETUP_LIFESTYLE_ROUTE = '/profile/setup/lifestyle';
export const PROFILE_SETUP_COMPLETE_ROUTE = '/profile/setup/complete';
export const PROFILE_PHOTO_VERIFY_ROUTE = '/profile/photo-verify';
export const PROFILE_ME_ROUTE = '/profile/me';
export const PROFILE_EDIT_HOME_ROUTE = '/profile/edit';
export const PROFILE_EDIT_BASIC_INFO_ROUTE = '/profile/edit/basic-info';
export const PROFILE_EDIT_PHOTOS_ROUTE = '/profile/edit/photos';
export const PROFILE_EDIT_LOCATION_ROUTE = '/profile/edit/location';
export const PROFILE_EDIT_BIO_ROUTE = '/profile/edit/bio';
export const PROFILE_EDIT_APPEARANCE_ROUTE = '/profile/edit/appearance';
export const PROFILE_EDIT_DATING_PREFERENCE_ROUTE =
  '/profile/edit/dating-preference';
export const PROFILE_EDIT_LIFESTYLE_ROUTE = '/profile/edit/lifestyle';
// activitiy route
export const ACTIVITY_LIKES_ME_ROUTE = '/activity/likes/me';
export const ACTIVITY_LIKES_OTHERS_ROUTE = '/activity/likes/others';
export const ACTIVITY_LIKES_MUTUAL_ROUTE = '/activity/likes/mutual';
export const ACTIVITY_PROFILE_VIEWS_ROUTE = '/activity/profile-views';
export const ACTIVITY_CONVERSATION_LIST_ROUTE = '/activity/conversations';
// settings route
export const SETTINGS_ROUTE = '/settings';
export const SETTINGS_ACCOUNT_ROUTE = '/settings/account';
export const SETTINGS_CHANGE_PASSWORD_ROUTE =
  '/settings/account/change-password';
export const SETTINGS_NOTIFICATIONS_ROUTE = '/settings/account/notifications';
export const SETTINGS_ACCOUNT_PRESENCE_ROUTE =
  '/settings/account/account-presence';
export const SETTINGS_CONTACT_INFO_ROUTE = '/settings/contact-info';
//search routes
export const SEARCH_BASIC_ROUTE = '/search/basic';
// admin routes
export const ADMIN_VERIFY_PHOTOS_ROUTE = '/admin/verify-photos';
export const ADMIN_IMPERSONATE_ROUTE = '/admin/impersonate';
//other routes
export const DEFAULT_REDIRECT_ROUTE = SEARCH_BASIC_ROUTE;

// api routes
export const API_AUTH_REGISTER_ROUTE = '/api/auth/register';
export const API_PROFILE_NOTIFCATIONS_UNREAD_ROUTE =
  '/api/profile/notifications/unread';
