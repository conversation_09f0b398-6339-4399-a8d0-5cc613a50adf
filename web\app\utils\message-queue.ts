interface QueuedMessage {
  id: string;
  payload: any;
  attempts: number;
  timestamp: number;
}

export class MessageQueue {
  private queue: QueuedMessage[] = [];
  private processing = false;
  private maxRetries = 3;
  private socket: WebSocket | null = null;

  setSocket(socket: WebSocket | null) {
    this.socket = socket;
    if (socket) {
      void this.processQueue();
    }
  }

  enqueue(message: any) {
    const queuedMessage: QueuedMessage = {
      id: Date.now().toString(),
      payload: message,
      attempts: 0,
      timestamp: Date.now(),
    };
    this.queue.push(queuedMessage);
    void this.processQueue();
  }

  private async processQueue() {
    if (
      this.processing ||
      !this.socket ||
      this.socket.readyState !== WebSocket.OPEN
    ) {
      return;
    }

    this.processing = true;

    while (this.queue.length > 0) {
      const message = this.queue[0];
      if (!message) continue;

      if (message.attempts >= this.maxRetries) {
        console.error(
          `Failed to send message after ${this.maxRetries} attempts:`,
          message,
        );
        this.queue.shift();
        continue;
      }

      try {
        this.socket.send(JSON.stringify(message.payload));
        this.queue.shift();
      } catch (error) {
        console.error('WebSocket send error:', error);
        message.attempts += 1;
      }
    }

    this.processing = false;
  }

  clear() {
    this.queue = [];
    this.processing = false;
  }
}
