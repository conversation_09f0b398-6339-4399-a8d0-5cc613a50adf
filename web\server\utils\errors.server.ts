import { type ZodError } from 'zod';
import { type ErrorDetail } from '#shared/types/errors.client';

// TODO: move out of shared because mobile doesn't need this

export class RaisedError extends Error {
  statusCode: number;
  constructor(message: string, name: string, statusCode: number) {
    super(message);
    this.name = name;
    this.statusCode = statusCode;
  }
}

export class BadRequestError extends RaisedError {
  constructor(message: string) {
    super(message, 'BadRequestError', 400);
  }
}
export class UnauthorizedError extends RaisedError {
  constructor(message: string = 'Unauthorized access') {
    super(message, 'UnauthorizedError', 401);
  }
}

export class ForbiddenError extends RaisedError {
  constructor(message: string = 'Forbidden access') {
    super(message, 'ForbiddenError', 403);
  }
}

export class NotFoundError extends RaisedError {
  constructor(message: string = 'Resource not found') {
    super(message, 'NotFoundError', 404);
  }
}

export class ProfileNotFoundError extends NotFoundError {
  constructor(message: string = 'Profile not found') {
    super(message);
    this.name = 'ProfileNotFoundError';
  }
}

export class InternalServerError extends RaisedError {
  constructor(message: string = 'Internal server error') {
    super(message, 'InternalServerError', 500);
  }
}
export class UserNotLoggedInError extends UnauthorizedError {
  authSession: any;
  attemptedUrl: string;
  constructor({
    authSession,
    attemptedUrl,
  }: {
    authSession: any; // this is difficult to type correctly
    attemptedUrl: string;
  }) {
    super('User not logged in');
    this.name = 'UserNotLoggedInError';
    this.authSession = authSession;
    this.attemptedUrl = attemptedUrl;
  }
}

export class UserNotAdminError extends UnauthorizedError {
  constructor(message: string = 'User is not an admin') {
    super(message);
    this.name = 'UserNotAdminError';
  }
}

export class NoUserProfileError extends UnauthorizedError {
  constructor(message: string = 'User has no profile') {
    super(message);
    this.name = 'NoUserProfileError';
  }
}

export class ValidationError extends BadRequestError {
  errorDetail: ErrorDetail;

  constructor(errorDetail: ErrorDetail) {
    super('ValidationError');
    this.name = 'ValidationError';
    this.errorDetail = errorDetail;
  }
}

export class ZodValidationError extends ValidationError {
  constructor(error: ZodError) {
    super(error.flatten().fieldErrors);
  }
}

export class HoneypotError extends BadRequestError {
  constructor(message: string = 'Form not submitted properly') {
    super(message);
    this.name = 'HoneypotError';
  }
}

export class EmailError extends InternalServerError {
  constructor(message: string = 'Error sending email') {
    super(message);
    this.name = 'EmailError';
  }
}

export class IpLocationError extends InternalServerError {
  constructor(message: string = 'Error fetching IP location data') {
    super(message);
    this.name = 'IpLocationError';
  }
}

export class IpLocationApiError extends BadRequestError {
  constructor(message: string = 'IP location API error') {
    super(message);
    this.name = 'IpLocationApiError';
  }
}
