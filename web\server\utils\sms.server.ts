import parsePhoneNumber from 'libphonenumber-js';
import Telnyx from 'telnyx';
import { z } from 'zod';
import settings from '#root/settings';

type PhoneNumber = string;

interface SendSmsOptions {
  telephone: PhoneNumber;
  text: string;
}

const telnyxSmsResponseSchema = z.object({
  data: z.object({
    id: z.string().uuid(),
    record_type: z.string(),
    direction: z.literal('outbound'),
    type: z.enum(['SMS', 'MMS']),
    messaging_profile_id: z.string().uuid(),
    organization_id: z.string().uuid(),
    from: z.object({
      phone_number: z.string(),
    }),
    to: z.array(
      z.object({
        phone_number: z.string(),
        status: z.enum([
          'queued',
          'sending',
          'sent',
          'expired',
          'sending_failed',
          'delivery_uncofirmed',
          'delivered',
          'delivery_failed',
        ]),
      }),
    ),
    text: z.string(),
    received_at: z.string(),
  }),
});

type TelnyxSmsResponse = z.infer<typeof telnyxSmsResponseSchema>;

type ValidCountries = keyof typeof settings.telnyxSettings.phoneNumber;

function countryHasSpecificPhoneNumber(
  countryCode: string,
): countryCode is ValidCountries {
  return settings.telnyxSettings.phoneNumber.hasOwnProperty(countryCode);
}

function getFromNumber(to: string): string {
  let parsedPhoneNumber = parsePhoneNumber(to);
  if (!parsedPhoneNumber) {
    console.warn(`Not possible to parse phone number ${to}.`);
    return settings.telnyxSettings.phoneNumber['US'];
  }

  const countryCode = parsedPhoneNumber.country;
  let phoneNumber: string | undefined;
  if (countryCode) {
    if (countryHasSpecificPhoneNumber(countryCode)) {
      phoneNumber = settings.telnyxSettings.phoneNumber[countryCode];
    }
  }
  if (!phoneNumber) {
    console.warn(`No country code detected in this number: ${to}.`);
    return settings.telnyxSettings.phoneNumber['US'];
  }
  return phoneNumber;
}

async function sendSms(options: SendSmsOptions): Promise<TelnyxSmsResponse> {
  const { telephone, text } = options;

  if (!settings.telnyxSettings.apiKey) {
    throw new Error('API key for telnyx not configured');
  }

  const telnyx = new Telnyx(settings.telnyxSettings.apiKey);

  const message = await telnyx.messages.create({
    from: getFromNumber(telephone),
    to: telephone,
    text: text,
    type: 'SMS',
    auto_detect: false,
    use_profile_webhooks: false,
  });

  const data = telnyxSmsResponseSchema.parse(message);

  return data;
}

export default sendSms;
