import { type Message } from '@prisma/client';
import { useFetcher, useRevalidator } from '@remix-run/react';
import { useMutation } from '@tanstack/react-query';
import { useRef, useState, useEffect, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { toast as showToast } from 'sonner';
import { useMobile } from '#app/contexts/mobile-context';
import { useWebSocket } from '#app/contexts/web-socket-context';
import { type ConversationWithMessages, type RootProfile } from '#app/types';
import { apiDelete, apiPost } from '#app/utils/fetch.client';
import { type OnlineStatusWithTime } from '#shared/types/profile';
import ConversationsContainer from '../activity/conversations-container';
import ChatForm from './chat-form';
import ChatHeader from './chat-header';
import { ChatMessage } from './chat-message';

interface MessageContainerProps {
  conversationWithMessages: ConversationWithMessages | null;
  profile: RootProfile;

  receiverProfile: RootProfile & {
    onlineStatus?: OnlineStatusWithTime;
    isBlocked: boolean;
  };
  currentPage: number;
  hasMore: boolean;
}

export function MessageContainer({
  conversationWithMessages,
  profile,
  receiverProfile,
  currentPage,
  hasMore,
}: MessageContainerProps) {
  const { t } = useTranslation();
  const conversationIdRef = useRef<string | null>(
    conversationWithMessages?.id || null,
  );
  const receiverProfileIdRef = useRef<string | null>(
    receiverProfile?.id || null,
  );

  const [page, setPage] = useState(currentPage);
  const [isLoading, setIsLoading] = useState(false);
  const [allMessagesLoaded, setAllMessagesLoaded] = useState(!hasMore);
  const { isMobile } = useMobile();

  const chatContainerRef = useRef<HTMLDivElement>(null);
  const loadingRef = useRef<HTMLDivElement>(null);
  const initialScrollDone = useRef(false);
  const previousScrollHeightRef = useRef<number>(0);
  const isAdjustingScroll = useRef<boolean>(false);
  const revalidator = useRevalidator();
  const [isBlocked, setIsBlocked] = useState(receiverProfile.isBlocked);

  const {
    allIncomingMessages,
    enrichedConversationsState,
    setEnrichedConversationsState,
    messages,
    setMessages,
  } = useWebSocket();

  const fetcher = useFetcher();
  const lastMessageId = useMemo(
    () => (messages.length > 0 ? messages[messages.length - 1]?.id : null),
    [messages],
  );

  const [activeConversationId, setActiveConversationId] = useState<
    string | null
  >(conversationWithMessages?.id || null);

  useEffect(() => {
    const currentConversationId = conversationWithMessages?.id || null;
    const currentReceiverProfileId = receiverProfile?.id || null;

    if (
      currentConversationId !== conversationIdRef.current ||
      currentReceiverProfileId !== receiverProfileIdRef.current
    ) {
      setMessages(conversationWithMessages?.messages || []);
      setPage(currentPage);
      setAllMessagesLoaded(!hasMore);
      initialScrollDone.current = false;
      conversationIdRef.current = currentConversationId;
      receiverProfileIdRef.current = currentReceiverProfileId;

      setIsLoading(false);

      setActiveConversationId(currentConversationId);

      if (currentConversationId) {
        setEnrichedConversationsState((prev) =>
          prev.map((conv) => {
            if (conv.id === currentConversationId && conv.lastMessage) {
              return {
                ...conv,
                lastMessage: {
                  ...conv.lastMessage,
                  readAt: new Date(),
                },
              };
            }
            return conv;
          }),
        );
      }
    }
  }, [
    conversationWithMessages?.id,
    conversationWithMessages?.messages,
    currentPage,
    hasMore,
    receiverProfile?.id,
    setEnrichedConversationsState,
    setMessages,
  ]);

  const loadMoreMessages = useCallback(() => {
    if (isLoading || allMessagesLoaded || !conversationWithMessages?.id) return;

    setIsLoading(true);
    const nextPage = page + 1;

    if (chatContainerRef.current) {
      previousScrollHeightRef.current = chatContainerRef.current.scrollHeight;
    }

    requestAnimationFrame(() => {
      fetcher.load(`${window.location.pathname}?page=${nextPage}&limit=15`);
    });
  }, [
    isLoading,
    allMessagesLoaded,
    conversationWithMessages?.id,
    page,
    fetcher,
  ]);

  useEffect(() => {
    if (fetcher.data && fetcher.state === 'idle') {
      const newMessages =
        (fetcher.data as { conversationWithMessages?: { messages: Message[] } })
          .conversationWithMessages?.messages || [];

      if (newMessages.length === 0) {
        setAllMessagesLoaded(true);
        setIsLoading(false);
        return;
      }

      const scrollContainer = chatContainerRef.current;
      const previousScrollHeight = previousScrollHeightRef.current;
      const previousScrollTop = scrollContainer?.scrollTop || 0;

      const currentConversationId = conversationWithMessages?.id;
      const filteredNewMessages = currentConversationId
        ? newMessages.filter(
            (msg: Message) => msg.conversationId === currentConversationId,
          )
        : [];

      isAdjustingScroll.current = true;

      setMessages((prevMessages) => {
        const existingIds = new Set(prevMessages.map((msg) => msg.id));
        const uniqueNewMessages = filteredNewMessages.filter(
          (msg: Message) => !existingIds.has(msg.id),
        );
        return [...uniqueNewMessages, ...prevMessages];
      });

      setPage((prevPage) => prevPage + 1);
      setAllMessagesLoaded(!(fetcher.data as { hasMore?: boolean }).hasMore);
      setIsLoading(false);

      if (scrollContainer) {
        requestAnimationFrame(() => {
          const newScrollHeight = scrollContainer.scrollHeight;
          const heightDifference = newScrollHeight - previousScrollHeight;
          scrollContainer.scrollTop = previousScrollTop + heightDifference;

          requestAnimationFrame(() => {
            isAdjustingScroll.current = false;
          });
        });
      }
    }
  }, [fetcher.data, fetcher.state, conversationWithMessages?.id, setMessages]);

  useEffect(() => {
    if (chatContainerRef.current && !initialScrollDone.current) {
      const scrollToBottom = () => {
        if (chatContainerRef.current) {
          chatContainerRef.current.scrollTop =
            chatContainerRef.current.scrollHeight;
        }
      };

      scrollToBottom();

      requestAnimationFrame(scrollToBottom);
      initialScrollDone.current = true;
    } else if (
      chatContainerRef.current &&
      lastMessageId &&
      !isAdjustingScroll.current
    ) {
      const isUserMessage =
        messages.length > 0 &&
        messages[messages.length - 1]?.senderProfileId === profile.id;

      if (isUserMessage) {
        requestAnimationFrame(() => {
          if (chatContainerRef.current) {
            chatContainerRef.current.scrollTop =
              chatContainerRef.current.scrollHeight;
          }
        });
      } else {
        const { scrollTop, scrollHeight, clientHeight } =
          chatContainerRef.current;
        const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100;

        if (isNearBottom) {
          chatContainerRef.current.scrollTop =
            chatContainerRef.current.scrollHeight;
        }
      }
    }
  }, [lastMessageId, messages, profile.id]);

  useEffect(() => {
    let observer: IntersectionObserver | null = null;

    if (loadingRef.current && !allMessagesLoaded) {
      observer = new IntersectionObserver(
        (entries) => {
          if (entries[0]?.isIntersecting && !isLoading && !allMessagesLoaded) {
            const scrollContainer = chatContainerRef.current;
            if (scrollContainer) {
              previousScrollHeightRef.current = scrollContainer.scrollHeight;
            }

            loadMoreMessages();
          }
        },
        {
          rootMargin: '200px 0px 0px 0px',
          threshold: 0.1,
        },
      );

      observer.observe(loadingRef.current);
    }

    return () => {
      if (observer) {
        observer.disconnect();
      }
    };
  }, [
    loadMoreMessages,
    isLoading,
    allMessagesLoaded,
    conversationWithMessages?.id,
  ]);

  useEffect(() => {
    if (conversationWithMessages?.id) setAllMessagesLoaded(!hasMore);
  }, [conversationWithMessages?.id, hasMore]);

  useEffect(() => {
    if (
      allIncomingMessages.length > 0 &&
      enrichedConversationsState.length > 0
    ) {
      const latestMessagesByConversation = allIncomingMessages.reduce<
        Record<string, (typeof allIncomingMessages)[0]>
      >((acc, message) => {
        const conversationId = message.conversationId;

        if (
          !acc[conversationId] ||
          new Date(message.createdAt) > new Date(acc[conversationId].createdAt)
        ) {
          acc[conversationId] = message;
        }

        return acc;
      }, {});

      setEnrichedConversationsState((prevConversations) =>
        prevConversations.map((conversation) => {
          const latestMessage = latestMessagesByConversation[conversation.id];

          if (
            latestMessage &&
            (!conversation.lastMessage ||
              new Date(latestMessage.createdAt) >
                new Date(conversation.lastMessage.createdAt))
          ) {
            const isActiveConversation =
              conversation.id === activeConversationId;
            const isFromCurrentUser =
              latestMessage.senderProfileId === profile.id;

            return {
              ...conversation,
              lastMessage: {
                ...latestMessage,
                readAt:
                  isActiveConversation && !isFromCurrentUser
                    ? new Date()
                    : latestMessage.readAt,
              },
              updatedAt: latestMessage.createdAt,
            };
          }

          return conversation;
        }),
      );
    }
  }, [
    allIncomingMessages,
    enrichedConversationsState.length,
    activeConversationId,
    profile.id,
    setEnrichedConversationsState,
  ]);

  useEffect(() => {
    setIsBlocked(receiverProfile.isBlocked);
  }, [receiverProfile.isBlocked]);

  const { mutateAsync: blockMutation } = useMutation({
    mutationFn: async (shouldBlock: boolean) => {
      if (shouldBlock) {
        return apiPost<{ message: string }>(
          `/api/profile/${receiverProfile.id}/block`,
        );
      }
      return apiDelete<{ message: string }>(
        `/api/profile/${receiverProfile.id}/block`,
      );
    },
    onMutate: async () => {
      const previousState = isBlocked;
      setIsBlocked(!isBlocked);
      return { previousState };
    },
    onError: (err, variables, context) => {
      if (context?.previousState !== undefined) {
        setIsBlocked(context.previousState);
      }
      showToast.error('Something went wrong');
    },
    onSuccess: (data) => {
      showToast.success(t(data.message));
      revalidator.revalidate();
    },
  });

  const handleBlock = useCallback(() => {
    void blockMutation(!isBlocked);
  }, [blockMutation, isBlocked]);

  return (
    <div className="flex w-full min-h-[500px] h-[100dvh] max-h-screen bg-[#F8F9FC] p-0 lg:p-5 overflow-x-hidden">
      {!isMobile && (
        <div className="hidden lg:block w-[280px] lg:w-[320px] xl:w-[380px] h-full">
          <ConversationsContainer
            conversations={enrichedConversationsState}
            currentUserId={profile.id}
            activeUsername={receiverProfile.username}
          />
        </div>
      )}

      <div className="flex-1 flex flex-col h-full min-h-0 min-w-0 bg-white rounded-none lg:rounded-[24px] lg:ml-5">
        <ChatHeader
          avatar={receiverProfile.mainPhoto?.smallUrl}
          name={receiverProfile.firstName}
          userName={receiverProfile.username}
          status={receiverProfile.onlineStatus}
          onBlock={handleBlock}
          isBlocked={isBlocked}
        />

        <div className="flex-1 flex flex-col min-h-0">
          <div
            ref={chatContainerRef}
            className="flex-1 overflow-y-auto overflow-x-hidden space-y-2 sm:space-y-3 md:space-y-5 py-3 px-2 sm:px-3 md:px-4 lg:px-6"
          >
            <div
              ref={loadingRef}
              className="h-10 flex items-center justify-center"
            >
              {isLoading && (
                <div className="animate-spin h-5 w-5 border-2 border-[#E0455B] border-t-transparent rounded-full" />
              )}
            </div>

            {messages.map((message, index) => {
              const isLastSenderMessage =
                index === messages.length - 1 ||
                messages[index + 1]?.senderProfileId !==
                  message.senderProfileId;

              const isLastMessage =
                message.senderProfileId === profile.id && isLastSenderMessage;

              return (
                <ChatMessage
                  key={message.id}
                  message={message}
                  senderProfile={{
                    ...profile,
                    parentPhotos: [],
                  }}
                  receiverProfile={{
                    ...receiverProfile,
                    parentPhotos: [],
                  }}
                  isLastMessage={isLastMessage}
                />
              );
            })}
          </div>

          <div className="flex-shrink-0 w-full">
            <ChatForm
              conversationWithMessages={conversationWithMessages}
              senderProfile={{
                ...profile,
                parentPhotos: [],
              }}
              receiverProfile={{
                ...receiverProfile,
                parentPhotos: [],
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
