import * as Dialog from '@radix-ui/react-dialog';
import * as Scroll<PERSON><PERSON> from '@radix-ui/react-scroll-area';
import { useNavigate, useRevalidator } from '@remix-run/react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { BsFillArrowThroughHeartFill } from 'react-icons/bs';
import { FaTimes } from 'react-icons/fa';
import { IoPaperPlane } from 'react-icons/io5';
import { Button } from '#app/components/ui/button';
import Vignette from '#app/components/vignette';
import { type SerializedFullProfile } from '#app/types/serialized-types';
import {
  apiGet,
  apiPost,
  apiDelete,
  handleApiCall,
} from '#app/utils/fetch.client';
import { ProfilePhotoGallery } from './profile-photo-gallery';
import ProfileView from './profile-view';

interface ProfileViewWithLoadingProps {
  profileId: string;
  open: boolean;
  onOpenChange?: (isOpen: boolean) => void;
}

const fetchProfile = async (
  profileId: string,
): Promise<SerializedFullProfile> => {
  const [blockStatus, profile, likeStatus] = await Promise.all([
    apiGet<{ isBlocked: boolean }>(`/api/profile/${profileId}/block/status`),
    apiGet<SerializedFullProfile>(`/api/profile/${profileId}/full`),
    apiGet<{ likesThisProfile: boolean }>(
      `/api/profile/${profileId}/like/status`,
    ),
  ]);

  if (blockStatus.isBlocked) {
    throw new Error('This profile is not available.');
  }

  return {
    ...profile,
    likesThisProfile: likeStatus.likesThisProfile,
    isBlocked: blockStatus.isBlocked,
  };
};

const ProfileViewWithLoading: React.FC<ProfileViewWithLoadingProps> = ({
  profileId,
  open,
  onOpenChange,
}) => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const revalidator = useRevalidator();
  const navigate = useNavigate();

  const { data: fullProfile } = useQuery<SerializedFullProfile, Error>({
    queryKey: ['serializedFullProfile', profileId],
    queryFn: () => fetchProfile(profileId),
    enabled: open,
    staleTime: 5 * 60 * 1000,
    retry: false,
  });

  const likeMutation = useMutation({
    mutationFn: async () => {
      if (fullProfile?.likesThisProfile) {
        return apiDelete(`/api/profile/${profileId}/like`);
      } else {
        return apiPost(`/api/profile/${profileId}/like`, { _: null });
      }
    },
    onError: () => {
      void queryClient.invalidateQueries({
        queryKey: ['serializedFullProfile', profileId],
      });
    },
    onSuccess: () => {
      void queryClient.invalidateQueries({
        queryKey: ['serializedFullProfile', profileId],
      });
      revalidator.revalidate();
    },
  });

  const handleLike = useCallback(() => {
    void handleApiCall(likeMutation.mutateAsync());
  }, [likeMutation]);

  const handleClose = useCallback(() => {
    onOpenChange?.(false);
  }, [onOpenChange]);

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black bg-opacity-50 z-[98]" />
        <Dialog.Content
          onClick={(e) => e.stopPropagation()}
          className="fixed top-1/2 left-1/2 z-[99] lg:h-auto h-[90%] rounded-lg max-w-lg lg:max-w-4xl w-full flex items-center transform -translate-x-1/2 -translate-y-1/2"
        >
          <div className="mx-4 relative h-full lg:h-[500px] overflow-auto lg:overflow-hidden w-full flex flex-col lg:flex-row rounded-2xl bg-white shadow-md">
            <div className="absolute right-2 top-2 z-[99] h-full">
              <Button
                onClick={handleClose}
                size={'icon'}
                className=" text-black sm:bg-gray-400 lg:bg-transparent bg-gray-400"
                variant={'link'}
              >
                <FaTimes className="text-sm " />
              </Button>
            </div>
            <div className="h-full bg-black lg:w-[50%]">
              <ProfilePhotoGallery
                onlineStatus={fullProfile?.onlineStatus}
                photos={fullProfile?.galleryPhotos || []}
              />
            </div>

            <div className="relative lg:w-[50%]">
              <ScrollArea.Root>
                <ScrollArea.Viewport className="relative lg:h-[500px] px-5 pt-5 pb-[5.5rem]">
                  {fullProfile && (
                    <ProfileView
                      isPopup
                      profile={fullProfile}
                      recommendedProfiles={[]}
                    />
                  )}
                </ScrollArea.Viewport>

                <ScrollArea.Scrollbar
                  className="ScrollAreaScrollbar"
                  orientation="vertical"
                >
                  <ScrollArea.Thumb className="ScrollAreaThumb" />
                </ScrollArea.Scrollbar>

                <ScrollArea.Scrollbar
                  className="ScrollAreaScrollbar"
                  orientation="horizontal"
                >
                  <ScrollArea.Thumb className="ScrollAreaThumb" />
                </ScrollArea.Scrollbar>

                <ScrollArea.Corner className="ScrollAreaCorner" />
              </ScrollArea.Root>

              <div className="flex justify-center gap-2 lg:px-5 px-4 z-[2] w-full bg-card py-3 lg:py-4 absolute bottom-0">
                <Button
                  className="gap-1.5 w-full lg:max-w-[150px]"
                  variant={'outline-secondary'}
                  onClick={() => navigate(`/messages/${fullProfile?.username}`)}
                >
                  <IoPaperPlane /> {t('Message')}
                </Button>
                <Button
                  className="gap-1.5 w-full lg:max-w-[150px]"
                  onClick={handleLike}
                  disabled={likeMutation.isPending}
                >
                  <BsFillArrowThroughHeartFill
                    color={
                      fullProfile?.likesThisProfile ? 'red' : 'currentColor'
                    }
                  />
                  {fullProfile?.likesThisProfile ? t('Unlike') : t('Like')}
                </Button>
              </div>

              <Vignette className="z-[1] absolute h-[70px] lg:h-[80px] bottom-0 w-full left-0" />
            </div>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default ProfileViewWithLoading;
