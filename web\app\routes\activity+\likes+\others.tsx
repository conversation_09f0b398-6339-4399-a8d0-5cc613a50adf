import { useLoaderData } from '@remix-run/react';
import { useTranslation } from 'react-i18next';
import { ActivityLayout } from '#app/components/activity/activity-layout';
import { ProfileResult } from '#app/components/search/profile-result';
import { getLikesCounts } from '#app/utils/count.server.js';
import { handleLoaderForProfileRoute } from '#app/utils/remix-helpers.server';
import { prisma } from '#server/utils/db.server';
import { getProfileCardInfoFromProfileIds } from '#server/utils/profile.server';

export const loader = handleLoaderForProfileRoute(async ({ profile }) => {
  const [myLikes, likesMe, mutualLikes] = await getLikesCounts(profile.id);

  const profilesWhoIlike = await prisma.profileLike.findMany({
    where: { likerProfileId: profile.id },
    include: { likedProfile: { select: { id: true } } },
  });

  const profileIds = profilesWhoIlike.map((p) => p.likedProfile.id);
  const profiles = await getProfileCardInfoFromProfileIds({
    profileIds,
    viewerProfileId: profile.id,
  });

  return {
    profiles,
    counts: { myLikes, likesMe, mutualLikes },
  };
});

export default function MyLikes() {
  useTranslation();
  const { profiles, counts } = useLoaderData<typeof loader>();
  return (
    <ActivityLayout counts={counts}>
      {profiles.map((profile) => {
        return (
          <ProfileResult
            key={profile.id}
            profile={{
              ...profile,
              mainPreviewPhoto: profile.mainPhoto,
              likesThisProfile: profile.likesThisProfile ?? false,
            }}
          />
        );
      })}
    </ActivityLayout>
  );
}
