type StepProps = {
  isStepDone?: boolean;
  isCurrentStep?: boolean;
  onClick?: () => void;
}

function StepIndicator({
  isStepDone,
  isCurrentStep,
}: StepProps) {
  return (
    <div
      className={`transition-all duration-250 size-[10px] ${isStepDone || isCurrentStep ? 'bg-[#D33544]': 'bg-[#FFE4E4]'} ${isCurrentStep ? 'w-[80px]' : 'w-[10px]'} rounded-full`}
    >
    </div>
  );
}

type StepBuilderProps = {
  steps: number;
  currentStep: number;
}

export function StepBuilder({
  steps,
  currentStep,
}: StepBuilderProps){
  
  return (
    <div className="flex gap-2">
      {
        Array.from({ length: steps }).map((_, index) => (
          <StepIndicator
            key={index}
            isStepDone={index <= currentStep}
            isCurrentStep={index === currentStep}
          />
        ))
      }
    </div>
  )
}
