import React from 'react';
import { type useController } from 'react-hook-form';
import { ErrorList } from '#app/components/forms/helpers/error-list';
import { Label } from '#app/components/forms/helpers/label';
import {
  AgeRangeSlider,
  AgeSelect,
  Email,
  GenderSelect,
  GenericMultiSelect,
  GenericSelect,
  HeightSelect,
  HiddenInput,
  Input,
  Password,
  PhoneNumber,
  PhotoUpload,
  Textarea,
  VideoUpload,
  WeightSelect,
  AttachmentUpload, // Add this import
  ChatInput,
  GenderMultiCheckbox,
  GenericMultiCheckbox,
} from '#app/components/forms/inputs';
import { Combobox } from '#app/components/forms/inputs/combobox';
import { DatePicker } from '#app/components/forms/inputs/date-picker';
import { GenderMultiSelect } from '#app/components/forms/inputs/gender-multi-select';
import { SegmentedToggle } from '#app/components/forms/inputs/segmented-toggle';
import { cn } from '#app/utils/misc';
import {
  type FieldFromConfigType,
  type ListOfErrors,
} from '#shared/types/forms';
import { RadioSelect } from '../inputs/radio-select';

// may need to add input props from the raw HTML types
type FieldProps<T> = FieldFromConfigType<T> &
  Omit<ReturnType<typeof useController>['field'], 'ref'> & {
    id?: string;
    form?: string;
    labelProps: React.LabelHTMLAttributes<HTMLLabelElement>;
    inputRef?: React.Ref<any>; // TODO: fix ref
    errors?: ListOfErrors;
    className?: string;
  };

function FieldFromConfig<T>({
  id,
  type,
  labelProps,
  errors,
  className,
  // default value comes from
  defaultValue: _defaultValue,
  ...rest
}: FieldProps<T>) {
  const errorId = errors?.length ? `${id}-error` : undefined;
  // Props for the input components
  const componentProps = {
    id,
    'aria-invalid': errorId ? true : undefined,
    'aria-describedby': errorId,
    ...rest,
  };
  // These inputs render their own label
  const strippedLabelInputs = ['height', 'weight'];

  // Render the correct input component based on the type
  const getInput = () => {
    switch (type) {
      case 'password':
        return <Password {...componentProps} />;
      case 'email':
        // need to make sure it has a default value
        componentProps.value = componentProps.value || '';
        return <Email {...componentProps} />;
      case 'gender-select':
        return <GenderSelect {...componentProps} />;
      case 'gender-multi-select':
        return <GenderMultiSelect {...componentProps} />;
      case 'age':
        return <AgeSelect {...componentProps} />;
      case 'text':
        return <Input {...componentProps} />;
      case 'photo-upload':
        return <PhotoUpload {...componentProps} />;
      case 'video-upload':
        return <VideoUpload {...componentProps} />;
      case 'hidden':
        return <HiddenInput {...componentProps} />;
      case 'textarea':
        return <Textarea {...componentProps} />;
      case 'height':
        return <HeightSelect labelProps={labelProps} {...componentProps} />;
      case 'weight':
        return <WeightSelect labelProps={labelProps} {...componentProps} />;
      case 'age-range-slider':
        return <AgeRangeSlider {...componentProps} />;
      case 'radio-select':
        return <RadioSelect {...componentProps} />;
      case 'date-picker':
        return <DatePicker {...componentProps} />;
      case 'select':
        // TODO: fix ignore
        // @ts-ignore: doesn't recognize the options prop
        return <GenericSelect {...componentProps} />;

      case 'segmented-toggle':
        // TODO: fix ignore
        // @ts-ignore: doesn't recognize the options prop
        return <SegmentedToggle {...componentProps} />;

      case 'multi-select':
        // TODO: fix ignore
        // @ts-ignore: doesn't recognize the options prop
        return <GenericMultiSelect {...componentProps} />;
      case 'phone-number':
        return <PhoneNumber {...componentProps} />;
      case 'attachment-upload':
        return <AttachmentUpload {...componentProps} />;
      case 'chat-input':
        return <ChatInput filesWithPreviews={[]} {...componentProps} />;
      case 'gender-multi-checkbox':
        return <GenderMultiCheckbox {...componentProps} />;
      case 'multi-checkbox':
        // TODO: fix ignore
        // @ts-ignore: doesn't recognize the options prop
        return <GenericMultiCheckbox {...componentProps} />;
      case 'combobox':
        // @ts-ignore: doesn't recognize the options prop
        return <Combobox {...componentProps} />;
      default:
        throw new Error(`Unsupported field type: ${type}`);
    }
  };

  const label =
    strippedLabelInputs.includes(type) || !labelProps.children ? null : (
      <Label
        htmlFor={id}
        {...labelProps}
        className={cn('mb-2 block', labelProps.className)}
      />
    );

  return (
    <div className={className}>
      {label}
      {getInput()}
      {errorId ? (
        <div className="px-2 pb-3 pt-1">
          <ErrorList id={errorId} errors={errors} />
        </div>
      ) : null}
    </div>
  );
}

export { FieldFromConfig };
