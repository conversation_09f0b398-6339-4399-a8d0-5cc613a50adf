import { UserType } from '@prisma/client';
import { type FastifyInstance, type FastifyRequest } from 'fastify';
import { UserNotAdminError } from '#server/utils/errors.server';
import { photoRouter } from './photos';

async function checkAdminMiddleware(request: FastifyRequest) {
  if (request.user.userType !== UserType.ADMIN) {
    throw new UserNotAdminError();
  }
}

export async function adminRouter(fastify: FastifyInstance) {
  fastify.addHook('preHandler', checkAdminMiddleware);
  fastify.register(photoRouter, { prefix: '/photos' });
}
