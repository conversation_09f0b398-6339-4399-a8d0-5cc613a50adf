import { createCookieSessionStorage } from '@remix-run/node';
import { SESSION_SECRET } from '#server/utils/session.server';

export const verifySessionStorage = createCookieSessionStorage({
  cookie: {
    name: 'en_verification',
    sameSite: 'lax', // CSRF protection is advised if changing to 'none'
    path: '/',
    httpOnly: true,
    maxAge: 60 * 10, // 10 minutes
    secrets: SESSION_SECRET,
    secure: process.env.NODE_ENV === 'production',
  },
});
