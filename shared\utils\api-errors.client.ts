import { type ClientValidationErrorType } from '#shared/types/errors.client';
// TODO: Standardize errors better with better names

// these are the errors that we raise at the api client layer
export class APIError extends Error {
  constructor(public errorType: string) {
    super(errorType);
  }
}

export class APIValidationError extends APIError {
  errorDetail: ClientValidationErrorType['errorDetail'];
  constructor(public errorJson: ClientValidationErrorType) {
    super(errorJson.errorType);
    this.errorDetail = errorJson.errorDetail;
  }
}

export class APINotFoundError extends APIError {
  constructor(message = 'NotFoundError') {
    super(message);
  }
}

export class APIInternalServerError extends APIError {
  constructor() {
    super('InternalServerError');
  }
}

export function isAPIError(error: any): error is APIError {
  return error instanceof APIError;
}
