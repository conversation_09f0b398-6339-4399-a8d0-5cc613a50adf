import { Link, useRevalidator } from '@remix-run/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import Typography from '#app/components/typography';
import { Avatar, AvatarFallback, AvatarImage } from '#app/components/ui/avatar';
import { Button } from '#app/components/ui/button';
import { type EnrichedProfileBlock } from '#app/types/serialized-types';
import { apiDelete } from '#app/utils/fetch.client';

interface BlockedUsersProps {
  blockedUsers: EnrichedProfileBlock[];
}

export function BlockedUsers({ blockedUsers }: BlockedUsersProps) {
  const queryClient = useQueryClient();
  const revalidator = useRevalidator();

  const { mutate: unblockMutation, isPending } = useMutation({
    mutationFn: (profileId: string) => {
      return apiDelete(`/api/profile/${profileId}/block`);
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: ['blockedUsers'] });
      revalidator.revalidate();
    },
  });

  return (
    <div className="space-y-4">
      <Typography variant="h4">Blocked Users</Typography>
      {blockedUsers.length === 0 ? (
        <Typography>You haven't blocked anyone.</Typography>
      ) : (
        <ul className="divide-y divide-gray-200">
          {blockedUsers.map((block) => (
            <li
              key={block.id}
              className="py-4 flex items-center justify-between"
            >
              <Link
                to={`/profile/${block.blockedProfile.username}`}
                className="flex items-center space-x-4"
              >
                <Avatar>
                  <AvatarImage src={block.blockedProfile.mainPhoto?.smallUrl} />
                  <AvatarFallback>
                    {block.blockedProfile.firstName.slice(0, 1)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <Typography variant="h6">
                    {block.blockedProfile.firstName}
                  </Typography>
                  <Typography variant="small" className="text-gray-500">
                    @{block.blockedProfile.username}
                  </Typography>
                </div>
              </Link>
              <Button
                variant="outline"
                onClick={() => unblockMutation(block.blockedProfile.id)}
                disabled={isPending}
              >
                Unblock
              </Button>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}
