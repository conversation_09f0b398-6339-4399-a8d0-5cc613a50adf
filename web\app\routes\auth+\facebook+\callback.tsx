import {
  FACEBOOK_SCOPES,
  getFacebookAccessToken,
  getFacebookProfileData,
} from '#app/utils/facebook.server';
import { loaderForSocialAuthCallback } from '#app/utils/social.server';
import { BadRequestError } from '#server/utils/errors.server.js';
import { authSessionStorage } from '#server/utils/session.server.js';
import { SocialProvider } from '#shared/constants/enums';

export const loader = loaderForSocialAuthCallback(
  SocialProvider.FACEBOOK,
  async ({ request, code }) => {
    const tokenData = await getFacebookAccessToken({
      code,
      requestUrl: request.url,
    });

    const { getSession } = authSessionStorage;
    const session = await getSession(request.headers.get('Cookie'));
    const storedState = session.get('oauth_state');
    const receivedState = new URLSearchParams(request.url).get('state');
    if (storedState !== receivedState) {
      throw new BadRequestError('Invalid state');
    }
    
    const receivedProvider = session.get('oauth_provider');
    if (receivedProvider !== SocialProvider.FACEBOOK) {
      throw new BadRequestError('Invalid state');
    }

    // generate the access token
    const profileData = await getFacebookProfileData({
      accessToken: tokenData.access_token,
    });
    const { email, id: providerId, first_name } = profileData;
    return {
      email,
      providerId,
      firstName: first_name,
      // no refresh token for facebook
      oAuthTokenData: {
        accessToken: tokenData.access_token,
        // Facebook does not return an expiry date.
        // Access tokens are valid for 60 days if made server side along with App Secret. See: https://developers.facebook.com/docs/facebook-login/guides/access-tokens/#termtokens
        expiryDate: new Date(Date.now() + (60 * 24 * 60 * 60 * 1000)),
        scope: FACEBOOK_SCOPES,
      },
    };
  },
);
