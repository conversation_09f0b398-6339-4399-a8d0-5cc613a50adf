// REMIX
import { useLoaderData } from '@remix-run/react';

// REACT
import { useTranslation } from 'react-i18next';

// COMPONENTS
import { FormFromConfig } from '#app/components/forms/form-from-config';

// UTILS
import { LayoutType } from '#app/types/layouts.js';
import { cn } from '#app/utils/misc.js';
import {
  handleFormActionForLoggedInUser,
  handleLoaderForLoggedInUser,
} from '#app/utils/remix-helpers.server';
import { t } from '#server/utils/api.server';
import { prisma } from '#server/utils/db.server';
import { ValidationError } from '#server/utils/errors.server';
import { type GenderEnum } from '#shared/constants/enums.js';
import { PROFILE_SETUP_BIO_ROUTE } from '#shared/constants/routes';
import { createProfileValidator } from '#shared/utils/validators.js';

export const handle = {layout: LayoutType.NONE}

const nextRoute = PROFILE_SETUP_BIO_ROUTE;

export const loader = handleLoaderForLoggedInUser(
  async ({ privateUser, profile }) => {
    // if (profile) {
    //   return redirect(PROFILE_SETUP_PHONE_ROUTE);
    // }
    const { username, firstName, gender, genderInterest } = profile ?? {};
    const { dateOfBirth } = privateUser ?? {};

    const socialConnections = await prisma.socialConnection.findMany({
      where: { privateUserId: privateUser.id },
    });

    const socialConnection = socialConnections.find(
      (connection) => !!connection.firstName,
    );

    return {
      initialValues: {
        username: socialConnection?.firstName ?? username,
        firstName: socialConnection?.firstName ?? firstName,
        gender: gender,
        genderInterest: genderInterest,
        dateOfBirth: dateOfBirth,
      },
    };
  },
);

export const action = handleFormActionForLoggedInUser(
  createProfileValidator,
  async ({ redirect, data, user, profile }) => {
    const existingUser = await prisma.profile.findFirst({
      where: { 
        username: { 
          equals: data.username, 
          mode: 'insensitive', 
        },
        NOT: {
          id: profile?.id,
        } 
      },
    });

    if (existingUser) {
      throw new ValidationError({
        username: [t('Username is already taken')],
      });
    }

    const { dateOfBirth, ...rest } = data;

    // Check if the user already has a profile
    // Update profile if it exists. Possible when user presses back button 
    // after continuing from this page
    if (profile) {
      await prisma.$transaction(async (tx) => {
        await tx.profile.update({
          where: { id: profile.id },
          data: {
            ...rest,
            user: { connect: { id: user.id } },
          },
        });
        await tx.privateUser.update({
          where: { id: user.privateUserId },
          data: { dateOfBirth },
        });
      });

      return redirect(nextRoute);
    }

    // Save the profile data
    await prisma.$transaction(async (tx) => {
      await tx.profile.create({
        data: {
          ...rest,
          user: { connect: { id: user.id } },
          genderInterest: data.genderInterest,
        },
      });
      await tx.privateUser.update({
        where: { id: user.privateUserId },
        data: { dateOfBirth },
      });
    });

    return redirect(nextRoute);
  },
);
export default function NewProfileRoute() {
  const { initialValues } = useLoaderData<typeof loader>();
  const { t } = useTranslation();

  const fields = [
    {
      name: 'firstName',
      type: 'text',
      label: t('First Name'),
      defaultValue: initialValues.firstName,
    },
    {
      name: 'username',
      type: 'text',
      label: t('Username'),
      defaultValue: initialValues.username,
    },
    {
      name: 'gender',
      type: 'gender-select',
      label: t('Gender'),
      defaultValue: initialValues.gender as GenderEnum,
    },
    {
      name: 'genderInterest',
      type: 'gender-multi-select',
      label: t('Gender Preference'),
      defaultValue: initialValues.genderInterest as GenderEnum[],
    },
    {
      name: 'dateOfBirth',
      type: 'date-picker',
      label: t('Date of Birth'),
      defaultValue: initialValues.dateOfBirth?.toDateString(),
      calendarProps: {
        disabled: (date: Date) => date > new Date(),
      },
    },
  ] as const;

  return (
    <>
      <div className="mb-6 md:mb-8">
        <h1
          className={cn(
            'text-3xl md:text-4xl font-extrabold text-gray-900',
            'dark:text-white md:tracking-tight',
          )}
        >
          {t('Start with the essentials')}
        </h1>
        <p className="mt-1 text-sm text-gray-500">
          {t('Every story starts somewhere — here\'s yours')}
        </p>
      </div>
      <FormFromConfig
        renderFields={({ fields }) => (
          <div className="flex h-full flex-col gap-6">
            <div className="space-y-6">
              {fields.firstName?.node}

              {fields.username?.node}

              <div className="space-y-6 lg:flex lg:gap-3 lg:space-y-0">
                <div className="w-full lg:w-1/2">
                  {fields.gender?.node}
                </div>

                <div className="w-full lg:w-1/2">
                  {fields.genderInterest?.node}
                </div>
              </div>

              {fields.dateOfBirth?.node}
            </div>

            <div className="flex justify-end mt-auto pt-8">
              <button
                type="submit"
                className={cn(
                  'w-[20%] rounded-lg bg-[#D33544] py-3 text-center font-medium text-white',
                  'transition-colors hover:bg-[#B82C3A] focus:outline-none focus:ring-2',
                  'focus:ring-[#D33544] focus:ring-offset-2',
                )}
              >
                Continue
              </button>
            </div>
          </div>
        )}
        method="POST"
        action="/profile/new"
        fields={fields}
        createValidator={createProfileValidator}
      />
    </>
  );
}
