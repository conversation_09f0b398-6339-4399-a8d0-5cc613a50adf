// apiRoutes.ts
import { type PrivateUser, type User } from '@prisma/client';
import { type FastifyInstance, type FastifyRequest } from 'fastify';
import { filesRouter } from '#server/files';
import { prisma } from '#server/utils/db.server';
import { UnauthorizedError } from '#server/utils/errors.server';
import { authSessionStorage } from '#server/utils/session.server';
import { SESSION_KEY } from '#shared/constants/auth';
import { adminRouter } from './admin';
import { geographyRouter } from './geography';
import { profileRouter } from './profile';

declare module 'fastify' {
  interface FastifyRequest {
    user: User;
    privateUser: PrivateUser;
  }
}
async function checkSessionMiddleware(request: FastifyRequest) {
  const authSession = await authSessionStorage.getSession(
    request.headers.cookie,
  );
  if (!authSession) {
    throw new UnauthorizedError();
  }
  const sessionId = authSession.get(SESSION_KEY);
  if (!sessionId) {
    throw new UnauthorizedError();
  }
  const session = await prisma.session.findUnique({
    select: { privateUser: { include: { user: true } } },
    where: { id: sessionId, expirationDate: { gt: new Date() } },
  });
  if (!session?.privateUser || !session.privateUser.user) {
    throw new UnauthorizedError();
  }
  request.privateUser = session.privateUser;
  request.user = session.privateUser.user;
}

export async function apiRouter(fastify: FastifyInstance) {
  // Middleware to check session
  fastify.addHook('preHandler', checkSessionMiddleware);

  fastify.register(adminRouter, { prefix: '/admin' });
  fastify.register(profileRouter, { prefix: '/profile' });
  fastify.register(geographyRouter, { prefix: '/geography' });
  fastify.register(filesRouter, { prefix: '/files' });
}
