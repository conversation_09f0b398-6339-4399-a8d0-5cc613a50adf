import { useLoaderD<PERSON>, useNavigate } from "@remix-run/react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { FaArrowLeftLong } from "react-icons/fa6";
import { z } from "zod";
import { FormFromConfig } from "#app/components/forms/form-from-config.js";
import { Button } from "#app/components/ui/button.js";
import { LayoutType } from "#app/types/layouts.js";
import { apiGet, handleApiCall } from "#app/utils/fetch.client.js";
import { cn } from "#app/utils/misc.js";
import { handleFormActionForProfile, handleLoaderForProfileRoute } from "#app/utils/remix-helpers.server.js";
import { prisma } from "#server/utils/db.server.js";
import { BadRequestError } from "#server/utils/errors.server.js";
import { getCountries, getGeocodeData, getRegionsOfCountry, sortCountriesByPriority } from "#server/utils/geography.js";
import { getCachedLocationDataForUser, validateAndCleanLocationData } from "#server/utils/ip-location.server.js";
import { PROFILE_SETUP_VERIFICATION_ROUTE } from "#shared/constants/routes.js";
import { type FieldsFromConfig } from "#shared/types/forms.js";
import { type ApiRegion } from "#shared/types/geography.js";
import { type TranslationFunction } from "#shared/types/translations.js";
import { createGenderInterestValidator } from "#shared/utils/validators.js";

const nextRoute = PROFILE_SETUP_VERIFICATION_ROUTE;

export const handle = { layout: LayoutType.NONE };

export const loader = handleLoaderForProfileRoute(
  async ({ profile, privateUser }) => {
    const countries = sortCountriesByPriority(getCountries());

    const [location, bio, cachedLocationData] = await Promise.all([
      prisma.location.findUnique({
        where: { profileId: profile.id },
      }),
      prisma.bio.findUnique({
        where: { profileId: profile.id },
      }),
      getCachedLocationDataForUser(privateUser),
    ]);
    
    // Determine the location data to use
    const finalLocation = !location && cachedLocationData
      ? await (async () => {
          const validatedLocationData = await validateAndCleanLocationData(cachedLocationData);
          
          return {
            countryIso2: validatedLocationData.countryCode || undefined,
            regionCode: validatedLocationData.regionCode || undefined,
            cityOrTown: validatedLocationData.city || undefined, // This will be undefined if city is invalid
            latitude: validatedLocationData.latitude || undefined,
            longitude: validatedLocationData.longitude || undefined,
          };
        })()
      : location;
    
    // Get regions for the determined location
    const regions = finalLocation?.countryIso2 
      ? getRegionsOfCountry(finalLocation.countryIso2)
      : [];

    const { tagline, aboutMe, occupation } = bio ?? {};
    
    return {
      countries,
      regions,
      location: finalLocation,
      tagline: tagline ?? undefined,
      aboutMe: aboutMe ?? undefined,
      occupation: occupation ?? undefined,
    };
  },
);
 
export const action = handleFormActionForProfile(
  setupBioValidator,
  async ({ redirect, profile, data }) => {
    const { country, region, cityOrTown, ...rest } = data;

    const combinedAddress = [cityOrTown, country, region]
      .filter((part) => part)
      .join(',');

    if (combinedAddress) {
      if (cityOrTown && !country) {
        throw new BadRequestError(
          'Country is required when adding city or town.',
        );
      }

      if (country && !cityOrTown) {
        throw new BadRequestError(
          'City or town is required when adding country.',
        );
      }
    }

    const { results } = await getGeocodeData(combinedAddress);
    const location = results?.[0];
    if (!location) {
      throw new BadRequestError('Invalid address');
    }

    const countryComponent = location.address_components.find((component) =>
      component.types.includes('country'),
    );

    if (!countryComponent || countryComponent.short_name !== country) {
      throw new BadRequestError('Invalid address');
    }

    await prisma.$transaction(async (tx) => {
      await Promise.all([
        tx.bio.upsert({
          where: { profileId: profile.id },
          update: rest,
          create: {
            ...rest,
            profile: {
              connect: { id: profile.id },
            },
          },
        }),
        tx.location.upsert({
          where: { profileId: profile.id },
          update: {
            countryIso2: country,
            regionCode: region,
            cityOrTown: cityOrTown,
            latitude: location.geometry.location.lat,
            longitude: location.geometry.location.lng,
          },
          create: {
            profileId: profile.id,
            countryIso2: country,
            regionCode: region ?? '',
            cityOrTown: cityOrTown ?? '',
            latitude: location.geometry.location.lat,
            longitude: location.geometry.location.lng,
          },
        }),
      ]);
    });

    return redirect(nextRoute);
  },
);

function setupBioValidator(t: TranslationFunction) {
  return z.object({
    tagline: z.string({ message: t('Tagline must be a string') }).optional(),
    aboutMe: z.string({ message: t('About Me must be a string') }).optional(),
    occupation: z
      .string({ message: t('Occupation must be a string') })
      .optional(),
    country: z.string().optional(),
    region: z.string().optional(),
    cityOrTown: z.string().optional(),
    genderInterest: createGenderInterestValidator(t).optional(),
  });
}

export default function SetupBioRoute() {
  const { t } = useTranslation();
  const {
    countries,
    regions: initialRegions,
    location,
    tagline,
    aboutMe,
    occupation,
  } = useLoaderData<typeof loader>();
  const [regions, setRegions] = useState<ApiRegion[]>(initialRegions);
  const navigate = useNavigate();

  const fields: FieldsFromConfig<typeof setupBioValidator> = [
    {
      name: 'tagline',
      label: t('Tagline'),
      type: 'text',
      defaultValue: tagline,
      placeholder: t('Write a quick intro'),
    },
    {
      name: 'aboutMe',
      label: t('About Me'),
      type: 'textarea',
      defaultValue: aboutMe,
      placeholder: t('Tell others a bit about you'),
    },
    {
      name: 'occupation',
      label: t('Occupation'),
      type: 'text',
      defaultValue: occupation,
      placeholder: t('Enter your profession'),
    },
    {
      name: 'country',
      label: t('Country'),
      type: 'combobox',
      options: countries.map((country) => ({
        label: country.name,
        value: country.iso2,
      })),
      defaultValue: location?.countryIso2,
      placeholder: t('Select your country'),
      asyncOnChange: async (value) => {
        await handleApiCall(
          apiGet<{ regions: ApiRegion[] }>(
            `/api/geography/countries/${value}/regions`,
          ),
          {
            onApiSuccess: (data) => setRegions(data.regions),
          },
        );
      },
    },
    {
      name: 'region',
      label: t('Region'),
      type: 'combobox',
      defaultValue: location?.regionCode,
      options: regions.map((region) => ({
        label: region.name,
        value: region.regionCode,
      })),
      disabled: regions.length === 0,
      placeholder: t('Select your region'),
    },
    {
      name: 'cityOrTown',
      label: t('City or town'),
      type: 'text',
      defaultValue: location?.cityOrTown,
      placeholder: t('Select your city or town'),
    },
  ] as const;

  function handleClickBack() {
    if (window.history.length > 1) {
      window.history.back();
    }
  }

  function handleClickSkip() {
    console.log('skipping');
    navigate(nextRoute);
  }

  return (
    <>
      <div className="mb-6 md:mb-8">
        <h1
          className={cn(
            'text-3xl md:text-4xl font-extrabold text-gray-900',
            'dark:text-white md:tracking-tight',
          )}
        >
          {t("Let's build your profile")}
        </h1>
        <p className="mt-1 text-sm text-gray-500">
          {t(
            'Your job, your city, your words — just a few touches to bring your profile to life',
          )}
        </p>
      </div>
      <FormFromConfig
        fields={fields}
        method="POST"
        action="/profile/setup/bio"
        renderFields={({ fields }) => (
          <div className="flex flex-col gap-6">
            {fields.tagline?.node}
            {fields.aboutMe?.node}
            {fields.occupation?.node}
            <div className="flex gap-6">
              <div className="w-full">{fields.country?.node}</div>
              <div className="w-full">{fields.region?.node}</div>
              <div className="w-full">{fields.cityOrTown?.node}</div>
            </div>
            <div className="flex justify-between mt-auto pt-8">
              <Button
                onClick={handleClickBack}
                className="text-black"
                variant="link"
              >
                <FaArrowLeftLong className="mr-2" />
                Back
              </Button>
              <button
                type="submit"
                className={cn(
                  'w-[20%] rounded-lg bg-[#D33544] py-3 text-center font-medium text-white',
                  'transition-colors hover:bg-[#B82C3A] focus:outline-none focus:ring-2',
                  'focus:ring-[#D33544] focus:ring-offset-2',
                )}
              >
                Next
              </button>
            </div>
            <div className="flex justify-end">
              <Button
                onClick={handleClickSkip}
                variant="link"
                className="w-[20%]"
                type="button"
              >
                Skip for now
              </Button>
            </div>
          </div>
        )}
        createValidator={setupBioValidator}
      />
    </>
  );
}
