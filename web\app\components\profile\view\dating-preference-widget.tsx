import { type DatingPreference } from '@prisma/client';
import { type TFunction } from 'i18next';
import { useTranslation } from 'react-i18next';
import {
  IoSearchOutline,
  IoShirtOutline,
  IoWineOutline,
} from 'react-icons/io5';
import { PiCigarette } from 'react-icons/pi';
import { IconDetailItem } from '#app/components/profile/view';
import Typography from '#app/components/typography';
import {
  getBodyTypeLabelFromEnum,
  getNeverSometimesOftenLabelFromEnum,
  getRelationshipLabelFromEnum,
} from '#shared/utils/form-options';

type RequiredDatingPreference = Required<
  Omit<DatingPreference, 'id' | 'createdAt' | 'updatedAt' | 'profileId'>
>;

type Prop = {
  datingPreference: RequiredDatingPreference | null;
};

const DatingPreferenceMap = (
  datingPreference: RequiredDatingPreference,
  t: TFunction,
) => ({
  relationshipPreference: datingPreference.relationshipPreference.length >
    0 && (
    <IconDetailItem
      containerClassName="bg-white px-3 py-2 rounded-xl"
      icon={<IoSearchOutline fontSize={18} />}
      label={datingPreference.relationshipPreference
        .map((pref) => getRelationshipLabelFromEnum(pref, t))
        .join(', ')}
    />
  ),
  drinking: datingPreference.drinking.length > 0 && (
    <IconDetailItem
      containerClassName="bg-white px-3 py-2 rounded-xl"
      icon={<IoWineOutline fontSize={18} />}
      label={datingPreference.drinking
        .map((drink) => getNeverSometimesOftenLabelFromEnum(drink, t))
        .join(', ')}
    />
  ),
  smoking: datingPreference.smoking.length > 0 && (
    <IconDetailItem
      containerClassName="bg-white px-3 py-2 rounded-xl"
      icon={<PiCigarette fontSize={18} />}
      label={datingPreference.smoking
        .map((smoke) => getNeverSometimesOftenLabelFromEnum(smoke, t))
        .join(', ')}
    />
  ),
  bodyTypes: datingPreference.bodyTypes.length > 0 && (
    <IconDetailItem
      containerClassName="bg-white px-3 py-2 rounded-xl"
      icon={<IoShirtOutline fontSize={18} />}
      label={datingPreference.bodyTypes
        .map((bodyType) => getBodyTypeLabelFromEnum(bodyType, t))
        .join(', ')}
    />
  ),
});

function DatingPreferenceWidget({ datingPreference }: Prop) {
  const { t } = useTranslation();

  const userDatingPreference: RequiredDatingPreference = datingPreference ?? {
    relationshipPreference: [],
    drinking: [],
    smoking: [],
    bodyTypes: [],
    lookingFor: 'ANY',
    minAge: 18,
    maxAge: 100,
  };

  return (
    <div>
      <Typography variant="h5" className="font-medium mb-2 !leading-none">
        {t('Looking for')}
      </Typography>

      <div className="bg-gray-100 px-3 py-3 flex flex-wrap gap-2 rounded-xl">
        {Object.entries(DatingPreferenceMap(userDatingPreference, t)).map(
          ([key, value]) => value && <div key={key}>{value}</div>,
        )}
      </div>
    </div>
  );
}

export { DatingPreferenceWidget };
