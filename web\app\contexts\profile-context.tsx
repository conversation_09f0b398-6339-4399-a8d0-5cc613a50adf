import React, {
  createContext,
  useContext,
  useState,
  type ReactNode,
} from 'react';
import { type RootProfile } from '#app/types/serialized-types';

interface ProfileContextType {
  profile?: RootProfile;
  setProfile: React.Dispatch<React.SetStateAction<RootProfile | undefined>>;
}

const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

export const ProfileProvider: React.FC<{
  children: ReactNode;
  initialProfile?: RootProfile;
}> = ({ children, initialProfile }) => {
  const [profile, setProfile] = useState<RootProfile | undefined>(
    initialProfile,
  );

  return (
    <ProfileContext.Provider value={{ profile, setProfile }}>
      {children}
    </ProfileContext.Provider>
  );
};

export const useProfile = (): ProfileContextType => {
  const context = useContext(ProfileContext);
  if (context === undefined) {
    throw new Error('useProfile must be used within a ProfileProvider');
  }
  return context;
};
