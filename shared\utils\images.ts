export async function getImageDimensions(
  file: File,
): Promise<{ width: number; height: number }> {
  return await new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({
        width: img.width,
        height: img.height,
      });
    };
    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Creates a hash for parsing through ```DimensionStringToVariantType```,
 * which maps dimension strings to their variant types.
 * @see DimensionStringToVariantType
 */
export function createDimensionStringHash({
  height,
  width,
}: {
  height: number;
  width: number;
}) {
  return `${height}x${width}`;
}
