import { type FastifyInstance } from 'fastify';
import { t } from '#server/utils/api.server';
import { prisma } from '#server/utils/db.server';
import { handleFastifyRoute } from '#server/utils/fastify-helpers.server';
import { createProfileLikeNotification } from '#server/utils/profile.server';

// TODO: change from like to likes
export async function likeProfileRouter(fastify: FastifyInstance) {
  fastify.post(
    '/:profileId/like',
    handleFastifyRoute(async ({ request, sendSuccessMessage }) => {
      const { profile, likedProfile } = request;
      const existingLike = await prisma.profileLike.findFirst({
        where: {
          likerProfileId: profile.id,
          likedProfileId: likedProfile.id,
        },
      });
      if (!existingLike) {
        const profileLike = await prisma.profileLike.create({
          data: {
            likerProfileId: profile.id,
            likedProfileId: likedProfile.id,
          },
        });
        // kick off in background
        createProfileLikeNotification({ profileLike, likedProfile }).catch(
          console.error,
        );
      }
      sendSuccessMessage(t('Profile Liked'));
    }),
  ),
    fastify.delete(
      '/:profileId/like',
      handleFastifyRoute(async ({ request, sendSuccessMessage }) => {
        const { profile, likedProfile } = request;
        const existingLike = await prisma.profileLike.findFirst({
          where: {
            likerProfileId: profile.id,
            likedProfileId: likedProfile.id,
          },
        });
        if (existingLike) {
          await prisma.profileLike.delete({
            where: {
              id: existingLike.id,
            },
          });
        }
        sendSuccessMessage(t('Profile unliked successfully.'));
      }),
    ),
    fastify.get(
      '/:profileId/like/status',
      handleFastifyRoute(async ({ request }) => {
        const { profile, likedProfile } = request;
        const existingLike = await prisma.profileLike.findFirst({
          where: {
            likerProfileId: profile.id,
            likedProfileId: likedProfile.id,
          },
        });
        return { likesThisProfile: !!existingLike };
      }),
    );
}
