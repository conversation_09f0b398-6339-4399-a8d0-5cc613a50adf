import { Link } from '@remix-run/react';
import { useTranslation } from 'react-i18next';
import { BsThreeDots } from 'react-icons/bs';
import { FaArrowLeftLong, FaRegUser } from 'react-icons/fa6';
import { MdBlockFlipped } from 'react-icons/md';
import Typography from '#app/components/typography';
import { Avatar, AvatarFallback, AvatarImage } from '#app/components/ui/avatar';
import { buttonVariants } from '#app/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '#app/components/ui/dropdown-menu';
import { cn } from '#app/utils/misc.js';
import { type OnlineStatusWithTime } from '#shared/types/profile';
import { formatLastOnlineTime } from '#shared/utils/last-online-formatter';
import { getUserTimeZone } from '#shared/utils/timezone';

interface Props {
  name: string;
  avatar?: string;
  userName: string;
  status?: OnlineStatusWithTime;
  onBlock: () => void;
  isBlocked: boolean;
}

const ChatHeader = ({
  name,
  avatar,
  userName,
  status,
  onBlock,
  isBlocked,
}: Props) => {
  const userTimeZone = getUserTimeZone();
  const { t } = useTranslation();

  return (
    <div className="flex items-center justify-between bg-white w-full lg:rounded-tr-[1.5rem] lg:rounded-tl-[1.5rem] px-4 py-4 sm:px-6 sm:py-4 shadow-lg shadow-gray-100/50">
      <div className="flex items-center gap-3">
        <Link
          to={`/activity/conversations`}
          className={cn(
            buttonVariants({ size: 'sm', variant: 'link' }),
            'p-0 text-black lg:hidden',
          )}
        >
          <FaArrowLeftLong fontSize={18} />
        </Link>

        <div className="flex items-center gap-2">
          <Link
            to={`/profile/${userName}`}
            className="relative cursor-pointer hover:opacity-80 transition-opacity duration-200"
          >
            <Avatar className="h-12 w-12">
              <AvatarImage src={avatar} />
              <AvatarFallback>{name.slice(0, 1) || '?'}</AvatarFallback>
            </Avatar>

            {status?.onlineStatus === 'ONLINE' && (
              <div className="absolute bottom-0 right-0 h-3.5 w-3.5 rounded-full border-2 border-white bg-green-500" />
            )}
          </Link>

          <div>
            <Typography variant="h5">{name}</Typography>

            <Typography variant="small" className="text-gray-500">
              {status
                ? formatLastOnlineTime({
                    statusWithTime: status,
                    userTimeZone,
                    t,
                  })
                : ''}
            </Typography>
          </div>
        </div>
      </div>

      <DropdownMenu>
        <DropdownMenuTrigger
          className={cn(
            buttonVariants({ size: 'icon', variant: 'outline' }),
            'rounded-full h-8 w-8 lg:h-9 lg:w-9 p-0 border border-brand-primary text-brand-primary hover:text-brand-primary',
          )}
        >
          <BsThreeDots className="text-sm lg:text-lg" />
        </DropdownMenuTrigger>

        <DropdownMenuContent className="rounded-xl" align="end">
          <DropdownMenuItem>
            <Link
              className="flex items-center gap-1.5 text-brand-primary"
              to={`/profile/${userName}`}
            >
              <FaRegUser />
              <Typography variant="small">{t('View Profile')}</Typography>
            </Link>
          </DropdownMenuItem>

          <DropdownMenuItem
            className="flex items-center gap-1.5 text-brand-primary"
            onClick={onBlock}
          >
            <MdBlockFlipped />
            <Typography variant="small" className="text-brand-primary">
              {isBlocked ? t('Unblock') : t('Block')}
            </Typography>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default ChatHeader;
