import { createCookieSessionStorage } from '@remix-run/node';
import { SESSION_SECRET } from './session.server';

export const connectionSessionStorage = createCookieSessionStorage({
  cookie: {
    name: 'en_connection',
    sameSite: 'lax', // CSRF protection is advised if changing to 'none'
    path: '/',
    httpOnly: true,
    maxAge: 60 * 10, // 10 minutes
    secrets: SESSION_SECRET,
    secure: process.env.NODE_ENV === 'production',
  },
});
