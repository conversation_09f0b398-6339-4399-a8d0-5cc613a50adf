import { Link, useLocation } from '@remix-run/react';
import { useTranslation } from 'react-i18next';
import {
  IoNotificationsOutline,
  IoPauseCircleOutline,
  IoSettingsOutline,
} from 'react-icons/io5';
import { cn } from '#app/utils/misc';
import {
  SETTINGS_ACCOUNT_PRESENCE_ROUTE,
  SETTINGS_ACCOUNT_ROUTE,
  SETTINGS_CHANGE_PASSWORD_ROUTE,
  SETTINGS_NOTIFICATIONS_ROUTE,
} from '#shared/constants/routes';

interface SettingsLayoutProps {
  children: React.ReactNode;
}

const NavItem = ({
  route,
  title,
  icon,
  isActive,
  isSubItem = false,
  isClickable = true,
  showBorder = false,
}: {
  route: string;
  title: string;
  icon?: React.ReactNode;
  isActive: boolean;
  isSubItem?: boolean;
  isClickable?: boolean;
  showBorder?: boolean;
}) => {
  const content = (
    <div
      className={cn(
        'flex items-center gap-3 py-3 rounded-lg transition-colors relative',
        isSubItem ? 'pl-12 pr-4 ml-4' : 'px-4',
        isActive ? 'text-red-600' : 'text-gray-700 hover:bg-gray-50',
        showBorder &&
          'after:absolute after:top-0 after:-right-6 after:h-full after:w-2 after:bg-red-600 after:rounded-l-full',
      )}
    >
      {icon && <div className="flex-shrink-0">{icon}</div>}
      <span className="font-medium">{title}</span>
    </div>
  );

  if (!isClickable) {
    return content;
  }

  return (
    <Link to={route} className={cn(isActive && 'text-red-600')}>
      {content}
    </Link>
  );
};

export const SettingsLayout: React.FC<SettingsLayoutProps> = ({ children }) => {
  const { t } = useTranslation();
  const location = useLocation();

  // Determine which section is active
  const isAccountSettingsActive =
    location.pathname === SETTINGS_ACCOUNT_ROUTE ||
    location.pathname === SETTINGS_CHANGE_PASSWORD_ROUTE;

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <div className="flex gap-8">
        {/* Sidebar Navigation */}
        <div className="w-80 bg-white rounded-3xl shadow-sm p-6">
          <div className="space-y-1">
            {/* Account Settings Section */}
            <NavItem
              route={SETTINGS_ACCOUNT_ROUTE}
              title={t('Account settings')}
              icon={<IoSettingsOutline className="h-5 w-5" />}
              isActive={isAccountSettingsActive}
              showBorder={isAccountSettingsActive}
            />
            <NavItem
              route={SETTINGS_ACCOUNT_ROUTE}
              title={t('Account information')}
              isActive={location.pathname === SETTINGS_ACCOUNT_ROUTE}
              isSubItem={true}
            />
            <NavItem
              route={SETTINGS_CHANGE_PASSWORD_ROUTE}
              title={t('Change password')}
              isActive={location.pathname === SETTINGS_CHANGE_PASSWORD_ROUTE}
              isSubItem={true}
            />

            {/* Notifications Section */}
            <NavItem
              route={SETTINGS_NOTIFICATIONS_ROUTE}
              title={t('Notifications')}
              icon={<IoNotificationsOutline className="h-5 w-5" />}
              isActive={location.pathname === SETTINGS_NOTIFICATIONS_ROUTE}
              showBorder={location.pathname === SETTINGS_NOTIFICATIONS_ROUTE}
            />

            {/* Account Presence Section */}
            <NavItem
              route={SETTINGS_ACCOUNT_PRESENCE_ROUTE}
              title={t('Account presence')}
              icon={<IoPauseCircleOutline className="h-5 w-5" />}
              isActive={location.pathname === SETTINGS_ACCOUNT_PRESENCE_ROUTE}
              showBorder={location.pathname === SETTINGS_ACCOUNT_PRESENCE_ROUTE}
            />
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 bg-white rounded-3xl shadow-sm p-8">
          {children}
        </div>
      </div>
    </div>
  );
};
