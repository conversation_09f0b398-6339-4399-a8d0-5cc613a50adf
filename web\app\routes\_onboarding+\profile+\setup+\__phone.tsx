// This page is pathless. This page was made before the setup page was redesigned.
// Leaving this here in-case we want to re-add them.

import { useLoaderData, useNavigate } from "@remix-run/react";
import { useTranslation } from "react-i18next";
import { FaArrowLeftLong } from "react-icons/fa6";
import { FormFromConfig } from "#app/components/forms/form-from-config.js";
import { Button } from "#app/components/ui/button.js";
import { LayoutType } from "#app/types/layouts.js";
import { cn } from "#app/utils/misc.js";
import { handleFormActionForLoggedInUser, handleLoaderForLoggedInUser } from "#app/utils/remix-helpers.server.js";
import { prisma } from "#server/utils/db.server.js";
import { PROFILE_SETUP_BIO_ROUTE } from "#shared/constants/routes.js";
import { changePhoneNumberValidator } from "#shared/utils/validators.js";

const nextRoute = PROFILE_SETUP_BIO_ROUTE;

export const handle = {layout: LayoutType.NONE}

export const loader = handleLoaderForLoggedInUser(
  async ({ privateUser }) => {
    console.log('phone here')
    return {
      phoneNumber: privateUser.phoneNumber ?? undefined,
    };
  },
);
 
export const action = handleFormActionForLoggedInUser(
  changePhoneNumberValidator,
  async ({ redirect, data, privateUser }) => {
    const { phoneNumber, } = data;

    await prisma.privateUser.update({
      data: {
        phoneNumber,
      },
      where: {
        id: privateUser.id,
      },
    });

    return redirect(nextRoute);
  },
);

export default function SetupPhoneRoute() {
  const { phoneNumber } = useLoaderData<typeof loader>();
  const { t } = useTranslation();
  const navigate = useNavigate();

  const fields = [
    {
      name: 'phoneNumber',
      type: 'phone-number',
      label: t('Mobile Number'),
      defaultValue: phoneNumber,
    },
  ] as const;

  function handleClickBack() {
    if (window.history.length > 1) {
      window.history.back();
    } 
  }

  function handleClickSkip() {
    console.log('skipping');
    navigate(nextRoute);
  } 

  return (
    <>
      <div className="mb-6 md:mb-8">
        <h1
          className={cn(
            'text-3xl md:text-4xl font-extrabold text-gray-900',
            'dark:text-white md:tracking-tight',
          )}
        >
          {t('Don\'t miss a connection')}
        </h1>
        <p className="mt-1 text-sm text-gray-500">
          {t('Add your mobile number for SMS alerts on new messages')}
        </p>
      </div>
      <FormFromConfig
        fields={fields}
        method="POST"
        action="/profile/setup/phone"
        renderFields={({ fields }) => {
          return (
            <div className="flex flex-col gap-6">
              {fields.phoneNumber.node}
              <div className="flex justify-between mt-auto pt-8">
                <Button 
                  onClick={handleClickBack}
                  className="text-black"
                  variant='link'
                  type="button"
                >
                  <FaArrowLeftLong className="mr-2"/>
                  Back
                </Button>
                <button
                  type="submit"
                  className={cn(
                    'w-[20%] rounded-lg bg-[#D33544] py-3 text-center font-medium text-white',
                    'transition-colors hover:bg-[#B82C3A] focus:outline-none focus:ring-2',
                    'focus:ring-[#D33544] focus:ring-offset-2',
                  )}
                >
                  Next
                </button>
              </div>
              <div className="flex justify-end">
                <Button 
                  onClick={handleClickSkip}
                  variant="link"
                  className="w-[20%]"
                  type="button"
                >
                  Skip for now
                </Button>
              </div>
            </div>
          )
        }}
      />
    </>
  );
}