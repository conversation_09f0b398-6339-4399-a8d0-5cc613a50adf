@import url('https://fonts.googleapis.com/css2?family=Figtree:ital,wght@0,300..900;1,300..900&display=swap');

@import url('https://fonts.googleapis.com/css2?family=Libre+Bodoni:ital,wght@0,400..700;1,400..700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* --font-sans: here if you have one */
    /* --font-mono: here if you got it... */

    /* prefixed with foreground because it should look good on the background */
    --foreground-destructive: 345 82.7% 40.8%;

    --background: 220 100% 98.4%;
    --foreground: 222.2 84% 4.9%;

    --muted: 210 40% 93%;
    --muted-foreground: 215.4 16.3% 30%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --input-invalid: 0 84.2% 60.2%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 20% 83%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --accent: 210 40% 90%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 70% 50%;
    --destructive-foreground: 210 40% 98%;

    --ring: 215 20.2% 65.1%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    /* prefixed with foreground because it should look good on the background */
    --foreground-destructive: -4 84% 60%;

    --muted: 217.2 32.6% 12%;
    --muted-foreground: 215 20.2% 65.1%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --input-invalid: 0 62.8% 30.6%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 20% 24%;
    --secondary-foreground: 210 40% 98%;

    --accent: 217.2 32.6% 10%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 60% 40%;
    --destructive-foreground: 0 85.7% 97.3%;

    --ring: 217.2 32.6% 60%;
  }

  @layer utilities {
    /* Hide scrollbar for Chrome, Safari and Opera */
    .no-scrollbar::-webkit-scrollbar {
      display: none;
    }
    /* Hide scrollbar for IE, Edge and Firefox */
    .no-scrollbar {
      -ms-overflow-style: none; /* IE and Edge */
      scrollbar-width: none; /* Firefox */
    }

    .gradient-border {
      background:
        linear-gradient(white, white) padding-box,
        linear-gradient(90deg, #d33544, #2c80aa) border-box;
    }
  }

  .gradient-border {
    background:
      linear-gradient(white, white) padding-box,
      linear-gradient(90deg, #d33544, #2c80aa) border-box;
  }

  .gradient-border {
    background:
      linear-gradient(white, white) padding-box,
      linear-gradient(90deg, #d33544, #2c80aa) border-box;
  }
}
