import { HiOutlineUsers } from 'react-icons/hi2';
import {
  IoChatbubblesOutline,
  IoHeartOutline,
  IoSearch,
} from 'react-icons/io5';

import { type IconType } from 'react-icons/lib';
import {
  ACTIVITY_CONVERSATION_LIST_ROUTE,
  ACTIVITY_LIKES_ME_ROUTE,
  ACTIVITY_PROFILE_VIEWS_ROUTE,
  SEARCH_BASIC_ROUTE,
} from '#shared/constants/routes';

export const navigationItems: {
  label: string;
  path: string;
  icon: IconType;
}[] = [
  {
    label: 'Search',
    path: SEARCH_BASIC_ROUTE,
    icon: IoSearch,
  },
  {
    label: 'Messages',
    path: ACTIVITY_CONVERSATION_LIST_ROUTE,
    icon: IoChatbubblesOutline,
  },
  {
    label: 'Likes',
    path: ACTIVITY_LIKES_ME_ROUTE,
    icon: IoHeartOutline,
  },
  {
    label: 'Profile Views',
    path: ACTIVITY_PROFILE_VIEWS_ROUTE,
    icon: HiOutlineUsers,
  },
];
