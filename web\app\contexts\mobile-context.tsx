import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  type ReactNode,
} from 'react';
import { useMediaQuery } from 'react-responsive';

interface MobileContextType {
  isMobile: boolean;
  setIsMobile: React.Dispatch<React.SetStateAction<boolean>>;
}

const MobileContext = createContext<MobileContextType | undefined>(undefined);

export const MobileProvider: React.FC<{
  children: ReactNode;
  initialIsMobile: boolean;
}> = ({ children, initialIsMobile = false }) => {
  const [isMobile, setIsMobile] = useState<boolean>(initialIsMobile);
  // logic to determine if the user is on a mobile device
  // defaults to the value from the loader
  const isMobileFromQuery = useMediaQuery({ maxWidth: 768 });
  useEffect(() => {
    setIsMobile(isMobileFromQuery);
  }, [isMobileFromQuery]);

  return (
    <MobileContext.Provider value={{ isMobile, setIsMobile }}>
      {children}
    </MobileContext.Provider>
  );
};

export const useMobile = (): MobileContextType => {
  const context = useContext(MobileContext);
  if (context === undefined) {
    throw new Error('useMobile must be used within a MobileProvider');
  }
  return context;
};
