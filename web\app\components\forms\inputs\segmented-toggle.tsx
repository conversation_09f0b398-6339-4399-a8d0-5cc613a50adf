import React from 'react';
import { cn } from '#app/utils/misc';

interface Option<T extends string> {
  value: T;
  label: string;
}

interface SegmentedToggleProps<T extends string> {
  options: Option<T>[];
  value?: T;
  onChange: (value: T) => void;
  name: string;
  inputRef?: React.Ref<HTMLInputElement>;
}

export function SegmentedToggle<T extends string>({
  options,
  value,
  onChange,
  name,
  inputRef,
}: SegmentedToggleProps<T>) {
  return (
    <div className="flex w-full rounded-2xl border border-gray-200 overflow-hidden">
      {options.map((option, index) => (
        <button
          key={option.value}
          type="button"
          onClick={() => onChange(option.value)}
          className={cn(
            'flex-1 px-1.5 py-2 h-10 text-sm font-medium transition-all duration-50',
            value === option.value
              ? 'bg-brand-primary text-white shadow rounded-2xl'
              : 'text-gray-500 hover:text-gray-900',
            {
              'border-r':
                index !== options.length - 1 &&
                value !== options[index + 1]?.value,
            },
          )}
        >
          {option.label}
        </button>
      ))}
      <input type="hidden" name={name} value={value ?? ''} ref={inputRef} />
    </div>
  );
}
