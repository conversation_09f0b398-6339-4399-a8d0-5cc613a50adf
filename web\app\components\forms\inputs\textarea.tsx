import * as React from 'react';

import { cn } from '#app/utils/misc';

interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  inputRef?: React.Ref<HTMLTextAreaElement>;
}

const Textarea: React.FC<TextareaProps> = ({
  className,
  inputRef,
  value: rawValue,
  ...props
}) => {
  const value = rawValue ?? '';
  return (
    <textarea
      className={cn(
        'flex w-full min-h-[80px]',
        'rounded-xl border border-input bg-transparent',
        'px-4 py-2',
        'text-sm !placeholder:text-muted-foreground',
        'ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
        'disabled:cursor-not-allowed disabled:opacity-50 aria-[invalid]:border-input-invalid',
        className,
      )}
      ref={inputRef}
      value={value}
      {...props}
    />
  );
};
Textarea.displayName = 'Textarea';

export { Textarea };
