import { $Enums } from '@prisma/client';
import { v2 as cloudinary } from 'cloudinary';
import { type FastifyInstance } from 'fastify';
import { z } from 'zod';
import { t } from '#server/utils/api.server';
import {
  deleteFileFromCloudinary,
  uploadFileToCloudinary,
} from '#server/utils/cloudinary.server';
import { prisma } from '#server/utils/db.server.js';
import { BadRequestError, NotFoundError } from '#server/utils/errors.server';
import { handleFastifyRoute } from '#server/utils/fastify-helpers.server';
import {
  deletePhotoFromCloudandDB,
  transformImageAndUploadToR2AndDatabase,
} from '#server/utils/image.server.js';
import { createVideoValidator } from '#shared/utils/validators.js';

cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_sNAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

const fileTypeSchema = z.enum(['image', 'video']);

const createFileSchema = z
  .object({
    file: createVideoValidator(t),
    type: fileTypeSchema,
    path: z.string(),
    photoType: z.nativeEnum($Enums.PhotoType).optional(),
  })
  .superRefine((data, ctx) => {
    if (data.type === 'image' && !data.photoType) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'photoType is required when file type is image',
        path: ['photoType'],
      });
    }
  });

const deleteFileSchema = z.object({
  type: fileTypeSchema,
});

export async function filesRouter(fastify: FastifyInstance) {
  fastify.post(
    '/',
    handleFastifyRoute(async ({ request, sendSuccessMessage }) => {
      const formData = await request.formData();
      const formFile = formData.get('file') as File;
      const formType = formData.get('type');
      const formPath = formData.get('path');
      const formPhotoType = formData.get('photoType') ?? undefined;

      const body = createFileSchema.safeParse({
        file: formFile,
        type: formType,
        path: formPath,
        photoType: formPhotoType,
      });

      if (!body.success) {
        throw new BadRequestError(body.error.message);
      }

      const { file, type, path, photoType } = body.data;

      const profile = await prisma.profile.findUnique({
        where: {
          userId: request.user.id,
        },
      });

      if (!profile) {
        throw new NotFoundError('Profile does not exist');
      }

      if (type === 'video') {
        const { fileId, url, assetId } = await uploadFileToCloudinary({
          file,
          profileId: profile.id,
          subPath: path,
          resourceType: type,
        });
        return sendSuccessMessage('File uploaded', { fileId, url, assetId });
      }

      if (type === 'image') {
        if (!photoType) {
          throw new BadRequestError('Phototype is required for images');
        }

        await transformImageAndUploadToR2AndDatabase({
          file,
          profileId: profile.id,
          photoType: photoType,
        });

        return sendSuccessMessage('File uploaded');
      }
    }),
  );
  fastify.delete(
    '/:fileId',
    handleFastifyRoute(async ({ request, sendSuccessMessage }) => {
      const { fileId } = request.params as { fileId: string };
      const body = await request.body;

      const parsedBody = deleteFileSchema.safeParse(body);

      if (!parsedBody.success) {
        throw new BadRequestError(parsedBody.error.message);
      }

      const { type } = parsedBody.data;

      if (type === 'video') {
        await deleteFileFromCloudinary({ fileId, type });
      }

      if (type === 'image') {
        await deletePhotoFromCloudandDB({
          photoFileId: fileId,
        });
      }

      return sendSuccessMessage('File deleted', { fileId });
    }),
  );
}
