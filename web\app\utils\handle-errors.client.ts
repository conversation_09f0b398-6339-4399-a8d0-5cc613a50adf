import {
  type ClientValidationErrorType,
  type ClientBadRequestErrorType,
  type ClientInternalServerErrorType,
  type ClientFormErrorType,
  type ClientErrorType,
} from '#shared/types/errors.client';

// these are the errors we get from action data and not the api
export function isClientError(actionData: any): actionData is ClientErrorType {
  return actionData?.success === false;
}

export function isClientSuccess(actionData: any): actionData is {
  success: true;
  message: string;
  options?: { delayedRedirectTo?: string };
} {
  return actionData?.success === true;
}

export function isClientValidationError(
  error: ClientFormErrorType,
): error is ClientValidationErrorType {
  return error.errorType === 'ValidationError';
}

export function isClientBadRequestError(
  error: ClientFormErrorType,
): error is ClientBadRequestErrorType {
  return error.errorType === 'BadRequestError';
}

export function isClientInternalServerError(
  error: ClientFormErrorType,
): error is ClientInternalServerErrorType {
  return error.errorType === 'InternalServerError';
}
