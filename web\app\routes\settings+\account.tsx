import { Outlet } from '@remix-run/react';
import { SettingsLayout } from '#app/components/settings/settings-layout';
import { handleLoaderForLoggedInUser } from '#app/utils/remix-helpers.server';

export const loader = handleLoaderForLoggedInUser(async ({ privateUser }) => {
  return { privateUser };
});

export default function AccountSettings() {
  return (
    <div className="w-full">
      <SettingsLayout>
        <Outlet />
      </SettingsLayout>
    </div>
  );
}
