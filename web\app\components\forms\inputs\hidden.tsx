import * as React from 'react';
import { Input } from './input';

interface HiddenInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  inputRef?: React.Ref<HTMLInputElement>;
}

const HiddenInput = ({
  inputRef,
  defaultValue,
  ...props
}: HiddenInputProps) => {
  return (
    <Input type="hidden" inputRef={inputRef} value={defaultValue} {...props} />
  );
};

HiddenInput.displayName = 'HiddenInput';

export { HiddenInput };
