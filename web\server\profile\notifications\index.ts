import { type FastifyInstance } from 'fastify';
import { z } from 'zod';
import { t } from '#server/utils/api.server';
import { prisma } from '#server/utils/db.server';
import { NotFoundError } from '#server/utils/errors.server';
import { handleFastifyRoute } from '#server/utils/fastify-helpers.server';
import { markProfileAsOnline } from '#server/utils/profile.server';
import {
  NOTIFICATION_TYPE_MAP,
  NOTIFICATION_TYPE_LIST,
} from '#shared/constants/profile';
import { type NotificationType } from '#shared/types/profile';

const markAsReadSchema = z.object({
  notificationType: z.enum(NOTIFICATION_TYPE_LIST),
});

function markNotificationTypeAsRead(
  userId: string,
  notificationType: NotificationType,
) {
  const query = {
    where: {
      readAt: null,
      userId,
    },
    data: {
      readAt: new Date(),
    },
  };
  switch (notificationType) {
    case NOTIFICATION_TYPE_MAP.MESSAGE:
      return prisma.messageNotification.updateMany(query);
    case NOTIFICATION_TYPE_MAP.PROFILE_LIKE:
      return prisma.profileLikeNotification.updateMany(query);
    case NOTIFICATION_TYPE_MAP.PROFILE_VIEW:
      return prisma.profileViewNotification.updateMany(query);
  }
}

export async function notificationsRouter(fastify: FastifyInstance) {
  fastify.get(
    '/unread',
    handleFastifyRoute(async ({ request, sendSuccessPayload }) => {
      const { user, profile } = request;
      // TODO: improve typing
      const userOnline = (request.query as any)?.user_online === 'true';

      const messageNotificationsCount = await prisma.messageNotification.count({
        where: {
          userId: user.id,
          readAt: null,
        },
      });

      const profileLikeNotificationsCount =
        await prisma.profileLikeNotification.count({
          where: {
            userId: user.id,
            readAt: null,
          },
        });

      const profileViewNotificationsCount =
        await prisma.profileViewNotification.count({
          where: {
            userId: user.id,
            readAt: null,
          },
        });

      const unreadNotifications = {
        messageNotificationsCount,
        profileLikeNotificationsCount,
        profileViewNotificationsCount,
      };

      // kick off in background
      userOnline && markProfileAsOnline(profile.id).catch(console.error);

      sendSuccessPayload(unreadNotifications);
    }),
  );

  fastify.post(
    '/types/:notificationType/mark-as-read',
    handleFastifyRoute(async ({ request, sendSuccessMessage }) => {
      const result = markAsReadSchema.safeParse(request?.params);
      if (!result.success) {
        throw new NotFoundError();
      }
      const { notificationType } = result.data;
      await markNotificationTypeAsRead(request.user.id, notificationType);
      return sendSuccessMessage(t('Success'));
    }),
  );
}
