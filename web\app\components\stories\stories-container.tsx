import { useEffect, useRef, useState } from 'react';
import { FaAngleLeft, FaAngleRight } from 'react-icons/fa6';
import { Button } from '#app/components/ui/button';
import { ScrollArea, ScrollBar } from '#app/components/ui/scroll-area';
import Vignette from '#app/components/vignette';
import { useProfile } from '#app/contexts/profile-context.js';
import { type performStoriesSearch } from '#app/utils/stories.server.js';
import { AddStoryAvatar } from './add-story-avatar';
import { StoriesResult } from './stories-result';

interface StoriesContainerProps {
  stories: Awaited<ReturnType<typeof performStoriesSearch>>;
}

function StoriesContainer({ stories = [] }: StoriesContainerProps) {
  const { profile } = useProfile();
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const [selectedStoryIdx, setSelectedStoryIdx] = useState<number | null>(null);

  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const gapOffset = 12; // 12px = estimated gap

  const handleScrollRight = () => {
    const scrollArea = scrollAreaRef.current;
    const container = containerRef.current;

    if (scrollArea && container?.firstChild instanceof HTMLElement) {
      const itemWidth =
        container.firstChild.getBoundingClientRect().width + gapOffset;
      scrollArea.scrollBy({
        left: itemWidth,
        behavior: 'smooth',
      });
    }
  };

  const handleScrollLeft = () => {
    const scrollArea = scrollAreaRef.current;
    const container = containerRef.current;

    if (scrollArea && container?.firstChild instanceof HTMLElement) {
      const itemWidth = container.firstChild.getBoundingClientRect().width + 12;
      scrollArea.scrollBy({
        left: -itemWidth,
        behavior: 'smooth',
      });
    }
  };

  const updateScrollButtons = () => {
    const scrollArea = scrollAreaRef.current;
    if (!scrollArea) return;

    const { scrollLeft, scrollWidth, clientWidth } = scrollArea;

    setCanScrollLeft(scrollLeft > 0);
    setCanScrollRight(scrollLeft + clientWidth < scrollWidth - 1); // `-1` to account for minor float errors
  };

  useEffect(() => {
    const scrollArea = scrollAreaRef.current;
    if (!scrollArea) return;

    updateScrollButtons();

    scrollArea.addEventListener('scroll', updateScrollButtons);
    return () => {
      scrollArea.removeEventListener('scroll', updateScrollButtons);
    };
  }, []);

  return (
    <>
      <ScrollArea viewportRef={scrollAreaRef} className="relative">
        <div className="px-6 pb-6 flex items-center gap-3" ref={containerRef}>
          {profile && <AddStoryAvatar profile={profile} />}
          {stories.map((story, i) => (
            <StoriesResult
              key={i}
              stories={story}
              currentIdx={i}
              selectedIdx={selectedStoryIdx}
              onSelectIdx={setSelectedStoryIdx}
              storiesLength={stories.length ?? 0}
            />
          ))}
        </div>

        {canScrollLeft && (
          <>
            <div className="absolute bottom-0 top-0 h-full pb-6 flex items-center left-2 z-[2]">
              <Button variant="link" size={'icon'} onClick={handleScrollLeft}>
                <FaAngleLeft fontSize={24} className="text-gray-900" />
              </Button>
            </div>

            <Vignette className="h-full w-[200px] top-0 left-[-60px] rotate-[90deg] absolute z-[1] [background:linear-gradient(180deg,rgba(255,255,255,0)_0%,rgba(255,255,255,1)_100%)]" />
          </>
        )}
        {canScrollRight && (
          <>
            <div className="absolute bottom-0 top-0 h-full pb-6 flex items-center right-2 z-[2]">
              <Button variant="link" size={'icon'} onClick={handleScrollRight}>
                <FaAngleRight fontSize={24} className="text-gray-900" />
              </Button>
            </div>
            <Vignette className="h-full w-[200px] top-0 right-[-50px] rotate-[270deg] absolute z-[1] [background:linear-gradient(180deg,rgba(255,255,255,0)_0%,rgba(255,255,255,1)_100%)]" />
          </>
        )}

        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </>
  );
}

export default StoriesContainer;
