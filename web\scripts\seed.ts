import { faker } from '@faker-js/faker';
import {
  GenderEnum,
  NeverSometimesOften,
  PhotoType,
  RelationshipEnum,
  ReligionEnum,
} from '@prisma/client';
import bycrypt from 'bcryptjs';
import chunk from 'lodash/chunk';
import sample from 'lodash/sample';

import countries from '#data/geography/countries.json';
import { cacheSet } from '#server/utils/cache.server';
import { prisma } from '#server/utils/db.server';
import { transformImageAndUploadToR2AndDatabase } from '#server/utils/image.server.js';

type Choice<T> = { weight: number; choice: T };
type GenderInterestChoice = Record<GenderEnum, Choice<GenderEnum>[]>;

const religions = Object.keys(ReligionEnum) as ReligionEnum[];

const getRandomReligion = () => {
  return sample(religions) || religions[0]!;
};

const downloadPhoto = async (url: string) => {
  const response = await fetch(url);
  const blob = await response.blob();
  const file = new File([blob], 'photo.jpg', { type: 'image/jpeg' });
  return file;
};

const allGender = Object.values(GenderEnum);
const allRelationship = Object.values(RelationshipEnum);

const allChoices = <T>(choices: T[], weight: number = 0.01) =>
  Object.values(choices).map((choice) => ({
    // Randomize weight
    weight: Math.random() * weight,
    choice,
  }));

const GENDER_INTEREST_CHOICES: GenderInterestChoice = {
  MALE: [
    ...allChoices(allGender),
    { weight: 0.2, choice: GenderEnum.MALE },
    { weight: 0.3, choice: GenderEnum.TRANSGENDER_FEMALE },
    { weight: 0.4, choice: GenderEnum.FEMALE },
  ],
  FEMALE: [
    ...allChoices(allGender),
    { weight: 0.3, choice: GenderEnum.TRANSGENDER_MALE },
    { weight: 0.6, choice: GenderEnum.MALE },
  ],
  TRANSGENDER_FEMALE: [
    ...allChoices(allGender),
    { weight: 0.4, choice: GenderEnum.TRANSGENDER_MALE },
    { weight: 0.5, choice: GenderEnum.MALE },
  ],
  TRANSGENDER_MALE: [
    { weight: 0.06, choice: GenderEnum.FEMALE },
    {
      weight: 0.04,
      choice: GenderEnum.FEMALE,
    },
    {
      weight: 0.03,
      choice: GenderEnum.TRANSGENDER_FEMALE,
    },
    { weight: 0.1, choice: GenderEnum.TRANSGENDER_FEMALE },
    { weight: 0.2, choice: GenderEnum.MALE },
    ...allChoices(allGender, 0.06),
  ],
  NON_BINARY: allChoices(allGender),
};

const NEVER_OFTEN_SOMETIMES_CHOICES: Choice<NeverSometimesOften>[] = [
  { weight: 0.2, choice: NeverSometimesOften.SOMETIMES },
  { weight: 0.3, choice: NeverSometimesOften.OFTEN },
  { weight: 0.7, choice: NeverSometimesOften.NEVER },
];

const RELATIONSHIP_PREFERENCE_CHOICES: Choice<RelationshipEnum>[] = [
  {
    weight: 0.1,
    choice: RelationshipEnum.CASUAL,
  },
  {
    weight: 0.2,
    choice: RelationshipEnum.FRIENDSHIP_OR_COMPANIONSHIP,
  },
  {
    weight: 0.3,
    choice: RelationshipEnum.MARRIAGE,
  },
  {
    weight: 0.4,
    choice: RelationshipEnum.OPEN_RELATIONSHIP,
  },
  {
    weight: 0.5,
    choice: RelationshipEnum.SERIOUS_RELATIONSHIP,
  },
  {
    weight: 0.6,
    choice: RelationshipEnum.UNSURE,
  },
  ...allChoices(allRelationship, 0.07),
];

const GENDER_CHOICES: Choice<GenderEnum>[] = [
  { weight: 0.05, choice: GenderEnum.TRANSGENDER_MALE },
  { weight: 0.05, choice: GenderEnum.NON_BINARY },
  { weight: 0.1, choice: GenderEnum.TRANSGENDER_FEMALE },
  { weight: 0.3, choice: GenderEnum.MALE },
  { weight: 0.5, choice: GenderEnum.FEMALE },
];

const getChoice = <T>(choices: Choice<T>[]) => {
  if (!choices.length) {
    throw new Error('Choices must not be empty');
  }

  for (const choice of choices) {
    if (choice.weight < 0) {
      throw new Error('Weights must be positive number');
    }
  }

  const totalWeight = choices.reduce((acc, choice) => acc + choice.weight, 0);

  let random = Math.random() * totalWeight;

  for (const item of choices) {
    if (random < item.weight) {
      return item.choice!;
    }
    random -= item.weight;
  }

  return choices[choices.length - 1]!.choice;
};

const GenderMap: Record<GenderEnum, 'male' | 'female' | undefined> = {
  MALE: 'male',
  TRANSGENDER_MALE: 'male',
  FEMALE: 'female',
  TRANSGENDER_FEMALE: 'female',
  NON_BINARY: undefined,
};

// Helper function to create different online status scenarios
const createOnlineStatusScenario = (index: number) => {
  const scenario = index % 7; // 7 different scenarios
  const now = new Date();

  switch (scenario) {
    case 0: // 15% - Currently online (active within last minute)
      return new Date(now.getTime() - Math.random() * 60 * 1000); // 0-60 seconds ago
    case 1: // 15% - Recently online (1-30 minutes ago)
      return new Date(now.getTime() - (1 + Math.random() * 29) * 60 * 1000);
    case 2: // 15% - Online hours ago (1-12 hours ago)
      return new Date(
        now.getTime() - (1 + Math.random() * 11) * 60 * 60 * 1000,
      );
    case 3: // 15% - Online yesterday (12-48 hours ago)
      return new Date(
        now.getTime() - (12 + Math.random() * 36) * 60 * 60 * 1000,
      );
    case 4: // 15% - Online few days ago (2-7 days ago)
      return new Date(
        now.getTime() - (2 + Math.random() * 5) * 24 * 60 * 60 * 1000,
      );
    case 5: // 15% - Online weeks ago (1-4 weeks ago)
      return new Date(
        now.getTime() - (7 + Math.random() * 21) * 24 * 60 * 60 * 1000,
      );
    case 6: // 10% - Never been online (null)
      return null;
    default:
      return null;
  }
};

const createTestUser = async () => {
  console.log('Creating test user...');

  const hashedPassword = await bycrypt.hash('password123', 10);
  const testEmail = '<EMAIL>';

  const user = await prisma.user.create({
    data: {
      createdAt: new Date(),
      updatedAt: new Date(),
      privateUser: {
        create: {
          email: testEmail,
          dateOfBirth: new Date('1990-01-01'),
          password: {
            create: {
              hash: hashedPassword,
            },
          },
          uniqueId: `email:${testEmail}`,
        },
      },
      id: faker.string.uuid(),
      verificationStatus: 'APPROVED',
      userType: 'USER',
      verifiedAt: new Date(),
    },
  });

  const profile = await prisma.profile.create({
    data: {
      firstName: 'Test',
      gender: GenderEnum.MALE,
      username: 'testuser',
      genderInterest: [GenderEnum.FEMALE],
      user: {
        connect: {
          id: user.id,
        },
      },
    },
  });

  await prisma.lifestyle.create({
    data: {
      profileId: profile.id,
      drinking: NeverSometimesOften.SOMETIMES,
      smoking: NeverSometimesOften.NEVER,
      haveChildren: 'NO',
      wantChildren: 'YES',
      religion: ReligionEnum.CHRISTIAN,
    },
  });

  const philippines = countries.find(
    (country) => country.name === 'Philippines',
  )!;

  await prisma.location.create({
    data: {
      cityOrTown: philippines.capital,
      countryIso2: philippines.iso2,
      latitude: parseInt(philippines.latitude),
      longitude: parseInt(philippines.longitude),
      regionCode: philippines.numeric_code,
      profile: {
        connect: {
          id: profile.id,
        },
      },
    },
  });

  await prisma.datingPreference.create({
    data: {
      maxAge: 35,
      relationshipPreference: [RelationshipEnum.SERIOUS_RELATIONSHIP],
      bodyTypes: [],
      drinking: [NeverSometimesOften.SOMETIMES],
      smoking: [NeverSometimesOften.NEVER],
      profile: {
        connect: {
          id: profile.id,
        },
      },
    },
  });

  // Add a test photo
  try {
    const testPhoto = await downloadPhoto(faker.image.avatarGitHub());
    await transformImageAndUploadToR2AndDatabase({
      file: testPhoto,
      photoType: PhotoType.MAIN,
      profileId: profile.id,
      verificationStatus: 'APPROVED',
    });
  } catch (error) {
    console.error(`Failed to upload photo for test user:`, error);
  }

  console.log('✅ Test user created successfully!');
  console.log('📧 Email: <EMAIL>');
  console.log('🔑 Password: password123');
  console.log('👤 Username: testuser');
};

const seed = async () => {
  // Create test user first
  await createTestUser();

  const USER_POPULATION = parseInt(process.env.USER_POPULATION || '100', 10);

  const locationMap = new Map<GenderEnum, (typeof countries)[0]>();

  const philippines = countries.find(
    (country) => country.name === 'Philippines',
  )!;
  const defaultCountry = countries.find(
    (country) => country.name === 'United States',
  )!;
  const otherCountries = countries.filter(
    (country) => country.name !== 'Philippines',
  );

  locationMap.set(GenderEnum.FEMALE, philippines);
  locationMap.set(GenderEnum.TRANSGENDER_FEMALE, philippines);

  const getRandomCountry = () => {
    return sample(otherCountries) ?? defaultCountry;
  };

  const promises: Promise<void>[] = Array.from({ length: USER_POPULATION }).map(
    async () => {
      const eightyPercent = Math.floor(USER_POPULATION * 0.8);
      const isVerified =
        Math.floor(Math.random() * USER_POPULATION) < eightyPercent;
      const hasGallery =
        Math.floor(Math.random() * USER_POPULATION) < eightyPercent;
      const hashedPassword = await bycrypt.hash('temp_password', 10);

      /**
       * Randomize location based on non female/transfemale gender
       */
      locationMap.set(GenderEnum.MALE, getRandomCountry());
      locationMap.set(GenderEnum.TRANSGENDER_MALE, getRandomCountry());
      locationMap.set(GenderEnum.NON_BINARY, getRandomCountry());

      const email = faker.internet.email().toLowerCase();

      const user = await prisma.user.create({
        data: {
          createdAt: faker.date.anytime(),
          updatedAt: faker.date.anytime(),
          privateUser: {
            create: {
              email,
              dateOfBirth: faker.date.birthdate({
                min: 18,
                max: 65,
                mode: 'age',
              }),
              password: {
                create: {
                  hash: hashedPassword,
                },
              },
              uniqueId: `email:${email}`,
            },
          },
          id: faker.string.uuid(),
          verificationStatus: isVerified ? 'APPROVED' : 'PENDING',
          userType: 'USER',
          verifiedAt: faker.date.recent(),
        },
      });

      const gender = getChoice(GENDER_CHOICES) ?? GenderEnum.MALE;

      const firstName = faker.person.firstName(GenderMap[gender]);

      const profile = await prisma.profile.create({
        data: {
          firstName,
          gender,
          username: faker.internet.username({ firstName }),

          genderInterest: [getChoice(GENDER_INTEREST_CHOICES[gender])],
          user: {
            connect: {
              id: user.id,
            },
          },
        },
      });

      await prisma.lifestyle.create({
        data: {
          profileId: profile.id,
          drinking: getChoice(NEVER_OFTEN_SOMETIMES_CHOICES),
          smoking: getChoice(NEVER_OFTEN_SOMETIMES_CHOICES),
          haveChildren: getChoice([
            { weight: 0.5, choice: 'YES' },
            { weight: 0.5, choice: 'NO' },
          ]),
          wantChildren: getChoice([
            { weight: 0.5, choice: 'YES' },
            { weight: 0.5, choice: 'NO' },
          ]),
          religion: getRandomReligion(),
        },
      });

      const location = locationMap.get(gender)!;

      await prisma.location.create({
        data: {
          cityOrTown: location.capital,
          countryIso2: location.iso2,
          latitude: parseInt(location.latitude),
          longitude: parseInt(location.longitude),
          regionCode: location.numeric_code,
          profile: {
            connect: {
              id: profile.id,
            },
          },
        },
      });

      await prisma.datingPreference.create({
        data: {
          maxAge: 60,
          relationshipPreference: [getChoice(RELATIONSHIP_PREFERENCE_CHOICES)],
          bodyTypes: [],
          drinking: [getChoice(NEVER_OFTEN_SOMETIMES_CHOICES)],
          smoking: [getChoice(NEVER_OFTEN_SOMETIMES_CHOICES)],
          profile: {
            connect: {
              id: profile.id,
            },
          },
        },
      });

      // Downloads photo from faker image
      const [mainPhoto, galleryPhoto] = await Promise.all([
        downloadPhoto(faker.image.avatarGitHub()),
        downloadPhoto(faker.image.avatarGitHub()),
      ]);

      try {
        await transformImageAndUploadToR2AndDatabase({
          file: mainPhoto,
          photoType: PhotoType.MAIN,
          profileId: profile.id,
          verificationStatus: isVerified ? 'APPROVED' : 'PENDING',
        });

        if (hasGallery) {
          await transformImageAndUploadToR2AndDatabase({
            file: galleryPhoto,
            profileId: profile.id,
            photoType: PhotoType.GALLERY,
            verificationStatus: isVerified ? 'APPROVED' : 'PENDING',
          });
        }
      } catch (error) {
        console.error(
          `Failed to upload photos for profile ${profile.id}:`,
          error,
        );
      }

      // Create different online status scenarios
      const lastOnlineTime = createOnlineStatusScenario(
        Math.floor(Math.random() * USER_POPULATION),
      );

      // Set custom last online time in cache
      if (lastOnlineTime) {
        const cacheKey = `profile/${profile.id}/last-online`;
        await cacheSet(cacheKey, lastOnlineTime.toISOString(), 24 * 60 * 60); // Keep for 24 hours
      }
      // If lastOnlineTime is null, we don't set anything in cache (user never been online)
    },
  );

  console.log('Seeding users...');

  const chunkedPromises = chunk(promises, 1).map((chunkedPromise, index) => {
    console.log(`Seeding chunk ${index + 1}...`);
    return Promise.all(chunkedPromise);
  });

  // Process chunks sequentially to avoid overwhelming R2 and Cloudinary
  for (const chunkedPromise of chunkedPromises) {
    await chunkedPromise;
    // Add a longer delay between chunks to further reduce API pressure on Cloudinary
    await new Promise((resolve) => setTimeout(resolve, 3000));
  }

  console.log('Seeding complete! 🎉');
};

seed()
  .then(() => {
    process.exit(0);
  })
  .catch((error) => {
    console.error(error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
