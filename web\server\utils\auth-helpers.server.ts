import { type PrivateUser } from '@prisma/client';
import { t } from '#server/utils/api.server';
import { signupWithEmail } from '#server/utils/auth.server';
import { prisma } from '#server/utils/db.server';
import { sendSignupEmail } from '#server/utils/email-generators.server';
import {
  ForbiddenError,
  ValidationError,
  ZodValidationError,
} from '#server/utils/errors.server';
import { genUniqueIdFromEmail } from '#server/utils/user.server';
import { type ChangePasswordData, type SignupData } from '#shared/types/auth';
import {
  changePasswordValidator,
  createSignupValidator,
} from '#shared/utils/validators.js';
import {
  cacheLocationDataForUser,
  getClientIpAddress,
  getIpLocationData,
  validateAndCleanLocationData,
} from './ip-location.server';
import { comparePassword, hashPassword } from './password.server';

export async function handleSignup(
  formDataObject: SignupData,
  request?: Request,
) {
  const result = createSignupValidator(t).safeParse(formDataObject);
  if (!result.success) {
    throw new ZodValidationError(result.error);
  }

  // check if the email is already in use
  const existingPrivateUser = await prisma.privateUser.findUnique({
    where: { uniqueId: genUniqueIdFromEmail(result.data.email) },
  });
  if (existingPrivateUser) {
    throw new ValidationError({
      email: [t('A user with this email already exists')],
    });
  }

  const session = await signupWithEmail(result.data);

  // send signup email asynchronously
  sendSignupEmail({
    privateUserId: session.privateUserId,
    email: result.data.email,
  }).catch((error: any) => {
    console.error('Error sending signup email:', error);
  });

  // Trigger IP location detection asynchronously (non-blocking)
  if (request && session.privateUser) {
    detectAndCacheUserLocation(request, session.privateUser).catch(
      (error: any) => {
        console.error('Error detecting user location:', error);
      },
    );
  }

  return session;
}

export async function handleChangePassword(
  formDataObject: ChangePasswordData,
  privateUser: PrivateUser,
) {
  const result = changePasswordValidator(t).safeParse(formDataObject);
  if (!result.success) {
    throw new ZodValidationError(result.error);
  }

  const { newPassword, currentPassword } = result.data;

  // Get current/old password
  const userPassword = await prisma.password.findUnique({
    where: {
      privateUserId: privateUser.id,
    },
  });

  // Forbidden for users who signed up via external providers.
  if (!userPassword) {
    throw new ForbiddenError(
      t('Forbidden: You cannot change password for social login accounts.'),
    );
  }

  // Check if current password in payload matches current password in database.
  const isValidCurrentPassword = await comparePassword(
    currentPassword,
    userPassword.hash,
  );

  if (!isValidCurrentPassword) {
    throw new ValidationError({
      currentPassword: [t('Invalid current password.')],
    });
  }

  // Check if new password is the same with old password.
  const isSamePassword = await comparePassword(newPassword, userPassword.hash);

  if (isSamePassword) {
    throw new ValidationError({
      newPassword: [t('New password must be different from current password.')],
    });
  }

  // Hash new password
  const newPasswordHash = await hashPassword(newPassword);

  // Replace current hashed password with new hashed password
  await prisma.password.update({
    data: {
      hash: newPasswordHash,
    },
    where: {
      privateUserId: privateUser.id,
    },
  });

  return { success: true };
}

export async function detectAndCacheUserLocation(
  request: Request,
  privateUser: PrivateUser,
) {
  const clientIp = await getClientIpAddress(request);
  if (!clientIp) {
    console.warn('Could not detect client IP address');
    return;
  }

  try {
    const locationData = await getIpLocationData(clientIp, privateUser.id);
    if (!locationData) {
      console.warn('No location data available for IP:', clientIp);
      return;
    }

    const validatedData = await validateAndCleanLocationData(locationData);
    await cacheLocationDataForUser(privateUser, validatedData);
  } catch (error) {
    console.warn('Error detecting and caching user location:', error);
    // Don't throw - this should be a non-blocking operation
  }
}
