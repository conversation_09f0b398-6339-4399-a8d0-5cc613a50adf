{
  "include": [
    "./**/*.ts",
    "./**/*.tsx",
    "./**/*.d.ts",
    "./server/**/*.ts",
    "./server/**/*.tsx",
    "../shared/**/*.ts",
    "../data/**/*.json"
  ],
  "exclude": ["mobile/**/*", "node_modules/**/*", "web/node_modules/**/*"],
  "extends": ["@epic-web/config/typescript"],
  "compilerOptions": {
    "isolatedModules": true,
    "jsx": "react-jsx",
    "target": "ES2022",
    "moduleDetection": "force",
    "composite": true,
    "noEmit": true,
    "rootDir": "./../",
    "allowImportingTsExtensions": false,
    // TODO: uncomment once we can figure out how to make it work with the current setup
    // "verbatimModuleSyntax": true,
    "paths": {
      "#app/*": ["./app/*"],
      "#shared/*": ["./../shared/*"],
      "#tests/*": ["./tests/*"],
      "#server/*": ["./server/*"],
      "#root/*": ["./*"],
      "#data/*": ["./../data/*"],
      "@/icon-name": [
        "./app/components/ui/icons/name.d.ts",
        "./types/icon-name.d.ts"
      ]
    }
  }
}
