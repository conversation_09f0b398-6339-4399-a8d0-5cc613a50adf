import { SignupEmail } from '#server/emails/auth/signup';
import { t } from '#server/utils/api.server';
import { sendEmail } from '#server/utils/email.server.js';
import { EmailError } from '#server/utils/errors.server';
import { signJwt } from '#server/utils/jwt.server';

export async function sendSignupEmail({
  privateUserId,
  email,
}: {
  privateUserId: string;
  email: string;
}) {
  // Generate a verification token using JWT
  const token = signJwt(
    { privateUserId },
    {
      expiresIn: '24h', // Token valid for 24 hours
    },
  );
  const verifyUrl = `${process.env.BASE_URL}/auth/email-verify/${token}`;

  // Send the verification email
  const response = await sendEmail({
    to: email,
    subject: t('Verify your email for Filipina Meet'),
    react: <SignupEmail verificationUrl={verifyUrl} />,
  });
  if (response.status === 'error') {
    console.error('Error sending signup email:', response.error);
    throw new EmailError(response.error.message);
  }
}
