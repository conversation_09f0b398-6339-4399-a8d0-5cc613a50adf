import { EyeSlashIcon, EyeIcon } from '@heroicons/react/24/outline';
import { useState } from 'react';
import { Input, type InputProps } from './input';

interface PasswordProps extends InputProps {}

export function Password(inputProps: PasswordProps) {
  const [showPassword, setShowPassword] = useState(false);
  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="relative">
      <button
        name="toggle-password"
        title="toggle-password"
        type="button"
        className="h-6 w-6 absolute top-3 right-3"
        onClick={toggleShowPassword}
      >
        {showPassword ? (
          <EyeSlashIcon className="text-muted-foreground" />
        ) : (
          <EyeIcon className="text-brand-muted" />
        )}
      </button>

      <Input
        {...inputProps}
        type={showPassword ? 'text' : 'password'}
        autoComplete="new-password"
      />
    </div>
  );
}
