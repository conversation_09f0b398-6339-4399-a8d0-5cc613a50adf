import * as RadioGroupPrimitive from '@radix-ui/react-radio-group';
import * as React from 'react';
import { cn } from '#app/utils/misc';

// Root component for the Radio Group
const RadioGroup = RadioGroupPrimitive.Root;

// Custom Radio Item component with styles and indicators
const RadioGroupItem = React.forwardRef<
  React.ElementRef<typeof RadioGroupPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item> & {
    label?: string;
  }
>(({ className, label, ...props }, ref) => (
  <div
    className={cn('flex items-center space-x-2', {
      'cursor-not-allowed': props.disabled,
    })}
  >
    <RadioGroupPrimitive.Item
      ref={ref}
      className={cn(
        'data-[state=checked]:bg-brand h-4 w-4 rounded-full border border-black transition-colors data-[state=checked]:border-brand-primary',
        {
          'border-gray-400': props.disabled,
        },
        className,
      )}
      {...props}
    >
      <RadioGroupPrimitive.Indicator className="relative flex h-full w-full items-center justify-center">
        <span className="h-2 w-2 rounded-full bg-brand-primary"></span>
      </RadioGroupPrimitive.Indicator>
    </RadioGroupPrimitive.Item>
    {label && (
      <span
        className={cn('text-sm text-gray-800', {
          'text-gray-400': props.disabled,
        })}
      >
        {label}
      </span>
    )}
  </div>
));
RadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName;

// Export components for usage
export { RadioGroup, RadioGroupItem };
