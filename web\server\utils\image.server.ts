import { URL } from 'node:url';
import {
  $Enums,
  type PhotoType,
  Provider,
  type VariantType,
  type PhotoVariant,
  type VerificationStatus,
} from '@prisma/client';
import {
  DimensionStringToVariantType,
  VariantTypeConfig,
} from '#shared/constants/media.js';
import {
  type CloudflareKeyCategories,
  type ParentPhotoWithVariantsandRaw,
} from '#shared/types/media.js';
import { createDimensionStringHash } from '#shared/utils/images.js';
import { deleteFileFromR2, uploadFileToR2 } from './cloudflare.server';
import {
  deleteFileFromCloudinary,
  getCloudinaryUrl,
  uploadImageToCloudinary,
} from './cloudinary.server';
import { prisma } from './db.server';
import { NotFoundError } from './errors.server';
import { getImageDimensions } from './media.server';

async function imageUrlToFile(url: URL) {
  const imageFetch = await fetch(url);
  const blob = await imageFetch.blob();
  return new File([blob], url.toString());
}

interface PhotoData {
  image: File;
  height: number;
  width: number;
  photoType: PhotoType;
  fileId: string;
  format: string;
}

interface PhotoVariantData extends PhotoData {
  variantType: VariantType;
}

function buildPhotoKey({
  fileId,
  format,
  category,
}: {
  fileId: string;
  format: string;
  category: CloudflareKeyCategories;
}) {
  return `${category}/${fileId}.${format}`;
}

async function uploadPhotoVariantAndCreateDBRef({
  variantData,
  parentPhotoId,
  profileId,
}: {
  variantData: PhotoVariantData;
  parentPhotoId: string;
  profileId: string;
}) {
  const {
    image,
    variantType: variant,
    fileId,
    format,
    photoType,
    height,
    width,
  } = variantData;
  const cloudflareVariantKey = buildPhotoKey({
    fileId,
    format,
    category: variant,
  });

  // make sure file is uploaded first before setting this in db
  await uploadFileToR2({
    file: image,
    key: cloudflareVariantKey,
  });
  // create Cloudflare entry and delete Cloudinary entry
  try {
    await prisma.$transaction([
      prisma.photoVariant.create({
        data: {
          parentPhotoId,
          profileId,
          fileSize: image.size,
          format,
          provider: $Enums.Provider.CLOUDFLARE_R2,
          height,
          width,
          variant,
          key: cloudflareVariantKey,
        },
      }),
      prisma.photoVariant.deleteMany({
        where: {
          provider: $Enums.Provider.CLOUDINARY,
          profileId,
          variant,
          parentPhoto: {
            photoType,
            fileId,
          },
        },
      }),
    ]);
  } catch {
    // delete uploaded file if transaction fails
    await deleteFileFromR2({ key: cloudflareVariantKey });
    throw new Error('Error creating Cloudflare Reference');
  }
  // delete Cloudinary copy
  await deleteFileFromCloudinary({ fileId });
}

async function uploadParentPhotoAndCreateDBRef({
  photoData,
  variantDataArray,
  profileId,
  verificationStatus,
}: {
  photoData: PhotoData;
  profileId: string;
  variantDataArray: PhotoVariantData[];
  verificationStatus?: VerificationStatus;
}) {
  const { fileId, image: file, format, photoType, height, width } = photoData;

  const rawPhotoKey = buildPhotoKey({
    fileId,
    category: 'RAWPHOTO',
    format,
  });
  await uploadFileToR2({
    file: file,
    key: rawPhotoKey,
  });
  const parentPhoto = await prisma.parentPhoto
    .create({
      data: {
        fileId,
        photoType,
        format,
        provider: $Enums.Provider.CLOUDINARY,
        height,
        width,
        profileId,
        verificationStatus,
        rawPhoto: {
          create: {
            fileId,
            fileSize: file.size,
            format,
            height,
            width,
            key: rawPhotoKey,
          },
        },
        photoVariants: {
          create: variantDataArray.map((variantData) => {
            const { height, width, image } = variantData;

            return {
              fileSize: image.size,
              format,
              height,
              width,
              profileId,
              key: fileId,
              variant: dimensionsToVariantType({ height, width }),
              provider: Provider.CLOUDINARY,
            };
          }),
        },
      },
    })
    .catch(async (e) => {
      // Delete rawphoto if db store fails
      await deleteFileFromR2({ key: rawPhotoKey });
      // Delete from cloud since db can't point to it
      await deleteFileFromCloudinary({ fileId, type: 'image' });
      throw new Error(e);
    });

  return parentPhoto;
}

/**
 * Transforms the image file using Cloudinary and uploads
 * the raw image and the transformations to R2 Cloudflare
 * and saves to the Database. Deletes the file from Cloudinary
 * at the very end.
 */
export async function transformImageAndUploadToR2AndDatabase({
  file,
  photoType,
  profileId,
  verificationStatus,
}: {
  file: File;
  photoType: $Enums.PhotoType;
  profileId: string;
  verificationStatus?: VerificationStatus;
}) {
  const [transformedPhoto, { height, width }] = await Promise.all([
    uploadImageToCloudinary({
      file,
      subPath: `${photoType}`,
      profileId,
    }),
    getImageDimensions(file),
  ]);

  const { fileId, format, eager: eagers } = transformedPhoto;

  // upload transformations to cloudflare
  const variantDataArray = await Promise.all(
    eagers.map(async (eager) => {
      const url = URL.parse(eager.secure_url);
      if (!url) throw new Error('Not a URL');

      const image = await imageUrlToFile(url);
      const { height, width } = await getImageDimensions(image);
      const variantType = dimensionsToVariantType({ width, height });

      return {
        image,
        height,
        width,
        variantType,
        photoType,
        fileId,
        format,
      };
    }),
  );

  const parentPhoto = await uploadParentPhotoAndCreateDBRef({
    photoData: {
      fileId,
      format,
      height,
      image: file,
      photoType,
      width,
    },
    profileId,
    variantDataArray,
    verificationStatus,
  });

  // Background task to upload cloudflare refs and replace cloudinary refs
  // and delete after
  Promise.all(
    variantDataArray.map(async (variantData) =>
      uploadPhotoVariantAndCreateDBRef({
        variantData,
        parentPhotoId: parentPhoto.id,
        profileId,
      }),
    ),
  )
    // Background task errors do not get caught by
    // outside catch or try/catch referencing the function so
    // catching errors here instead.
    .catch((err) => console.error(err));
}

type DeletePhotoInput =
  | { parentPhotoId: string; photoFileId?: never }
  | { parentPhotoId?: never; photoFileId: string };

/**
 * Delete photo from both cloud and db store
 */
export async function deletePhotoFromCloudandDB(args: DeletePhotoInput) {
  const { parentPhotoId, photoFileId } = args;

  let parentPhoto: ParentPhotoWithVariantsandRaw | null = null;

  if (parentPhotoId) {
    parentPhoto = await prisma.parentPhoto.findUnique({
      where: {
        id: parentPhotoId,
      },
      include: {
        rawPhoto: true,
        photoVariants: true,
      },
    });
  }

  if (photoFileId) {
    parentPhoto = await prisma.parentPhoto.findUnique({
      where: {
        fileId: photoFileId,
      },
      include: {
        rawPhoto: true,
        photoVariants: true,
      },
    });
  }

  if (!parentPhoto) {
    throw new NotFoundError('Parent Photo Not Found');
  }

  if (!parentPhoto.rawPhoto) {
    throw new NotFoundError('Raw Photo Not Found');
  }

  const r2Keys = parentPhoto.photoVariants
    .filter(
      (photoVariant) => photoVariant.provider === $Enums.Provider.CLOUDFLARE_R2,
    )
    .map((photoVariant) => photoVariant.key);

  await prisma.parentPhoto.delete({
    where: {
      id: parentPhotoId,
    },
  });
  await Promise.all([
    deleteFileFromCloudinary({
      fileId: parentPhoto.rawPhoto.fileId,
      type: 'image',
    }),
    deleteFileFromR2({
      key: parentPhoto.rawPhoto.key,
    }),
    r2Keys.map((key) => deleteFileFromR2({ key })),
  ]);
}

export function makeURLFromPhotoVariant({
  photoVariant,
}: {
  photoVariant: PhotoVariant;
}) {
  const provider = photoVariant.provider;
  const key = photoVariant.key;
  if (provider === $Enums.Provider.CLOUDFLARE_R2) {
    return `${process.env.CLOUDFLARE_PUBLIC_URL}/${key}`;
  }

  if (provider === $Enums.Provider.CLOUDINARY) {
    return getCloudinaryUrl(key, VariantTypeConfig[photoVariant.variant]);
  }

  throw new NotFoundError('No Provider Matched');
}

function dimensionsToVariantType({
  height,
  width,
}: {
  height: number;
  width: number;
}) {
  const variantType =
    DimensionStringToVariantType[
      createDimensionStringHash({
        height,
        width,
      })
    ];

  if (!variantType) {
    throw new NotFoundError('VariantType could not be determined');
  }

  return variantType;
}
