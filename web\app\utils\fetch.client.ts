import { toast as showToast } from 'sonner';
import { type HttpMethod } from '#shared/types/api';
import { type APIError, isAPIError } from '#shared/utils/api-errors.client';
import { apiFetch as baseApiFetch } from '#shared/utils/fetch.js';
import { handleFetchError } from '#shared/utils/handle-errors.client';

// Fix the typings to include headers and body properties
type FetchOptions = Omit<Parameters<typeof baseApiFetch>[2], 'handleError'> & {
  headers?: Record<string, string>;
  body?: string | FormData;
  queryParams?: Record<string, string | number | undefined>;
};

export async function handleApiCall<T>(
  apiCall: Promise<T>,
  options?: {
    onApiSuccess?: (result: T) => void;
    onApiFailure?: (error: APIError) => void;
    onFinally?: () => void;
  },
) {
  const { onApiSuccess, onApiFailure, onFinally } = options || {};
  try {
    const result = await apiCall;
    onApiSuccess?.(result);
  } catch (error) {
    console.log('error', error);
    if (isAPIError(error)) {
      showToast.error('API call failed', {
        description: error.message || 'An unexpected error occurred.',
        id: 'api-call-failure-toast',
      });
      onApiFailure?.(error);
    } else {
      showToast.error('An unexpected error occurred', {
        id: 'api-call-failure-toast',
      });
    }
    // Show failure toast message
    console.error('API call failed:', error); // Replace with actual toast implementation
  } finally {
    onFinally?.();
  }
}

// fetch logic
async function apiFetch<T>(
  url: string,
  method: HttpMethod,
  options?: FetchOptions,
): Promise<T> {
  return baseApiFetch(url, method, {
    ...options,
    handleError: handleFetchError,
  });
}

// Boilerplate code that exists in many files
export async function apiGet<T>(
  url: string,
  options?: FetchOptions,
): Promise<T> {
  return apiFetch<T>(url, 'GET', options);
}

export async function apiPost<T>(
  url: string,
  body?: any,
  options?: FetchOptions,
): Promise<T> {
  return apiFetch<T>(url, 'POST', {
    ...options,
    body: JSON.stringify(body || {}),
  });
}

export async function apiPut<T>(
  url: string,
  body: any,
  options?: FetchOptions,
): Promise<T> {
  return apiFetch<T>(url, 'PUT', { ...options, body: JSON.stringify(body) });
}

export async function apiDelete<T>(
  url: string,
  options?: FetchOptions,
): Promise<T> {
  return apiFetch<T>(url, 'DELETE', {
    ...options,
    headers: {
      'Content-Type': 'application/json',
    },
    // Only add empty body if no body is provided
    body: options?.body || JSON.stringify({}),
  });
}

export async function apiPatch<T>(
  url: string,
  body: any,
  options?: FetchOptions,
): Promise<T> {
  return apiFetch<T>(url, 'PATCH', { ...options, body: JSON.stringify(body) });
}
