import { useTranslation } from "react-i18next";
import { z } from "zod";
import gradientMeshUrl from '#app/images/gradient-mesh.webp';
import { LayoutType } from "#app/types/layouts.js";
import { cn } from "#app/utils/misc.js";
import { handleFormActionForLoggedInUser, handleLoaderForProfileRoute } from "#app/utils/remix-helpers.server.js";
import { prisma } from "#server/utils/db.server.js";
import { SEARCH_BASIC_ROUTE } from "#shared/constants/routes.js";

export const handle = {layout: LayoutType.NONE}

export const loader = handleLoaderForProfileRoute(
  async ({ profile }) => {
    // mark user as completed profile setup once they reach the page
    await prisma.profile.update({
      where: {
        id: profile.id
      },
      data: {
        completedProfileSetup: true,
      },
    })

    return null;
  },
)
 
export const action = handleFormActionForLoggedInUser(
  () => z.object({}),
  async ({ redirect }) => {
    return redirect(SEARCH_BASIC_ROUTE);
  },
);

export default function SetupCompleteRoute() {
  const { t } = useTranslation();

  return (
    <form method="POST" action="/profile/setup/complete">
      <div className="flex flex-col items-center justify-center">
        <div className="mb-6 md:mb-8 relative size-[300px] flex justify-center items-center">
          <img
            src={gradientMeshUrl}
            alt="Gradient Background"
            className="size-[300px] rounded-full object-cover absolute"
          />
          <div
            className="backdrop-blur-lg size-[300px] rounded-full object-cover absolute"
          />
          <img
            src={gradientMeshUrl}
            alt="Gradient Background"
            className="size-[250px] rounded-full object-cover absolute"
          />
          <div className="text-9xl absolute">
            🎉
          </div>
        </div>
        <div className="mb-6 md:mb-8 text-center">
          <h1
            className={cn(
              'text-3xl md:text-4xl font-extrabold text-gray-900',
              'dark:text-white md:tracking-tight',
            )}
          >
            {t('You\'re all set!')}
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            {t('Your profile’s ready—time to see who’s out there')}
          </p>
        </div>
          <button
            type="submit"
            className={cn(
              'w-[35%] rounded-lg bg-[#D33544] py-3 text-center font-medium text-white',
              'transition-colors hover:bg-[#B82C3A] focus:outline-none focus:ring-2',
              'focus:ring-[#D33544] focus:ring-offset-2',
            )}
          >
            Start exploring 💘
          </button>
      </div>
    </form>
  );
}