import { handleFormActionForLoggedInUser } from '#app/utils/remix-helpers.server';
import { t } from '#server/utils/api.server';
import { prisma } from '#server/utils/db.server';
import { changePhoneNumberValidator } from '#shared/utils/validators';

export const action = handleFormActionForLoggedInUser(
  changePhoneNumberValidator,
  async ({ successWithMessage, privateUser, data }) => {
    console.log('contact info update action', data)
    const { phoneNumber } = data;

    await prisma.privateUser.update({
      data: {
        phoneNumber,
      },
      where: {
        id: privateUser.id,
      },
    });

    return successWithMessage(t('Phone number updated successfully'));
  },
);
