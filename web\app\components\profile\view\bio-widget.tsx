import { type Bio } from '@prisma/client';
import { useTranslation } from 'react-i18next';
import Typography from '#app/components/typography';

function BioWidget({ bio, isPopup = false }: { bio: Bio; isPopup: boolean }) {
  const { t } = useTranslation();
  // TODO: find a way to translate database fields
  return (
    <div>
      {bio.tagline && !isPopup && (
        <Typography
          variant="h5"
          className="font-medium italic text-brand-primary"
        >
          "{bio.tagline}"
        </Typography>
      )}

      <Typography variant="h5" className="font-medium mb-2 !leading-none">
        {t('My Bio')}
      </Typography>
      {bio.aboutMe && <Typography>{bio.aboutMe}</Typography>}
    </div>
  );
}

export { BioWidget };
