#!/bin/sh
echo "⏳ Running lint-staged (formatting and linting)..."

# Run lint-staged which handles formatting and linting
npm exec lint-staged

# Check if lint-staged passed
if [ $? -ne 0 ]; then
  echo "🧨 Lint-staged checks failed. Commit aborted."
  exit 1
fi

# Run TypeScript type checking
echo "⏳ Running type checking..."
npm run typecheck
if [ $? -ne 0 ]; then
  echo "🧨 TypeScript type checking failed. Commit aborted."
  exit 1
fi

# Run Vitest tests with coverage
echo "⏳ Running tests..."
npm run test:web:ci -- --coverage
if [ $? -ne 0 ]; then
  echo "🧨 Vitest tests failed. Commit aborted."
  exit 1
fi

echo "🎉 All checks passed. Committing..."