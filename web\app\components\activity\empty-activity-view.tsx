import { Link } from '@remix-run/react';
import Typography from '#app/components/typography';
import { buttonVariants } from '#app/components/ui/button';
import { cn } from '#app/utils/misc';

interface Props {
  imgSrc?: string;
  title: string;
  description: string;
  cta: {
    title: string;
    path: string;
  };
}

const EmptyActivityView = ({ title, imgSrc, description, cta }: Props) => {
  return (
    <div className="inline-flex justify-center items-center w-full flex-col gap-2 sm:gap-0 px-3 sm:px-4 py-3 sm:py-4">
      {imgSrc && (
        <div>
          <img
            src={imgSrc || '/placeholder.svg'}
            alt="mailbox"
            className="w-full h-auto max-w-[150px] sm:max-w-[200px]"
          />
        </div>
      )}
      <div className="inline-flex justify-center items-center w-full flex-col gap-3 sm:gap-5">
        <div className="text-center">
          <Typography
            variant="h3"
            className="bg-gradient-to-r from-brand-secondary to-brand-primary bg-clip-text text-transparent text-xl sm:text-2xl md:text-3xl"
          >
            {title}
          </Typography>
          <Typography
            variant="h5"
            className="font-normal text-muted-foreground text-sm sm:text-base"
          >
            {description}
          </Typography>
        </div>
        <Link
          to={cta.path}
          className={cn(
            buttonVariants({ variant: 'default', size: 'lg' }),
            'font-semibold text-sm sm:text-base',
          )}
        >
          {cta.title}
        </Link>
      </div>
    </div>
  );
};

export default EmptyActivityView;
