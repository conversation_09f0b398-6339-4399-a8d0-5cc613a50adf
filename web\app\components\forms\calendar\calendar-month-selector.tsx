// REACT
import { useMemo, useCallback, memo } from 'react';

// STYLES
import {
  COLORS,
  STYLES,
  monthContainerStyle,
  getBaseMonthStyle,
} from '#app/styles/calendar-picker-styles';

// TYPES
import { type Month, type MonthItemProps } from '#app/types/calendar-types';

// UTILS
import { MIN_AGE_DATE } from '#app/utils/calendar-helpers';

interface CalendarMonthSelectorProps {
  viewDate: Date;
  setViewDate: (date: Date) => void;
  setMonthSelectOpen: (open: boolean) => void;
}

export function CalendarMonthSelector({
  viewDate,
  setViewDate,
  setMonthSelectOpen,
}: CalendarMonthSelectorProps) {
  const currentMonth = viewDate.getMonth();
  const currentViewYear = viewDate.getFullYear();

  const isMinAgeYear = currentViewYear === MIN_AGE_DATE.getFullYear();

  const monthItems = useMemo(() => {
    return MONTHS.map((month) => {
      const isDisabled = isMinAgeYear && month.value > MIN_AGE_DATE.getMonth();
      const isActive = !isDisabled && month.value === currentMonth;

      return (
        <MonthItem
          key={month.name}
          month={month}
          isActive={isActive}
          isDisabled={isDisabled}
          viewDate={viewDate}
          setViewDate={setViewDate}
          setMonthSelectOpen={setMonthSelectOpen}
        />
      );
    });
  }, [currentMonth, isMinAgeYear, viewDate, setViewDate, setMonthSelectOpen]);

  return <div style={monthContainerStyle}>{monthItems}</div>;
}

const MONTHS: Month[] = [
  { name: 'Jan', value: 0 },
  { name: 'Feb', value: 1 },
  { name: 'Mar', value: 2 },
  { name: 'Apr', value: 3 },
  { name: 'May', value: 4 },
  { name: 'Jun', value: 5 },
  { name: 'Jul', value: 6 },
  { name: 'Aug', value: 7 },
  { name: 'Sep', value: 8 },
  { name: 'Oct', value: 9 },
  { name: 'Nov', value: 10 },
  { name: 'Dec', value: 11 },
];

const MonthItem = memo(
  ({
    month,
    isActive,
    isDisabled,
    viewDate,
    setViewDate,
    setMonthSelectOpen,
  }: MonthItemProps) => {
    const handleMonthClick = useCallback(() => {
      if (!isDisabled) {
        const newDate = new Date(viewDate);
        newDate.setMonth(month.value);
        setViewDate(newDate);
        setMonthSelectOpen(false);
      }
    }, [isDisabled, month.value, viewDate, setViewDate, setMonthSelectOpen]);

    const handleMouseOver = useCallback(
      (e: React.MouseEvent<HTMLDivElement>) => {
        if (!isDisabled && !isActive) {
          e.currentTarget.style.backgroundColor = COLORS.primaryLight;
          e.currentTarget.style.color = COLORS.primary;
          e.currentTarget.style.boxShadow =
            '0 4px 10px rgba(211, 53, 68, 0.15)';
        }
      },
      [isDisabled, isActive],
    );

    const handleMouseOut = useCallback(
      (e: React.MouseEvent<HTMLDivElement>) => {
        if (!isDisabled && !isActive) {
          e.currentTarget.style.backgroundColor = 'transparent';
          e.currentTarget.style.color = '#1E1E1E';
          e.currentTarget.style.boxShadow = 'none';
          e.currentTarget.style.border = 'none';
        }
      },
      [isDisabled, isActive],
    );

    const baseStyle = getBaseMonthStyle(isDisabled);
    const style = isActive
      ? { ...baseStyle, ...STYLES.activeElement }
      : baseStyle;

    return (
      <div
        key={month.name}
        onClick={handleMonthClick}
        style={style}
        onMouseOver={handleMouseOver}
        onMouseOut={handleMouseOut}
      >
        {month.name}
      </div>
    );
  },
);

MonthItem.displayName = 'MonthItem';
