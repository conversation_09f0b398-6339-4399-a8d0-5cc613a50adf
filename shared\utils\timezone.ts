import { type PrivateUser } from '@prisma/client';

/**
 * Gets the user's timezone preference.
 * Currently returns the browser's timezone, but can be enhanced later
 * to use the user's stored timezone preference.
 */
export function getUserTimeZone(privateUser?: PrivateUser): string {
  // TODO: Placeholder for future implementation
  void privateUser;

  return Intl.DateTimeFormat().resolvedOptions().timeZone;
}
