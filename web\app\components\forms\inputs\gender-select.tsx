// REACT
import React from 'react';
import { useTranslation } from 'react-i18next';

// UTILS
import { getGenderFormOptions } from '#shared/utils/form-options';

// COMPONENTS
import { GenericSelect } from './generic-select';

type GenderSelectProps = Omit<
  React.ComponentProps<typeof GenericSelect>,
  'options' | 'placeholder' | 'formValue'
>;

const GenderSelect = (props: GenderSelectProps) => {
  const { t } = useTranslation();

  const translatedOptions = getGenderFormOptions(t);

  return <GenericSelect options={translatedOptions} {...props} />;
};

export { GenderSelect };
