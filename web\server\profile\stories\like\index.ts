import { type FastifyInstance } from 'fastify';
import { t } from '#server/utils/api.server';
import { prisma } from '#server/utils/db.server';
import { handleFastifyRoute } from '#server/utils/fastify-helpers.server';

export async function likeStoriesRouter(fastify: FastifyInstance) {
  fastify.post(
    '/:videoId/like',
    handleFastifyRoute(async ({ request, sendSuccessMessage }) => {
      const { profile, likedVideo } = request;
      console.log(`Liked ID: ${likedVideo} Liker: ${profile}`);
      const existingLike = await prisma.videoLike.findFirst({
        where: {
          videoLikerProfileId: profile.id,
          likedVideoId: likedVideo.id,
        },
      });
      if (!existingLike) {
        await prisma.videoLike.create({
          data: {
            videoLikerProfileId: profile.id,
            likedVideoId: likedVideo.id,
          },
        });
      }
      sendSuccessMessage(t('Video Liked'));
    }),
  );

  fastify.delete(
    '/:videoId/like',
    handleFastifyRoute(async ({ request, sendSuccessMessage }) => {
      const { profile, likedVideo } = request;
      console.log(`Liked ID: ${likedVideo} Liker: ${profile}`);
      const existingLike = await prisma.videoLike.findFirst({
        where: {
          videoLikerProfileId: profile.id,
          likedVideoId: likedVideo.id,
        },
      });
      if (existingLike) {
        await prisma.videoLike.delete({
          where: {
            id: existingLike.id,
          },
        });
      }
      return sendSuccessMessage(t('Video unliked successfully.'));
    }),
  );
}
