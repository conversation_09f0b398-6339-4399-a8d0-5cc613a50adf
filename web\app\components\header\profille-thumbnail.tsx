import { Link, useFetcher } from '@remix-run/react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '#app/components/ui/button';
import { CircularProgress } from '#app/components/ui/circular-progress';
import { type RootProfile } from '#app/types/serialized-types.js';
import {
  LOGOUT_ROUTE,
  PROFILE_EDIT_BASIC_INFO_ROUTE,
  PROFILE_ME_ROUTE,
  SETTINGS_ACCOUNT_ROUTE,
} from '#shared/constants/routes';
import { calculateAge } from '#shared/utils/date';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import { Icon } from '../ui/icon';

type ProfileThumbnailProps = {
  profile: RootProfile;
};

const ProfileThumbnail: React.FC<ProfileThumbnailProps> = ({ profile }) => {
  const [profileProgress, setProfileProgress] = useState(0);
  const fetcher = useFetcher<{ profileProgress: number }>();
  const { t } = useTranslation();

  useEffect(() => {
    if (fetcher.state === 'idle' && !fetcher.data) {
      fetcher.load('/profile/progress');
    }
  }, [fetcher]);

  useEffect(() => {
    if (fetcher.data) {
      setProfileProgress(fetcher.data.profileProgress);
    }
  }, [fetcher.data]);

  const dropdownItems = [
    {
      label: 'View Profile',
      path: PROFILE_ME_ROUTE,
      icon: 'avatar',
    },
    {
      label: 'Edit Profile',
      path: PROFILE_EDIT_BASIC_INFO_ROUTE,
      icon: 'pencil-edit',
    },
    {
      label: 'Account settings',
      path: SETTINGS_ACCOUNT_ROUTE,
      icon: 'settings',
    },
  ];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="flex items-center">
        <div className="relative h-12 w-12">
          <CircularProgress
            progress={profileProgress}
            size={48}
            strokeWidth={3}
          />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="h-10 w-10 overflow-hidden rounded-full">
              {profile.mainPhoto?.smallUrl ? (
                <img
                  src={profile.mainPhoto.smallUrl}
                  alt="Profile"
                  className="h-full w-full object-cover"
                />
              ) : (
                <span className="flex h-full w-full items-center justify-center bg-gray-200 text-lg font-bold text-brand-primary">
                  {profile.firstName[0]}
                </span>
              )}
            </div>
          </div>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        sideOffset={12}
        className="w-64 rounded-xl p-4 shadow-lg"
      >
        <div className="relative flex flex-col items-center">
          <div className="relative mb-2 h-20 w-20">
            <CircularProgress
              progress={profileProgress}
              size={80}
              strokeWidth={6}
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="h-[68px] w-[68px] overflow-hidden rounded-full">
                {profile.mainPhoto?.smallUrl ? (
                  <img
                    src={profile.mainPhoto.smallUrl}
                    alt="Profile"
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <span className="flex h-full w-full items-center justify-center bg-gray-200 text-2xl font-bold text-brand-primary">
                    {profile.firstName[0]}
                  </span>
                )}
              </div>
            </div>
          </div>
          <div className="absolute top-16 rounded-md bg-gradient-to-r from-brand-secondary to-brand-primary px-3 py-0.5 text-xs text-white">
            {profileProgress}% COMPLETE
          </div>
          <p className="text-lg font-bold">
            {profile.firstName},{' '}
            {profile.dateOfBirth ? calculateAge(profile.dateOfBirth) : ''}
          </p>
          <p className="text-xs text-gray-500">@{profile.username}</p>
        </div>
        <DropdownMenuSeparator className="my-4" />
        {dropdownItems.map((item) => (
          <DropdownMenuItem key={item.path} asChild>
            <Link
              to={item.path}
              className="flex w-full cursor-pointer items-center gap-4 rounded-lg p-3 text-left text-base transition-colors hover:bg-gray-100"
            >
              <Icon name={item.icon} className="h-6 w-6 text-gray-600" />
              <span>{t(item.label)}</span>
            </Link>
          </DropdownMenuItem>
        ))}
        <DropdownMenuSeparator className="my-2" />
        <fetcher.Form method="post" action={LOGOUT_ROUTE}>
          <Button
            variant="ghost"
            type="submit"
            className="flex w-full cursor-pointer items-center gap-4 rounded-lg text-left text-base text-red-600 transition-colors hover:bg-red-50"
          >
            <Icon name="exit" className="h-6 w-6" />
            <span>{t('Logout')}</span>
          </Button>
        </fetcher.Form>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export { ProfileThumbnail };
