import {
  BodyTypeEnum,
  EthnicityEnum,
  EyeColorEnum,
  HairColorEnum,
} from './enums';

export const ONLINE_TIME_THRESHOLD_SECONDS = 60 * 20; // 20 minutes

// based on route parameters
export const RESERVED_PROFILE_NAMES = [
  'edit',
  'me',
  'photo-verify',
  'setup',
  'new',
  'settings',
];

export enum AGES {
  MIN_AGE = 18,
  MAX_AGE = 120,
  // the upper age men see
  UPPER_AGE_SLIDER_LOW = 60,
  // the upper age women see
  UPPER_AGE_SLIDER_HIGH = 80,
}

export const GENDER_LIST = [
  'MALE',
  'FEMALE',
  'TRANSGENDER_MALE',
  'TRANSGENDER_FEMALE',
  'NON_BINARY',
] as const;

export const NEVER_SOMETIMES_OFTEN_LIST = [
  'NEVER',
  'SOMETIMES',
  'OFTEN',
] as const;
export const YES_NO_LIST = ['YES', 'NO'] as const;
export const YES_NO_NOT_SURE_LIST = ['YES', 'NO', 'NOT_SURE'] as const;

export const BODY_TYPE_ENUM_LIST = [
  'PETITE',
  'SLIM',
  'ATHLETIC',
  'AVERAGE',
  'FEW_EXTRA_POUNDS',
  'FULL_FIGURED',
  'LARGE',
] as const;

export const RELATIONSHIP_ENUM_LIST = [
  'MARRIAGE',
  'SERIOUS_RELATIONSHIP',
  'OPEN_RELATIONSHIP',
  'FRIENDSHIP_OR_COMPANIONSHIP',
  'CASUAL',
  'UNSURE',
] as const;

export const RELIGION_ENUM_LIST = [
  'CHRISTIAN',
  'MUSLIM',
  'HINDU',
  'BUDDHIST',
  'JEWISH',
  'SPIRITUAL',
  'AGNOSTIC',
  'ATHEIST',
  'OTHER',
] as const;

export enum NOTIFICATION_TYPE_MAP {
  PROFILE_LIKE = 'profile-like',
  PROFILE_VIEW = 'profile-view',
  MESSAGE = 'message',
}

export const NOTIFICATION_TYPE_LIST = [
  NOTIFICATION_TYPE_MAP.MESSAGE,
  NOTIFICATION_TYPE_MAP.PROFILE_LIKE,
  NOTIFICATION_TYPE_MAP.PROFILE_VIEW,
] as const;

// eventually add more statuses here
export enum ONLINE_STATUS_MAP {
  ONLINE = 'ONLINE',
  OFFLINE = 'OFFLINE',
}

export const ETHNICITIES = (t: (arg0: string) => any) => [
  {
    label: t('Middle Eastern'),
    value: EthnicityEnum.MIDDLE_EASTERN,
  },
  {
    label: t('East Asian'),
    value: EthnicityEnum.EAST_ASIAN,
  },
  {
    label: t('South Asian'),
    value: EthnicityEnum.SOUTH_ASIAN,
  },
  {
    label: t('Southeast Asian'),
    value: EthnicityEnum.SOUTHEAST_ASIAN,
  },
  {
    label: t('Filipino'),
    value: EthnicityEnum.FILIPINO,
  },
  {
    label: t('Black'),
    value: EthnicityEnum.BLACK,
  },
  {
    label: t('White'),
    value: EthnicityEnum.WHITE,
  },
  {
    label: t('Hispanic'),
    value: EthnicityEnum.HISPANIC,
  },
  {
    label: t('Pacific Islander'),
    value: EthnicityEnum.PACIFIC_ISLANDER,
  },
  {
    label: t('Other'),
    value: EthnicityEnum.OTHER,
  },
];

export const EYE_COLORS = (t: (arg0: string) => any) => [
  { value: EyeColorEnum.BLACK, label: t('Black') },
  { value: EyeColorEnum.BLUE, label: t('Blue') },
  { value: EyeColorEnum.BROWN, label: t('Brown') },
  { value: EyeColorEnum.GREEN, label: t('Green') },
  { value: EyeColorEnum.GREY, label: t('Grey') },
  { value: EyeColorEnum.HAZEL, label: t('Hazel') },
];

export const BODY_TYPES = (t: (arg0: string) => any) => [
  { value: BodyTypeEnum.PETITE, label: t('Petite') },
  { value: BodyTypeEnum.SLIM, label: t('Slim') },
  { value: BodyTypeEnum.ATHLETIC, label: t('Athletic') },
  { value: BodyTypeEnum.AVERAGE, label: t('Average') },
  {
    value: BodyTypeEnum.FEW_EXTRA_POUNDS,
    label: t('Few Extra Pounds'),
  },
  { value: BodyTypeEnum.FULL_FIGURED, label: t('Full Figured') },
  { value: BodyTypeEnum.LARGE, label: t('Large') },
];

export const HAIR_COLORS = (t: (arg0: string) => any) => [
  { value: HairColorEnum.BALD_SHAVEN, label: t('Bald/Shaven') },
  { value: HairColorEnum.BLACK, label: t('Black') },
  { value: HairColorEnum.BLONDE, label: t('Blonde') },
  { value: HairColorEnum.BROWN, label: t('Brown') },
  { value: HairColorEnum.GREY_WHITE, label: t('Grey/White') },
  { value: HairColorEnum.LIGHT_BROWN, label: t('Light Brown') },
  { value: HairColorEnum.RED, label: t('Red') },
  {
    value: HairColorEnum.CHANGES_FREQUENTLY,
    label: t('Changes Frequently'),
  },
];
