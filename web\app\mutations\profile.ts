import { useMutation } from '@tanstack/react-query';
import { apiDelete } from '#app/utils/fetch.client';

export function useProfilePhotoMutation() {
  return useMutation({
    mutationFn: async ({ photoId }: { photoId: string }) => {
      const res = await apiDelete<{ message: string }>(
        `/api/profile/photos/${photoId}`,
      );

      return res;
    },
  });
}

export function useProfilePhotoDeleteMutation() {
  return useMutation({
    mutationFn: async ({ photoId }: { photoId: string }) => {
      const res = await apiDelete<{ message: string }>(
        `/api/profile/photos/${photoId}`,
      );

      return res;
    },
  });
}
