import { type FieldsFromConfig } from '#shared/types/forms';
import { type CreateValidator } from '#shared/types/validators';

export function enrichFieldsWithDefaultValues<
  TCreateValidator extends CreateValidator,
  TDBEntity extends Record<string, any>,
>(fields: FieldsFromConfig<TCreateValidator>, dbEntity: TDBEntity) {
  return fields.map((field) => {
    return {
      ...field,
      defaultValue: field.defaultValue ?? dbEntity[field.name],
    };
  }) as FieldsFromConfig<TCreateValidator>;
}
