import { useNavigate } from '@remix-run/react';
import { useState } from 'react';
import Typography from '#app/components/typography';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '#app/components/ui/dropdown-menu';
import { Icon } from '#app/components/ui/icon';
import { useNotificationContext } from '#app/contexts';
import { cn } from '#app/utils/misc';
import { NOTIFICATION_TYPE_MAP } from '#shared/constants/profile';
import {
  ACTIVITY_CONVERSATION_LIST_ROUTE,
  ACTIVITY_LIKES_ME_ROUTE,
  ACTIVITY_PROFILE_VIEWS_ROUTE,
} from '#shared/constants/routes';
import { type NotificationType } from '#shared/types/profile';

const Notification: React.FC = () => {
  const [openDropdown, setOpenDropdown] = useState(false);
  const { notificationData, clearNotificationsOfType } =
    useNotificationContext();

  const navigate = useNavigate();

  const totalUnreadNotification =
    (notificationData?.messageNotificationsCount || 0) +
    (notificationData?.profileLikeNotificationsCount || 0) +
    (notificationData?.profileViewNotificationsCount || 0);

  const notifications = [
    {
      label: 'Messages',
      count: notificationData?.messageNotificationsCount || 0,
      type: NOTIFICATION_TYPE_MAP.MESSAGE,
      route: ACTIVITY_CONVERSATION_LIST_ROUTE,
    },
    {
      label: 'Likes',
      count: notificationData?.profileLikeNotificationsCount || 0,
      type: NOTIFICATION_TYPE_MAP.PROFILE_LIKE,
      route: ACTIVITY_LIKES_ME_ROUTE,
    },
    {
      label: 'Profile Views',
      count: notificationData?.profileViewNotificationsCount || 0,
      type: NOTIFICATION_TYPE_MAP.PROFILE_VIEW,
      route: ACTIVITY_PROFILE_VIEWS_ROUTE,
    },
  ];

  const handleNotificationClick = (
    type: NotificationType,
    route: string,
    count: number,
  ) => {
    navigate(route);
    if (count > 0) {
      clearNotificationsOfType(type);
    }
  };

  return (
    <DropdownMenu
      onOpenChange={() => {
        setOpenDropdown(!openDropdown);
      }}
      open={openDropdown}
    >
      <DropdownMenuTrigger className="relative flex items-center justify-center">
        <div className="flex flex-col items-center justify-center">
          <div className="relative">
            <Icon
              name="notifications-outline"
              className={cn(
                'h-[24px] w-[24px]',
                openDropdown ? 'text-brand-primary' : '',
              )}
            />

            {totalUnreadNotification > 0 && (
              <span
                className={`absolute -right-2 -top-1 flex h-3 w-3 items-center justify-center rounded-full bg-brand-primary text-[8px] text-white`}
              >
                {totalUnreadNotification > 99 ? '99' : totalUnreadNotification}
              </span>
            )}
          </div>
          <Typography
            variant="small"
            className={cn(
              'font-medium',
              openDropdown
                ? 'bg-gradient-to-r from-brand-secondary to-brand-primary bg-clip-text text-transparent'
                : '',
            )}
          >
            Notifications
          </Typography>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent sideOffset={4}>
        {notifications.map((notification) => (
          <DropdownMenuItem
            key={notification.label}
            onClick={() =>
              handleNotificationClick(
                notification.type,
                notification.route,
                notification.count,
              )
            }
          >
            <p>
              {notification.label}: {notification.count}
            </p>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export { Notification };
