import { useLoaderData } from '@remix-run/react';
import ConversationsContainer from '#app/components/activity/conversations-container';
import { useClearNotificationOfType } from '#app/contexts/notification-context';
import { LayoutType } from '#app/types/layouts';
import { noCacheHeaders } from '#app/utils/cache-headers.js';
import { getEnrichedConversations } from '#app/utils/conversations.server';
import { cn } from '#app/utils/misc.js';
import { handleLoaderForProfileRoute } from '#app/utils/remix-helpers.server';
import { prisma } from '#server/utils/db.server.js';
import { SocialProvider } from '#shared/constants/enums.js';
import { NOTIFICATION_TYPE_MAP } from '#shared/constants/profile';

export const handle = { layout: LayoutType.ROOT };

export const headers = noCacheHeaders;

export const loader = handleLoaderForProfileRoute(async ({ profile, privateUser }) => {
  const enrichedConversations = await getEnrichedConversations(profile);

  const hasFacebookLogin = await prisma.socialConnection.findFirst({
    where: {
      providerName: SocialProvider.FACEBOOK,
      privateUserId: privateUser.id,
    },
  }) ?? false;

  return { enrichedConversations, profile, hasFacebookLogin };
});

export default function Conversations() {
  const { enrichedConversations, profile, hasFacebookLogin } = useLoaderData<typeof loader>();
  useClearNotificationOfType(NOTIFICATION_TYPE_MAP.MESSAGE);

  return (
    <div className="w-full max-w-[600px] h-full  mx-auto flex-1 flex flex-col tablet-min:pb-4 pb-0 pt-0 sm:pt-4">
      {
        hasFacebookLogin && (
          <div className={
            cn(
              'flex items-center',
              'mb-3'
            )
          }>
            <a
              href="https://m.me/rn/filipinameetapp?topic=conversation%20updates"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 px-4 py-2 rounded-xl font-medium text-white bg-[#3b5998] hover:bg-[#334f88] transition-colors duration-200 shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#3b5998]"
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5"><path d="M22.675 0h-21.35C.595 0 0 .592 0 1.326v21.348C0 23.408.595 24 1.325 24h11.495v-9.294H9.692v-3.622h3.128V8.413c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.797.143v3.24l-1.918.001c-1.504 0-1.797.715-1.797 1.763v2.313h3.587l-.467 3.622h-3.12V24h6.116C23.406 24 24 23.408 24 22.674V1.326C24 .592 23.406 0 22.675 0"/></svg>
              Receive Updates on Messenger
            </a>
          </div>
        )
      }
      <ConversationsContainer
        conversations={enrichedConversations ?? []}
        currentUserId={profile.id}
      />
    </div>
  );
}
