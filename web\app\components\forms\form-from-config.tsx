import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  useActionData,
  useFetcher,
  useNavigate,
  useNavigation,
} from '@remix-run/react';
import React, { useEffect, useMemo, useRef } from 'react';
import {
  useForm,
  type DefaultValues,
  type FieldValues,
  type Path,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast as showToast } from 'sonner';
import { Button } from '#app/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '#app/components/ui/tooltip';

import {
  isClientBadRequestError,
  isClientError,
  isClientInternalServerError,
  isClientSuccess,
  isClientValidationError,
} from '#app/utils/handle-errors.client';
import { DELAYED_REDIRECT_TIME } from '#shared/constants/ui';
import { type ClientFormErrorType } from '#shared/types/errors.client.js';
import { type FieldFromConfigType } from '#shared/types/forms';
import { type TranslationFunction } from '#shared/types/translations';
import { type ZodValidator } from '#shared/types/validators';
import { Field } from './fields/field';

export const FormType = {
  REMIX: 'remix',
  FETCHER: 'fetcher',
  PRIMITIVE: 'primitive',
} as const;

export type FormType = (typeof FormType)[keyof typeof FormType];

interface Props<T extends FieldValues>
  extends React.ComponentProps<typeof Form> {
  fields: readonly FieldFromConfigType<T>[];
  createValidator?: (t: TranslationFunction) => ZodValidator<T>;
  formType?: FormType;
  handleReset?: () => void;
  showResetButton?: boolean;
  activeTab?: string;
  basicFields?: string[];
  renderFields?: ({
    fields,
    submitButton,
    resetButton,
  }: {
    fields: Record<Path<T>, { node: React.ReactNode }>;
    submitButton: React.ReactNode;
    resetButton: React.ReactNode;
  }) => React.ReactNode;
  onSuccess?: () => void;
}

type ActionData<T> = T | ClientFormErrorType | undefined;

export function FormFromConfig<T extends FieldValues>({
  fields,
  createValidator,
  formType = FormType.REMIX,
  activeTab,
  basicFields = [],
  handleReset,
  showResetButton,
  renderFields,
  onSuccess,
  ...formProps
}: Props<T>) {
  const { t } = useTranslation();
  const _actionData = useActionData<ActionData<T>>();
  const navigation = useNavigation();
  const navigate = useNavigate();
  const fetcher = useFetcher();
  const formRef = useRef<HTMLFormElement>(null);

  const basicFieldsSet = new Set(basicFields);
  const shouldShowField = (fieldName: string) =>
    activeTab === 'basic'
      ? basicFieldsSet.has(fieldName)
      : !basicFieldsSet.has(fieldName);

  const hideSubmitButton = fields.some(
    (field) => !!field.triggerFormSubmitOnChange,
  );

  const useFetcherForForm = formType === FormType.FETCHER;

  const validator = useMemo(
    () => (createValidator ? createValidator(t) : undefined),
    [createValidator, t],
  );

  const actionData: ActionData<T> = useMemo(() => {
    return useFetcherForForm
      ? (fetcher.data as typeof _actionData)
      : _actionData;
  }, [useFetcherForForm, fetcher.data, _actionData]);

  const state = useMemo(() => {
    return useFetcherForForm ? fetcher.state : navigation.state;
  }, [useFetcherForForm, fetcher.state, navigation.state]);

  const defaultValues = useMemo(() => {
    const acc = {} as DefaultValues<T>;
    for (const field of fields) {
      // @ts-expect-error
      acc[field.name] = field.defaultValue;
    }
    return acc;
  }, [fields]);

  const {
    control,
    setError,
    clearErrors,
    setFocus,
    reset,
    formState: { isDirty, errors },
  } = useForm<T>({
    defaultValues,
    resolver: validator ? zodResolver(validator) : undefined,
    mode: 'onBlur',
  });

  // TODO: figure out why we get a new translation fuction ever re-render
  const tRef = useRef(t);
  tRef.current = t;

  useEffect(() => {
    if (!isClientError(actionData)) return;

    const handleErrors = () => {
      if (isClientValidationError(actionData)) {
        const { errorDetail } = actionData;

        let firstErrorFieldSet = false;
        for (const [fieldName, fieldErrors] of Object.entries(errorDetail)) {
          if (
            Array.isArray(fieldErrors) &&
            fieldErrors.length > 0 &&
            fieldErrors[0]
          ) {
            setError(fieldName as Path<T>, {
              type: 'manual',
              message: tRef.current(fieldErrors[0]),
            });

            if (!firstErrorFieldSet) {
              setFocus(fieldName as Path<T>);
              firstErrorFieldSet = true;
            }
          }
        }
      } else if (isClientBadRequestError(actionData)) {
        showToast.error(tRef.current('Please check your input and try again'), {
          id: 'bad-request-error-toast',
          description: tRef.current(actionData.error),
        });
      } else if (isClientInternalServerError(actionData)) {
        showToast.error(tRef.current('Internal Server Error'), {
          id: 'internal-server-error-toast',
          description: tRef.current(
            'An unexpected error occurred. Please try again later.',
          ),
        });
      }
    };

    handleErrors();
  }, [actionData, setError, setFocus]);

  useEffect(() => {
    if (isClientSuccess(actionData)) {
      showToast.success(tRef.current('Success'), {
        id: 'success-toast',
        description: tRef.current(actionData.message),
      });

      if (actionData.options?.delayedRedirectTo) {
        const { delayedRedirectTo } = actionData.options;
        setTimeout(() => {
          navigate(delayedRedirectTo);
        }, DELAYED_REDIRECT_TIME);
      }

      if (onSuccess) onSuccess();
    }
  }, [actionData, navigate, onSuccess]);

  const isValid = Object.keys(errors).length === 0;

  const resetButton = showResetButton && (
    <Button
      type="button"
      onClick={() => {
        handleReset?.();
        reset(defaultValues);
      }}
      variant="ghost"
    >
      {t('Reset')}
    </Button>
  );

  const submitButton = hideSubmitButton ? null : !isValid ? (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className="inline-block w-full">
            <Button
              type="submit"
              disabled
              className="pointer-events-none w-full"
            >
              {t('Submit')}
            </Button>
          </span>
        </TooltipTrigger>
        <TooltipContent>
          {!isDirty
            ? t('Please fill out the form')
            : t('Please fix the form errors')}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  ) : (
    <Button type="submit" disabled={state === 'submitting'} className="w-full">
      {activeTab ? t('Apply') : t('Submit')}
    </Button>
  );

  const fieldsContent = useMemo(
    () =>
      fields.reduce(
        (acc, { triggerFormSubmitOnChange, asyncOnChange, ...field }) => {
          const handleFieldChange = (value: any) => {
            // Clear error for this field
            if (errors[field.name as Path<T>]) {
              clearErrors(field.name as Path<T>);
            }

            // Execute async onChange handler if provided
            asyncOnChange?.(value as never).catch((error) => {
              console.error('Error in asyncOnChange', error);
            });

            // If the field triggers form submission on change
            if (triggerFormSubmitOnChange && field.type === 'photo-upload') {
              if (formRef.current) {
                const formData = new FormData(formRef.current);
                const photoData = formData.get(field.name);

                // Prevent submitting empty files
                if (photoData instanceof File && photoData.size === 0) {
                  return;
                }

                formRef.current.submit();
              } else {
                console.error('Form ref is not set');
              }
            }
          };

          acc[field.name] = {
            node: (
              <Field
                key={field.name}
                control={control}
                customOnChange={handleFieldChange}
                {...field}
              />
            ),
          };
          return acc;
        },
        {} as Record<string, { node: React.ReactNode }>,
      ),
    [fields, control, errors, clearErrors],
  );

  const innerContent = renderFields ? (
    renderFields({ fields: fieldsContent, submitButton, resetButton })
  ) : (
    <>
      <div className="space-y-6 mb-6">
        {fields.map(
          ({ triggerFormSubmitOnChange, asyncOnChange, ...field }) => {
            const handleFieldChange = (value: any) => {
              // Clear error for this field
              if (errors[field.name as Path<T>]) {
                clearErrors(field.name as Path<T>);
              }

              // Execute async onChange handler if provided
              asyncOnChange?.(value as never).catch((error) => {
                console.error('Error in asyncOnChange', error);
              });

              // If the field triggers form submission on change
              if (triggerFormSubmitOnChange && field.type === 'photo-upload') {
                if (formRef.current) {
                  const formData = new FormData(formRef.current);
                  const photoData = formData.get(field.name);

                  // Prevent submitting empty files
                  if (photoData instanceof File && photoData.size === 0) {
                    return;
                  }

                  if (useFetcherForForm) {
                    fetcher.submit(formData, {
                      method: formProps.method,
                      action: formProps.action,
                      encType: formProps.encType,
                    });
                    return;
                  }

                  // Uses native form submission forcing a page navigation
                  formRef.current.submit();
                } else {
                  console.error('Form ref is not set');
                }
              }
            };

            return shouldShowField(field.name) ? (
              <Field
                key={field.name}
                control={control}
                customOnChange={handleFieldChange}
                {...field}
              />
            ) : null;
          },
        )}
      </div>

      {submitButton}
      {resetButton}
    </>
  );

  switch (formType) {
    case FormType.FETCHER:
      return (
        <fetcher.Form ref={formRef} {...formProps}>
          {innerContent}
        </fetcher.Form>
      );
    case FormType.PRIMITIVE:
      return (
        <form ref={formRef} {...formProps}>
          {innerContent}
        </form>
      );
    default:
      return (
        <Form ref={formRef} {...formProps}>
          {innerContent}
        </Form>
      );
  }
}
