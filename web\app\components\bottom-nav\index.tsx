import { NavLink } from '@remix-run/react';
import { navigationItems } from '#app/constants/navigation';
import { useMobile } from '#app/contexts/mobile-context.js';
import { cn } from '#app/utils/misc';

const BottomNavigation = () => {
  const bottomNavItems = [...navigationItems];

  const { isMobile } = useMobile();

  return (
    <>
      {isMobile ? (
        <nav className="tablet-min:hidden w-full fixed bottom-0 left-0 bg-white z-[99] border-t px-4 h-14 flex items-center justify-center">
          <ul className="flex justify-around grow items-center">
            {bottomNavItems.map((item) => (
              <li key={item.label}>
                <NavLink
                  className={({ isActive }) =>
                    cn(isActive ? 'text-brand-primary' : '')
                  }
                  key={item.label}
                  to={item.path}
                >
                  <item.icon fontSize={24} />
                </NavLink>
              </li>
            ))}
          </ul>
        </nav>
      ) : null}
    </>
  );
};

export default BottomNavigation;
