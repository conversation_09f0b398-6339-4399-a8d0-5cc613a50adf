import * as Collapsible from '@radix-ui/react-collapsible';
import React, { useState } from 'react';
import { Button } from '#app/components/ui/button';
import { Icon } from '#app/components/ui/icon.js';

interface CollapsibleListProps {
  children: React.ReactNode;
  title: string;
  columns?: number;
  subtitle?: string;
}

const CollapsibleList = ({
  children,
  title,
  columns,
  subtitle,
}: CollapsibleListProps) => {
  const [open, setOpen] = useState(true);
  const childrenArray = React.Children.toArray(children);

  return (
    <Collapsible.Root open={open} onOpenChange={setOpen}>
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-semibold">{title}</h2>
            {subtitle && <p className="text-neutral-400">{subtitle}</p>}
          </div>
          <Collapsible.Trigger asChild>
            <Button variant="ghost" className="desktop-min:hidden">
              <Icon
                name="chevron-down-outline"
                className={`transition-transform duration-300 ${
                  open ? 'rotate-180' : ''
                }`}
              />
            </Button>
          </Collapsible.Trigger>
        </div>
        <Collapsible.Content>
          <div
            className={`grid ${columns ? `grid-cols-${columns}` : 'grid-cols-2'} tablet:gap-0 gap-8 tablet:grid-cols-1`}
          >
            {childrenArray.map((child, index) => (
              <div
                key={index}
                className={`${
                  !columns &&
                  index === childrenArray.length - 1 &&
                  childrenArray.length % 2 !== 0
                    ? 'col-span-2'
                    : ''
                } tablet:col-span-1`}
              >
                {child}
              </div>
            ))}
          </div>
        </Collapsible.Content>
      </div>
    </Collapsible.Root>
  );
};

export default CollapsibleList;
