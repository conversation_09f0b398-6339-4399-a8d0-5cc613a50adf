import { Alert } from 'react-native';
import { type HttpMethod } from '#shared/types/api';
import { type APIError, isAPIError } from '#shared/utils/api-errors.client';
import { handleFetchError } from '#shared/utils/handle-errors.client';
import { apiFetch as baseApiFetch } from '#shared/utils/fetch';

type FetchOptions = Omit<Parameters<typeof baseApiFetch>[2], 'hanldleError'>;

export async function handleApiCall<T>(
  apiCall: Promise<T>,
  options?: {
    onApiSuccess?: (result: T) => void;
    onApiFailure?: (error: APIError) => void;
    onFinally?: () => void;
  },
) {
  const { onApiSuccess, onApiFailure, onFinally } = options || {};
  try {
    const result = await apiCall;
    if (onApiSuccess) {
      onApiSuccess(result);
    }
  } catch (error) {
    console.log('error', error);
    if (isAPIError(error)) {
      Alert.alert(
        'API call failed',
        error.message || 'An unexpected error occurred.',
      );
      onApiFailure?.(error);
    } else {
      Alert.alert('An unexpected error occurred');
    }
    // Show failure toast message
    console.error('API call failed:', error); // Replace with actual toast implementation
  } finally {
    onFinally?.();
  }
}

// Fetch logic that is similar to what we have in the client
async function apiFetch<T>(
  url: string,
  method: HttpMethod,
  options?: FetchOptions,
): Promise<T> {
  const fullUrl = `${process.env.EXPO_PUBLIC_API_URL}${url}`;
  return baseApiFetch(fullUrl, method, {
    ...options,
    handleError: handleFetchError,
  });
}

export async function apiGet<T>(
  url: string,
  options?: FetchOptions,
): Promise<T> {
  return apiFetch<T>(url, 'GET', options);
}

export async function apiPost<T>(
  url: string,
  body: any,
  options?: FetchOptions,
): Promise<T> {
  return apiFetch<T>(url, 'POST', { ...options, body: JSON.stringify(body) });
}

export async function apiPut<T>(
  url: string,
  body: any,
  options?: FetchOptions,
): Promise<T> {
  return apiFetch<T>(url, 'PUT', { ...options, body: JSON.stringify(body) });
}

export async function apiDelete<T>(
  url: string,
  options?: FetchOptions,
): Promise<T> {
  return apiFetch<T>(url, 'DELETE', options);
}

export async function apiPatch<T>(
  url: string,
  body: any,
  options?: FetchOptions,
): Promise<T> {
  return apiFetch<T>(url, 'PATCH', { ...options, body: JSON.stringify(body) });
}
