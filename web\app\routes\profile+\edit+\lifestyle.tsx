import { useLoaderData } from '@remix-run/react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { FormFromConfig } from '#app/components/forms/form-from-config';
import { ProfileLayout } from '#app/components/profile/profile-layout.js';

import { enrichFieldsWithDefaultValues } from '#app/utils/forms';
import {
  handleFormActionForProfile,
  handleLoaderForProfileRoute,
} from '#app/utils/remix-helpers.server';
import { t } from '#server/utils/api.server';
import { prisma } from '#server/utils/db.server';
import {
  NeverSometimesOften,
  ReligionEnum,
  YesNo,
  YesNoNotSure,
} from '#shared/constants/enums';
import { type FieldsFromConfig } from '#shared/types/forms';
import { InterestType } from '#shared/types/interests';
import { type TranslationFunction } from '#shared/types/translations';
import {
  getNeverSometimesOftenFormOptions,
  getReligionEnumFormOptions,
  getYesNoFormOptions,
  getYesNoNotSureFormOptions,
} from '#shared/utils/form-options';
import { createOptionalEnumValidator } from '#shared/utils/validators';

const createLifestyleValidator = (t: TranslationFunction) => {
  return z.object({
    drinking: createOptionalEnumValidator(
      NeverSometimesOften,
      t('Drinking must be a valid option'),
    ),
    smoking: createOptionalEnumValidator(
      NeverSometimesOften,
      t('Smoking must be a valid option'),
    ),
    haveChildren: createOptionalEnumValidator(
      YesNo,
      t('Have Children must be a valid option'),
    ),
    wantChildren: createOptionalEnumValidator(
      YesNoNotSure,
      t('Want Children must be a valid option'),
    ),
    religion: createOptionalEnumValidator(
      ReligionEnum,
      t('Religion must be a valid option'),
    ),
    interests: z
      .union([
        z.array(z.string()),
        z.string().transform((str) => str.split(',').map((s) => s.trim())),
      ])
      .optional(),
  });
};

export const loader = handleLoaderForProfileRoute(async ({ profile }) => {
  const lifestyle = await prisma.lifestyle.findUnique({
    where: { profileId: profile.id },
  });
  return {
    lifestyle,
  };
});

export const action = handleFormActionForProfile(
  createLifestyleValidator,
  async ({ profile, data, successWithMessage }) => {
    const { interests, ...rest } = data;
    await prisma.lifestyle.upsert({
      where: { profileId: profile.id },
      update: {
        ...rest,
        interests: interests,
      },
      create: {
        ...rest,
        interests: interests,
        profile: { connect: { id: profile.id } },
      },
    });
    return successWithMessage(t('Lifestyle updated successfully'));
  },
);

export default function EditLifestyleRoute() {
  // enums come from the loader
  const { t } = useTranslation();
  const { lifestyle } = useLoaderData<typeof loader>();

  // Array of Interests
  const interests = useMemo(() => {
    return [
      { icon: '🌄', label: t('Adventure'), value: InterestType.Adventure },
      { icon: '🎨', label: t('Art'), value: InterestType.Art },
      { icon: '📚', label: t('Books'), value: InterestType.Books },
      { icon: '🍳', label: t('Cooking'), value: InterestType.Cooking },
      { icon: '💃', label: t('Dance'), value: InterestType.Dance },
      { icon: '💪', label: t('Fitness'), value: InterestType.Fitness },
      { icon: '🎮', label: t('Gaming'), value: InterestType.Gaming },
      { icon: '🌻', label: t('Gardening'), value: InterestType.Gardening },
      { icon: '🥾', label: t('Hiking'), value: InterestType.Hiking },
      { icon: '🧘', label: t('Meditation'), value: InterestType.Meditation },
      { icon: '🎬', label: t('Movies'), value: InterestType.Movies },
      { icon: '🎶', label: t('Music'), value: InterestType.Music },
      { icon: '🌳', label: t('Nature'), value: InterestType.Nature },
      { icon: '📸', label: t('Photography'), value: InterestType.Photography },
      { icon: '📜', label: t('Poetry'), value: InterestType.Poetry },
      { icon: '📖', label: t('Reading'), value: InterestType.Reading },
      { icon: '🏃', label: t('Running'), value: InterestType.Running },
      { icon: '🛍️', label: t('Shopping'), value: InterestType.Shopping },
      { icon: '🏅', label: t('Sports'), value: InterestType.Sports },
      { icon: '🏄', label: t('Surfing'), value: InterestType.Surfing },
      { icon: '🏊', label: t('Swimming'), value: InterestType.Swimming },
      { icon: '💻', label: t('Technology'), value: InterestType.Technology },
      { icon: '🎭', label: t('Theater'), value: InterestType.Theater },
      { icon: '✈️', label: t('Travel'), value: InterestType.Travel },
      {
        icon: '🤝',
        label: t('Volunteering'),
        value: InterestType.Volunteering,
      },
      { icon: '🍷', label: t('Wine'), value: InterestType.Wine },
      { icon: '🧘‍♀️', label: t('Yoga'), value: InterestType.Yoga },
      {
        icon: '👩‍🍳',
        label: t('Cooking Classes'),
        value: InterestType.CookingClasses,
      },
      { icon: '🗣️', label: t('Languages'), value: InterestType.Languages },
      { icon: '🎉', label: t('Festivals'), value: InterestType.Festivals },
    ];
  }, [t]);

  const fields: FieldsFromConfig<typeof createLifestyleValidator> =
    useMemo(() => {
      let initialFields: FieldsFromConfig<typeof createLifestyleValidator> = [
        {
          name: 'drinking',
          label: t('Drinking habits'),
          type: 'segmented-toggle',
          options: getNeverSometimesOftenFormOptions(t),
          defaultValue: lifestyle?.drinking?.toString(),
        },
        {
          name: 'smoking',
          label: t('Smoking habits'),
          type: 'segmented-toggle',
          options: getNeverSometimesOftenFormOptions(t),
          defaultValue: lifestyle?.smoking?.toString(),
        },
        {
          name: 'haveChildren',
          label: t('Have Children'),
          type: 'segmented-toggle',
          options: getYesNoFormOptions(t),
          defaultValue: lifestyle?.haveChildren?.toString(),
        },
        {
          name: 'wantChildren',
          label: t('Want Children'),
          type: 'segmented-toggle',
          options: getYesNoNotSureFormOptions(t),
          defaultValue: lifestyle?.wantChildren?.toString(),
        },
        {
          name: 'religion',
          label: t('Religion'),
          type: 'select',
          options: getReligionEnumFormOptions(t),
          defaultValue: lifestyle?.religion?.toString(),
        },
        // TODO: add interests
        {
          name: 'interests',
          label: t('Interests'),
          type: 'multi-select',
          options: interests,
          defaultValue: lifestyle?.interests,
        },
      ] as const;

      if (lifestyle) {
        initialFields = enrichFieldsWithDefaultValues(initialFields, lifestyle);
      }

      return initialFields;
    }, [interests, lifestyle, t]);

  return (
    <ProfileLayout>
      <div className="form-container">
        <FormFromConfig
          method="POST"
          action="/profile/edit/lifestyle"
          fields={fields}
          showResetButton
          createValidator={createLifestyleValidator}
          renderFields={({ fields, submitButton, resetButton }) => {
            return (
              <div className="space-y-4">
                <div className="mb-6">
                  <h2 className="text-2xl font-bold">{t('Lifestyle')}</h2>
                  <p className="text-sm text-muted-foreground">
                    {t('Select your lifestyle habits and choices.')}
                  </p>
                </div>
                <div className="grid grid-cols-2 desktop-sm:grid-cols-1 md:gap-8 items-start">
                  <div className="flex flex-col gap-2 [&>div>label]:pb-3 [&>div>div]:mb-7">
                    {fields.drinking?.node}
                    {fields.smoking?.node}
                    {fields.haveChildren?.node}
                    {fields.wantChildren?.node}
                  </div>
                  <div className="flex flex-col gap-2 [&>div>label]:pb-3 [&>div>button]:mb-7">
                    {fields.religion?.node}
                    {fields.interests?.node}
                  </div>
                </div>

                <div className="ml-auto flex w-fit gap-2 tablet:flex-col tablet:w-full tablet:gap-0">
                  {submitButton}
                  {resetButton}
                </div>
              </div>
            );
          }}
        />
      </div>
    </ProfileLayout>
  );
}
