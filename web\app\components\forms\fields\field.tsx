import { useController, type Control, type FieldValues } from 'react-hook-form';
import { type FieldFromConfigType } from '#shared/types/forms';
import { FieldFromConfig } from './field-from-config';

type FieldProps<T extends FieldValues> = FieldFromConfigType<T> & {
  control: Control<T>;
  customOnChange?: (value: T) => void;
};

export function Field<T extends FieldValues>({
  name,
  label,
  control,
  customOnChange,
  ...rest
}: FieldProps<T>) {
  const {
    field: { ref, onChange, ...field },
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const handleChange = (value: T) => {
    onChange(value);
    customOnChange?.(value);
  };

  return (
    <FieldFromConfig
      {...field}
      {...rest}
      onChange={handleChange}
      id={name}
      labelProps={{ children: label }}
      errors={error ? [error] : []}
      inputRef={ref}
    />
  );
}
