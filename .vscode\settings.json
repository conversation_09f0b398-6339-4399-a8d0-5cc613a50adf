{"typescript.preferences.autoImportFileExcludePatterns": ["@remix-run/server-runtime", "@remix-run/router", "express", "@radix-ui/**", "@react-email/**", "react-router-dom", "react-router", "stream/consumers", "node:stream/consumers", "node:test", "console", "node:console"], "editor.tabSize": 2, "editor.insertSpaces": true, "editor.formatOnSave": true, "prettier.enable": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "always"}, "[prisma]": {"editor.defaultFormatter": "Prisma.prisma"}, "eslint.enable": true, "eslint.codeAction.showDocumentation": {"enable": true}, "typescript.tsdk": "node_modules/typescript/lib"}