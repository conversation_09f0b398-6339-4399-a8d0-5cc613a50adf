import { useFetcher } from '@remix-run/react';
import { Loader2Icon } from 'lucide-react';
import { useCallback, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import PlusIcon from '#app/components/icons/PlusIcon';
import XIcon from '#app/components/icons/XIcon';
import { cn } from '#app/utils/misc';
import { isImageLarge } from '#app/utils/photo';
import { PhotoType } from '#shared/constants/enums';
import { type PhotoWithUrls } from '#shared/types/media';

interface GalleryPhotosProps {
  galleryPhotos: PhotoWithUrls[];
  onDeletePhoto: (photoId: string) => Promise<void>;
}

const GalleryPhotos = ({
  galleryPhotos,
  onDeletePhoto,
}: GalleryPhotosProps) => {
  const { t } = useTranslation();
  const galleryPhotoFields = [
    {
      name: 'photo',
      type: 'photo-upload',
      label: t('Gallery photos'),
      placeholder: t('Share more about yourself with additional photos'),
      triggerFormSubmitOnChange: true,
      hidePreview: true,
    },
    {
      name: 'photoType',
      type: 'hidden',
      defaultValue: PhotoType.GALLERY,
    },
  ] as const;
  const fetcher = useFetcher();

  // Create refs for each photo position
  const formRefs = {
    large1: useRef<HTMLFormElement>(null),
    large2: useRef<HTMLFormElement>(null),
    small1: useRef<HTMLFormElement>(null),
    small2: useRef<HTMLFormElement>(null),
    small3: useRef<HTMLFormElement>(null),
    small4: useRef<HTMLFormElement>(null),
  };

  // Shared configuration for all gallery photo forms
  const photoUploadConfig = {
    method: 'post' as const,
    action: '/profile/edit/photos',
    encType: 'multipart/form-data' as const,
  };

  // Track the uploading state for each photo
  const [uploadingPhotos, setUploadingPhotos] = useState<boolean[]>(
    // Initialize with `false` for each photo slot
    new Array(galleryPhotos.length).fill(false),
  );

  const PhotoUploadForm = useCallback(
    ({
      formRef,
      position,
      size,
    }: {
      formRef: React.RefObject<HTMLFormElement>;
      position: number;
      size: 'large' | 'small';
    }) => {
      const photo = galleryPhotos[position];

      if (photo) {
        return (
          <>
            <img
              src={photo.mediumUrl}
              alt={`Gallery photo ${position + 1}`}
              className="w-full h-full object-cover"
            />
            <button
              type="button"
              onClick={() => onDeletePhoto(photo.fileId)}
              className="absolute top-2 right-2 p-1 rounded-full"
              aria-label={t('Delete photo')}
            >
              <div
                className={cn(
                  'rounded-lg bg-red-100 hover:bg-red-300 flex items-center justify-center group transition-colors duration-200',
                  isImageLarge(size) ? 'h-8 w-8' : 'h-6 w-6',
                )}
              >
                <XIcon
                  className={cn(
                    'text-red-500 group-hover:text-red-100 transition-colors duration-200',
                    isImageLarge(size) ? 'w-2.5 h-2.5' : 'w-2 h-2',
                  )}
                />
              </div>
            </button>
          </>
        );
      }

      return (
        <fetcher.Form
          ref={formRef}
          {...photoUploadConfig}
          className="w-full h-full flex items-center justify-center"
        >
          {galleryPhotoFields.map((field) => {
            if (field.type === 'hidden') {
              return (
                <input
                  key={field.name}
                  type="hidden"
                  name={field.name}
                  value={field.defaultValue}
                />
              );
            }

            if (field.type === 'photo-upload') {
              return (
                <label
                  key={field.name}
                  htmlFor={`photo-${position}`}
                  className="cursor-pointer flex items-center justify-center w-full h-full"
                >
                  {uploadingPhotos[position] ? (
                    <div className="absolute inset-0 left-0 top-0 z-10 flex h-full w-full items-center justify-center bg-black opacity-50">
                      <Loader2Icon className="h-12 w-12 animate-spin text-white" />
                    </div>
                  ) : null}
                  <div
                    className={cn(
                      'bg-[#F6D7DA]  flex items-center justify-center',
                      isImageLarge(size)
                        ? 'w-14 h-12 rounded-xl'
                        : 'w-10 h-9 rounded-[10px]',
                    )}
                  >
                    <PlusIcon
                      className={cn(
                        'text-[#D33544]',
                        isImageLarge(size) ? 'w-5 h-5' : 'w-4 h-4',
                      )}
                    />
                  </div>
                  <input
                    id={`photo-${position}`}
                    name={field.name}
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (!file || file.size === 0) return;

                      // Set the current photo slot as uploading
                      setUploadingPhotos((prevState) => {
                        const updatedState = [...prevState];
                        updatedState[position] = true; // Set this position to uploading
                        return updatedState;
                      });
                      formRef.current?.submit();
                    }}
                  />
                </label>
              );
            }
            return null;
          })}
        </fetcher.Form>
      );
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      galleryPhotos,
      onDeletePhoto,
      t,
      fetcher,
      galleryPhotoFields,
      uploadingPhotos,
    ],
  );

  // Photo slots configuration
  const photoSlots = [
    { size: 'large' as const, ref: formRefs.large1, position: 0 },
    { size: 'large' as const, ref: formRefs.large2, position: 1 },
    { size: 'small' as const, ref: formRefs.small1, position: 2 },
    { size: 'small' as const, ref: formRefs.small2, position: 3 },
    { size: 'small' as const, ref: formRefs.small3, position: 4 },
    { size: 'small' as const, ref: formRefs.small4, position: 5 },
  ];

  // Separate slots by size
  const largeSlots = photoSlots.filter((slot) => slot.size === 'large');
  const smallSlots = photoSlots.filter((slot) => slot.size === 'small');

  const largePhotoSlotStyle = {
    className:
      'relative h-64 w-64 rounded-3xl bg-neutral-100 flex items-center justify-center overflow-hidden',
  };
  const smallPhotoSlotsStyle = {
    className:
      'relative w-[133px] h-[133px] rounded-3xl bg-neutral-100 flex items-center justify-center overflow-hidden',
  };

  return (
    <>
      <h2 className="mb-[-20px] text-neutral-900">
        {galleryPhotoFields[0].label}
      </h2>
      <p className="mb-0 text-neutral-400">
        {galleryPhotoFields[0].placeholder}
      </p>

      <div className="flex flex-col sm:flex-col md:flex-col lg:flex-row gap-5 items-start ">
        {/* Large gallery photos (2) */}
        <div className="flex flex-col md:flex-row lg:flex-row gap-3">
          {largeSlots.map((slot) => (
            <div key={`large-${slot.position}`} {...largePhotoSlotStyle}>
              <PhotoUploadForm
                formRef={slot.ref}
                position={slot.position}
                size={slot.size}
              />
            </div>
          ))}
        </div>

        {/* Small gallery photos (4) */}
        <div className="flex flex-col md:flex-row lg:flex-col gap-1 sm:gap-3">
          {/* Top row */}
          <div className="flex gap-3 ">
            {smallSlots.slice(0, 2).map((slot) => (
              <div key={`small-${slot.position}`} {...smallPhotoSlotsStyle}>
                <PhotoUploadForm
                  formRef={slot.ref}
                  position={slot.position}
                  size={slot.size}
                />
              </div>
            ))}
          </div>

          {/* Bottom row */}
          <div className="flex gap-3 ">
            {smallSlots.slice(2, 4).map((slot) => (
              <div key={`small-${slot.position}`} {...smallPhotoSlotsStyle}>
                <PhotoUploadForm
                  formRef={slot.ref}
                  position={slot.position}
                  size={slot.size}
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default GalleryPhotos;
