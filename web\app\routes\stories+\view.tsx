import { useLoaderData } from '@remix-run/react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { StoriesResult } from '#app/components/stories/stories-result';
import { Button } from '#app/components/ui/button';
import { handleLoaderForProfileRoute } from '#app/utils/remix-helpers.server';
import { performStoriesSearch } from '#app/utils/stories.server';

export const loader = handleLoaderForProfileRoute(async ({ profile }) => {
  const searchResults = await performStoriesSearch(profile);
  return { searchResults, profile };
});

export default function ViewStoriesPage() {
  const { t } = useTranslation();
  const { searchResults } = useLoaderData<typeof loader>(); // Use LoaderData type
  const [currentIndex, setCurrentIndex] = useState<number | null>(0); // State for current video index

  const handleNext = () => {
    if (currentIndex !== null) {
      setCurrentIndex((prevIndex) => {
        if (prevIndex === null) return 0;
        return (prevIndex + 1) % searchResults.length;
      });
    }
  };

  const handlePrevious = () => {
    if (currentIndex !== null) {
      setCurrentIndex((prevIndex) => {
        if (prevIndex === null) return 0;
        return (prevIndex - 1 + searchResults.length) % searchResults.length;
      });
    }
  };

  return (
    <div>
      <h1>{t(`View Stories.`)}</h1>
      {searchResults.length !== 0 ? ( // Check if the story exists
        <div className="popup h-96 w-64 object-cover">
          <div className="popup-content">
            {
              searchResults.map((stories, index) => {
                 return (
                   <StoriesResult
                     stories={stories}
                     key={index}
                     selectedIdx={currentIndex}
                     onSelectIdx={setCurrentIndex}
                     currentIdx={index}
                     storiesLength={searchResults.length}
                   />
                 );
                }
              )
            }
            <div className="navigation">
              <Button onClick={handlePrevious} disabled={currentIndex === 0}>
                Previous
              </Button>
              <Button
                onClick={handleNext}
                disabled={currentIndex === searchResults.length - 1}
              >
                Next
              </Button>
            </div>
          </div>
        </div>
      ) : (
        // Fallback if the story is undefined
        <div>No story found.</div>
      )}
    </div>
  );
}
