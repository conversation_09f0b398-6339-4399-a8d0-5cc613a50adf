import { type User, type Profile, type PrivateUser } from '@prisma/client';
import {
  type ActionFunction,
  type LoaderFunction,
  type json as remixJson,
  type redirect as remixRedirect,
} from '@remix-run/node';
import { type z } from 'zod';
import { type ZodValidator } from '#shared/types/validators';

// once the the defineAction and defineLoader functions are stable
// we will make our own callbacks to replicate how Remix handles them
export type CustomRedirect = typeof remixRedirect;
export type CustomJson = typeof remixJson;

export type ActionReturn =
  | Record<string, unknown>
  | ReturnType<typeof remixJson>
  | ReturnType<typeof remixRedirect>
  | Promise<Record<string, unknown>>
  | Promise<ReturnType<typeof remixJson>>
  | Promise<ReturnType<typeof remixRedirect>>;

export type LoaderReturn =
  | Record<string, unknown>
  | ReturnType<typeof remixJson>
  | Promise<Record<string, unknown>>
  | Promise<ReturnType<typeof remixJson>>;

// no json since api routes should be hanldled by Fastify
export type LoaderHandler<
  TLoaderFunction extends LoaderFunction = LoaderFunction,
> = (
  args: Parameters<TLoaderFunction>[0] & {
    redirect: CustomRedirect;
  },
) => ReturnType<TLoaderFunction>;

export type ActionHandler<
  TActionFunction extends ActionFunction = ActionFunction,
> = (
  args: Parameters<TActionFunction>[0] & {
    redirect: CustomRedirect;
    json: CustomJson;
  },
) => ReturnType<TActionFunction>;

export type LoaderHandlerForLoggedInUser<
  TLoaderHandler extends LoaderHandler,
  THandlerReturn,
> = (
  args: Parameters<TLoaderHandler>[0] & {
    user: User;
    privateUser: PrivateUser;
    profile?: Profile; // profile is optional since the user may not have one
    redirect: CustomRedirect;
  },
) => THandlerReturn;

export type LoaderForProfileRoute<
  TLoaderHandler extends LoaderHandler,
  THandlerReturn,
> = (
  args: Parameters<
    LoaderHandlerForLoggedInUser<TLoaderHandler, THandlerReturn>
  >[0] & {
    profile: Profile;
  },
) => THandlerReturn;

// has user and maybe profile
export type ActionHandlerForLoggedInUser<
  TActionHandler extends ActionHandler,
  THandlerReturn,
> = (
  args: Parameters<TActionHandler>[0] & {
    user: User;
    privateUser: PrivateUser;
    redirect: CustomRedirect;
    json: CustomJson;
    successWithMessage: (
      message: string,
      options?: {
        delayedRedirectTo?: string;
      },
    ) => ReturnType<typeof remixJson>;
    profile?: Profile;
  },
) => THandlerReturn;

// has form data
export type ActionFormHandlerForLoggedInUser<
  TActionHandler extends ActionHandler,
  TZodValidator extends ZodValidator,
  THandlerReturn,
> = (
  args: Parameters<
    ActionHandlerForLoggedInUser<TActionHandler, THandlerReturn>
  >[0] & {
    data: z.infer<TZodValidator>;
  },
) => THandlerReturn;

// has user, profile, and form data
export type ActionFormHandlerForProfile<
  TActionHandler extends ActionHandler,
  TZodValidator extends ZodValidator,
  THandlerReturn,
> = (
  args: Parameters<
    ActionFormHandlerForLoggedInUser<
      TActionHandler,
      TZodValidator,
      THandlerReturn
    >
  >[0] & {
    profile: Profile;
  },
) => THandlerReturn;
