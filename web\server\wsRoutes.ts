import { type WebSocket as FastifyWebsocketType } from '@fastify/websocket';
import { type FastifyInstance } from 'fastify';
import  WebSocket  from 'ws';
import { SESSION_KEY } from '#shared/constants/auth.js';
import { type SocketMessagePayload } from '#shared/types/conversations.js';
import { isValidMessagePayload } from '#shared/utils/message.js';
import { prisma } from './utils/db.server';
import { authSessionStorage } from './utils/session.server';

const clients = new Set<FastifyWebsocketType>(); // Track connected clients

function handleSocketDisconnect(
  socket: FastifyWebsocketType,
  message?: string,
) {
  socket.on('close', () => {
    console.log(message || '🔴 Something went wrong...');
    clients.delete(socket);
  });
}

function getWebsocketData(buffer: Buffer): SocketMessagePayload | null {
  try {
    const data = JSON.parse(buffer.toString()) as SocketMessagePayload;
    if (!isValidMessagePayload(data)) {
      return null;
    }

    return data;
  } catch (error) {
    console.error('Failed to parse websocket data:', error);
    return null;
  }
}

export async function wsRouter(fastify: FastifyInstance) {
  fastify.get(
    '/chat/message',
    {
      websocket: true,
    },
    async (socket, req) => {
      const query = req.query as {
        conversationId?: string;
        token?: string;
      };

      const session = await authSessionStorage.getSession(query.token);

      const sessionId = session.get(SESSION_KEY);

      const dbSession = await prisma.session.findUnique({
        where: {
          id: sessionId,
        },
        include: {
          privateUser: {
            include: {
              user: {
                include: {
                  profile: true,
                },
              },
            },
          },
        },
      });

      if (!sessionId || !dbSession) {
        handleSocketDisconnect(
          socket,
          '🔴 You have no permission to access this resource.',
        );
        return;
      }

      if (!query.conversationId) {
        handleSocketDisconnect(socket, '🔴 No conversation/sender ID');
        return;
      }

      const profileId = dbSession.privateUser.user?.profile?.id;

      // Check if conversation exists
      const conversation = await prisma.conversation.findUnique({
        where: {
          id: query.conversationId,
          OR: [
            {
              initiatorProfileId: profileId,
            },
            { receiverProfileId: profileId },
          ],
        },
      });

      if (!conversation) {
        handleSocketDisconnect(socket, '🔴 Conversation does not exist');
        return;
      }

      clients.add(socket);
      console.log('🟢 New client connected');

      socket.on('message', (message: Buffer) => {
        const data = getWebsocketData(message);

        if (!data) {
          handleSocketDisconnect(socket, '🔴 Invalid payload shape.');
          return;
        }

        // Broadcast message to all connected clients
        for (const client of clients) {
          if (client.readyState === WebSocket.OPEN) {
            client.send(JSON.stringify(data));
          }
        }
      });

      handleSocketDisconnect(socket, '🔴 Client disconnected');
    },
  );
}
