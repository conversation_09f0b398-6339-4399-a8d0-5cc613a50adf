name: 🚀 Deploy

on:
  push:
    branches:
      - main
      - dev
  pull_request:
    branches:
      - main
      - dev

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

permissions:
  actions: write
  contents: read

jobs:
  lint:
    name: ⬣ ESLint
    runs-on: ubuntu-22.04
    steps:
      - name: ⬇️ Checkout repo
        uses: actions/checkout@v4

      - name: ⎔ Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: 📥 Download deps
        uses: bahmutov/npm-install@v1

      - name: 🖼 Build icons
        run: npm run build:icons

      - name: 🔬 Lint
        run: npm run lint

      - name: Generate Prisma Client
        run: npm run prisma:generate

      - name: Prepare Prisma Browser Client
        run: |
          # Create the directory structure
          mkdir -p web/node_modules/.prisma/client

          # Create or ensure the file exists in @prisma location
          if [ ! -f "web/node_modules/.prisma/client/index-browser.js" ]; then
            echo "Creating empty index-browser.js file as a workaround"
            echo "// Empty file created as build workaround" > web/node_modules/.prisma/client/index-browser.js
          fi

      - name: Clean and Build
        run: |
          rm -rf web/server-build
          npm run build:web

  typecheck:
    name: ʦ TypeScript
    runs-on: ubuntu-22.04
    steps:
      - name: ⬇️ Checkout repo
        uses: actions/checkout@v4

      - name: ⎔ Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: 📥 Download deps
        uses: bahmutov/npm-install@v1

      - name: 🖼 Build icons
        run: npm run build:icons

      - name: Generate Prisma Client
        run: npm run prisma:generate

      - name: 🔎 Type check
        run: npm run typecheck --if-present

  vitest:
    name: ⚡ Vitest
    runs-on: ubuntu-22.04
    steps:
      - name: ⬇️ Checkout repo
        uses: actions/checkout@v4

      - name: ⎔ Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: 📥 Download deps
        uses: bahmutov/npm-install@v1

      - name: 🛠 Start Services
        run: ./ci-services.sh start

      - name: ⚡ Run vitest
        run: npm run test -- --coverage

      - name: 🛑 Stop Services
        if: always()
        run: ./ci-services.sh stop

  # TODO: bring back playwright tests
  playwright:
    name: 🎭 Playwright
    runs-on: ubuntu-22.04
    services:
      postgres:
        image: postgres:16
        env:
          POSTGRES_DB: filipina-meet-test-db
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:latest
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    timeout-minutes: 60
    steps:
      - name: ⬇️ Checkout repo
        uses: actions/checkout@v4

      - name: 🏄 Copy test env vars
        run: cp web/.env.test web/.env

      - name: ⎔ Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: 📥 Download deps
        uses: bahmutov/npm-install@v1

      - name: 📥 Setup
        run: |
          npm run prisma:generate
          npm run prisma:migrate:dev

      - name: Prepare Prisma Browser Client
        run: |
          # Create the directory structure
          mkdir -p web/node_modules/.prisma/client

          # Create or ensure the file exists in @prisma location
          if [ ! -f "web/node_modules/.prisma/client/index-browser.js" ]; then
            echo "Creating empty index-browser.js file as a workaround"
            echo "// Empty file created as build workaround" > web/node_modules/.prisma/client/index-browser.js
          fi

      - name: Build Web
        run: npm run build:web

      - name: 📥 Install Playwright Browsers
        run: npm run test:e2e:install

      - name: 🎭 Run Playwright tests
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/filipina-meet-db-test
          DISABLE_FASTIFY_CACHE: true
        run: npm run test:e2e:run

      - name: 📊 Upload report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30

  deploy:
    name: 🚀 Deploy
    runs-on: ubuntu-22.04
    needs: [lint, typecheck, vitest]
    if: ${{ github.event_name == 'push' && github.ref == 'refs/heads/main' }}

    steps:
      - name: ⬇️ Checkout repo
        uses: actions/checkout@v4
        with:
          fetch-depth: '50'

      - name: 👀 Read app name
        uses: SebRollen/toml-action@v1.2.0
        id: app_name
        with:
          file: 'fly.toml'
          field: 'app'

      - name: 🎈 Setup Fly
        uses: superfly/flyctl-actions/setup-flyctl@1.5

      - name: 🚀 Deploy Staging
        if: ${{ github.ref == 'refs/heads/dev' }}
        run: flyctl deploy --remote-only --build-arg COMMIT_SHA=${{ github.sha }} --app ${{ steps.app_name.outputs.value }}-staging
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

      - name: 🚀 Deploy Production
        if: ${{ github.ref == 'refs/heads/main' }}
        run: flyctl deploy --remote-only --build-arg COMMIT_SHA=${{ github.sha }} --build-secret SENTRY_AUTH_TOKEN=${{ secrets.SENTRY_AUTH_TOKEN }}
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

  migrate:
    name: 🚀 Migrate
    runs-on: ubuntu-22.04
    needs: [deploy]
    if: ${{ github.event_name == 'push' && github.ref == 'refs/heads/main' }}
    steps:
      - name: 🎈 Setup Fly
        uses: superfly/flyctl-actions/setup-flyctl@1.5

      - name: 🛠️ Run Migrations Production
        if: ${{ github.ref == 'refs/heads/main' }}
        run: |
          # Wait for the app to be reachable
          echo "Waiting for the app to be reachable..."
          until curl -s -f "https://filipina-meet-app.fly.dev" > /dev/null; do
            echo "App is not reachable yet, waiting..."
            sleep 5  # Check every 5 seconds
          done
          echo "App is reachable, proceeding to run migrations."

          # Run migrations
          flyctl ssh console --app filipina-meet-app --command "npm run prisma:migrate:deploy"
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
