import { useState } from 'react';
import { z } from 'zod';
import { VideoUpload } from '#app/components/files/video-upload.js';
import { FormFromConfig } from '#app/components/forms/form-from-config';
import {
  handleFormActionForProfile,
  handleLoaderForProfileRoute,
} from '#app/utils/remix-helpers.server';
import { uploadStoryAndStoreToDB } from '#app/utils/stories.server.js';
import { t } from '#server/utils/api.server';
import { BadRequestError } from '#server/utils/errors.server';
import { type FieldFromConfigType } from '#shared/types/forms';
import { type TranslationFunction } from '#shared/types/translations';
import { createVideoValidator } from '#shared/utils/validators.js';

export function createNewStoryValidator(t: TranslationFunction) {
  return z.object({
    story: createVideoValidator(t),
  });
}

type Schema = z.infer<ReturnType<typeof createNewStoryValidator>>;

export const loader = handleLoaderForProfileRoute(async ({ profile }) => {
  return { profile };
});

export const action = handleFormActionForProfile(
  createNewStoryValidator,
  async ({ profile, data, successWithMessage }) => {
    const { story } = data;

    if (!story) {
      throw new BadRequestError('Video not found');
    }

    // file inputs don't return empty even if the user
    // has not chosen a file but with 0 byte length
    const buffer: ArrayBuffer = await story.arrayBuffer();
    if (buffer.byteLength === 0) {
      throw new BadRequestError('Video not found');
    }

    await uploadStoryAndStoreToDB({
      story,
      profileId: profile.id,
    });

    return successWithMessage(t('Video uploaded successfully.'));
  },
);

export default function NewStoriesPage() {
  const [videoPublicId, setVideoPublicId] = useState<string | null>(null);
  const videoFields: FieldFromConfigType<Schema>[] = [
    {
      name: 'story',
      type: 'hidden',
      defaultValue: videoPublicId ?? '',
    },
  ];
  return (
    <div>
      <VideoUpload
        path={`stories`}
        value={videoPublicId}
        onChange={setVideoPublicId}
        onDelete={() => setVideoPublicId(null)}
      />
      <FormFromConfig
        key={videoPublicId}
        method="POST"
        action="/stories/new"
        encType="multipart/form-data"
        fields={videoFields}
      />
    </div>
  );
}
