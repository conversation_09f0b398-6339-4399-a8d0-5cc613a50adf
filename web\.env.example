DATABASE_URL=postgres://postgres:postgres@localhost:5432/filipina-meet-db
SESSION_SECRET=super-duper-s3cret # you replace
HONEYPOT_SECRET=super-duper-s3cret # ok to leave for development
RESEND_API_KEY=re_blAh_blaHBlaHblahBLAhBlAh # you replace
JWT_SECRET=some-secret-key # ok to leave for development

# this is set to a random value in the Dockerfile
INTERNAL_COMMAND_TOKEN=some-made-up-token #ok to leave

# set this to false to prevent search engines from indexing the website
# default to allow indexing for seo safety
ALLOW_INDEXING=true
HOST=true

PRINT_FASTIFY_ROUTES=true
AWS_ACCESS_KEY_ID=your_key_id # you replace
AWS_SECRET_ACCESS_KEY=your_secret_key # you replace
AWS_ENDPOINT_URL_S3=https://fly.storage.tigris.dev
AWS_REGION=auto
AWS_BUCKET_NAME=filipina-meet # ok to leave
BASE_URL=http://localhost:3000 # ok to leave
GOOGLE_MAPS_API_KEY=your_google_maps_api_key # you replace
CLOUDINARY_API_KEY= # you replace
CLOUDINARY_API_SECRET= # you replace
CLOUDINARY_CLOUD_NAME= # you replace
SENTRY_DSN= # you replace
SENTRY_TRACES_SAMPLE_RATE= # you replace
SENTRY_PROFILES_SAMPLE_RATE= # you replace


CLOUDFLARE_ENDPOINT= # you replace
CLOUDFLARE_ACCESS_KEY_ID= # you replace
CLOUDFLARE_SECRET_ACCESS_KEY= # you replace
CLOUDFLARE_TOKEN= # you replace
CLOUDFLARE_BUCKET_NAME= # you replace
CLOUDFLARE_PUBLIC_URL= # you replace
