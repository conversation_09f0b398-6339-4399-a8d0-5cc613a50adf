import { type Message, type MessageAttachment } from '@prisma/client';
import type React from 'react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { type RootProfile } from '#app/types/serialized-types';
import { cn, formatMessageDate } from '#app/utils/misc.js';
import Typography from '../typography';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { ChatImage } from './chat-image';

type ChatMessageProps = {
  message: Message & { MessageAttachment?: MessageAttachment[] };
  senderProfile: RootProfile;
  receiverProfile: RootProfile;
  isLastMessage?: boolean;
};

const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  senderProfile,
  receiverProfile,
  isLastMessage = false,
}) => {
  const { t } = useTranslation();

  const isSender = message.senderProfileId === senderProfile.id;
  const hasAttachments = (message.MessageAttachment?.length ?? 0) > 0;
  const showLastMessageInfo = isLastMessage && message.readAt;

  const contentStyling = cn(
    'min-h-[28px] w-auto max-w-[75%] sm:max-w-[60%] md:max-w-[50%] px-3 sm:px-4 py-2 break-words',
    isSender
      ? 'bg-[#E0455B] text-white rounded-[10px] rounded-br-[3px]'
      : 'bg-[#E5E5E5] text-black rounded-[10px] rounded-bl-[3px]',
  );

  const formattedDate = useMemo(
    () =>
      message.createdAt ? formatMessageDate(new Date(message.createdAt)) : '',
    [message.createdAt],
  );

  const allSlides = useMemo(
    () =>
      message.MessageAttachment?.map((attachment) => ({
        src: attachment.url || '/placeholder.svg',
        alt: message.content || 'Attachment',
      })) ?? [],
    [message.MessageAttachment, message.content],
  );

  const renderAvatar = (profile: RootProfile) => (
    <Avatar className="size-7 sm:h-8 sm:w-8 flex-shrink-0">
      <AvatarImage src={profile.mainPhoto?.smallUrl} />
      <AvatarFallback>
        {profile.firstName.slice(0, 1).toUpperCase()}
      </AvatarFallback>
    </Avatar>
  );

  const renderAttachments = () => (
    <div className="flex flex-col">
      {message.MessageAttachment?.map((attachment, index) => (
        <div
          key={attachment.id}
          className={cn(
            'transition-transform duration-200 hover:-translate-y-1 hover:z-10 relative',
            index % 2 === 0 ? 'mr-8' : 'ml-8',
            index === 0 ? '' : '-mt-3',
            `z-[${message.MessageAttachment!.length - index}]`,
            index % 2 === 0 ? 'self-start' : 'self-end',
          )}
        >
          <ChatImage slides={allSlides} initialSlide={index} />
        </div>
      ))}
    </div>
  );

  const renderMessageContent = () => (
    <Typography className="text-[15px] leading-[1.4] font-medium">
      {t(message.content)}
    </Typography>
  );

  if (!message.content && !hasAttachments) {
    return null;
  }

  if (isSender) {
    return (
      <div className="w-full flex flex-col items-end mb-3">
        {message.content && (
          <div className="w-full inline-flex justify-end gap-2 items-end mb-1">
            <div className={contentStyling}>{renderMessageContent()}</div>
            {renderAvatar(senderProfile)}
          </div>
        )}

        {hasAttachments && (
          <div className="w-full inline-flex justify-end gap-2 items-end">
            <div
              className={cn(
                'max-w-[75%] sm:max-w-[60%] md:max-w-[50%] overflow-visible',
                message.content ? 'mr-[40px]' : '',
              )}
            >
              {renderAttachments()}
            </div>
            {!message.content && renderAvatar(senderProfile)}
          </div>
        )}

        {showLastMessageInfo && (
          <>
            <div className="flex justify-end w-full pr-10 mt-1">
              <span className="text-[13px] text-gray-500">{t('Seen')}</span>
            </div>
            <div className="flex flex-col items-center w-full mt-2 mb-2">
              <div className="text-[15px] text-gray-500 px-4 py-2 rounded-full bg-gray-100">
                {formattedDate}
              </div>
            </div>
          </>
        )}
      </div>
    );
  }

  return (
    <div className="w-full flex flex-col items-start mb-3">
      {message.content && (
        <div className="flex w-full items-end gap-2 mb-1">
          {renderAvatar(receiverProfile)}
          <div className={contentStyling}>{renderMessageContent()}</div>
        </div>
      )}

      {hasAttachments && (
        <div className="flex w-full items-end gap-2">
          {!message.content && renderAvatar(receiverProfile)}
          <div
            className={cn(
              'max-w-[75%] sm:max-w-[60%] md:max-w-[50%] overflow-visible',
              message.content ? 'ml-[40px]' : '',
            )}
          >
            {renderAttachments()}
          </div>
        </div>
      )}
    </div>
  );
};

export { ChatMessage };
