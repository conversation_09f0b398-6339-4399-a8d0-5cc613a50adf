import { Link } from '@remix-run/react';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { MdOutlineArrowForwardIos } from 'react-icons/md';
import Typography from '#app/components/typography';

interface Props {
  title: string;
  path: string;
  icon: React.ReactNode;
}

const ProfileLink = ({ title, path, icon }: Props) => {
  const { t } = useTranslation();

  return (
    <Link to={path}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {icon}
          <Typography className="!leading-none">{t(title)}</Typography>
        </div>
        <MdOutlineArrowForwardIos />
      </div>
    </Link>
  );
};

export default ProfileLink;
