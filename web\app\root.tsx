import {
  json,
  type HeadersFunction,
  type LinksFunction,
  type LoaderFunctionArgs,
  type MetaFunction,
} from '@remix-run/node';
import {
  Links,
  Meta,
  Scripts,
  ScrollRestoration,
  useLoaderData,
} from '@remix-run/react';
import { withSentry } from '@sentry/remix';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { HoneypotProvider } from 'remix-utils/honeypot/react';
import { GeneralErrorBoundary } from '#app/components/error-boundary';
import RootLayout from '#app/components/layouts/root-layout';
import { EpicProgress } from '#app/components/progress-bar';
import { useToast } from '#app/components/toaster';
import { href as iconsHref } from '#app/components/ui/icon';
import { EpicToaster } from '#app/components/ui/sonner';
import {
  MobileProvider,
  NotificationProvider,
  ProfileProvider,
  UserProvider,
} from '#app/contexts';
// import { useTheme } from '#app/routes/resources+/theme-switch'
//@ts-expect-error: tailwind is weird
import tailwindStyleSheetUrl from '#app/styles/tailwind.css?url';
import { ClientHintCheck, getHints } from '#app/utils/client-hints';
import { getEnv } from '#app/utils/env.server';
import { honeypot } from '#app/utils/honeypot.server';
import { getDomainUrl, isMobileBrowser } from '#app/utils/misc';
import { useNonce } from '#app/utils/nonce-provider';
import { getTheme, type Theme } from '#app/utils/theme.server';
import { getToast } from '#app/utils/toast.server';
import { getBasicInfoFromRequest } from '#server/utils/auth.server';
import { makeTimings } from '#server/utils/timing.server';
import { combineHeaders } from '#shared/utils/misc';
import { type RootProfile, type RootUser } from './types/serialized-types';

const queryClient = new QueryClient();

export const links: LinksFunction = () => {
  return [
    // Preload svg sprite as a resource to avoid render blocking
    { rel: 'icon', href: '/favicon.ico' },
    { rel: 'preload', href: iconsHref, as: 'image' },
    { rel: 'mask-icon', href: '/favicons/mask-icon.svg' },
    {
      rel: 'alternate icon',
      type: 'image/png',
      href: '/favicons/favicon-32x32.png',
    },
    { rel: 'apple-touch-icon', href: '/favicons/apple-touch-icon.png' },
    {
      rel: 'manifest',
      href: '/site.webmanifest',
      crossOrigin: 'use-credentials',
    } as const, // necessary to make typescript happy
    { rel: 'icon', type: 'image/svg+xml', href: '/favicons/favicon.svg' },
    { rel: 'stylesheet', href: tailwindStyleSheetUrl },
  ].filter(Boolean);
};

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: data ? 'Filipina Meet' : 'Error | Filipina Meet' },
    {
      name: 'description',
      content: `Discover meaningful connections with Filipina women. Share your story on FilipinaMeet – where real connections happen. Sign up now to create your own spark!`,
    },
    {
      name: 'keywords',
      content: 'Filipina Dating, Filipina Dating App, Dating App Philippines',
    },
  ];
};

//@ts-expect-error: we don't need to type this yet
export const loader: LoaderFunction = async ({
  request,
}: LoaderFunctionArgs) => {
  const timings = makeTimings('root loader');
  const [{ toast, headers: toastHeaders }, { user, profile }] =
    await Promise.all([
      getToast(request),
      getBasicInfoFromRequest({
        request,
        includeProfile: true,
        extendedProfileData: true,
      }),
    ]);

  const honeyProps = honeypot.getInputProps();
  const isMobile = isMobileBrowser(request.headers.get('user-agent') || '');

  return json(
    {
      requestInfo: {
        hints: getHints(request),
        origin: getDomainUrl(request),
        path: new URL(request.url).pathname,
        userPrefs: {
          theme: getTheme(request),
        },
      },
      ENV: getEnv(),
      toast,
      honeyProps,
      user,
      profile,
      isMobile,
    },
    {
      headers: combineHeaders(
        { 'Server-Timing': timings.toString() },
        toastHeaders,
      ),
    },
  );
};

export const headers: HeadersFunction = ({ loaderHeaders }) => {
  const headers = {
    'Server-Timing': loaderHeaders.get('Server-Timing') ?? '',
  };
  return headers;
};

function Document({
  children,
  nonce,
  theme = 'light',
  env = {},
  allowIndexing = true,
}: {
  children: React.ReactNode;
  nonce: string;
  theme?: Theme;
  env?: Record<string, string>;
  allowIndexing?: boolean;
}) {
  return (
    <html lang="en" className={`${theme} h-full`}>
      <head>
        <ClientHintCheck nonce={nonce} />
        <Meta />
        <meta charSet="utf-8" />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1"
        />
        {allowIndexing ? null : (
          <meta name="robots" content="noindex, nofollow" />
        )}
        <Links />
      </head>
      <body className="bg-background text-foreground">
        {children}
        <script
          nonce={nonce}
          dangerouslySetInnerHTML={{
            __html: `window.ENV = ${JSON.stringify(env)}`,
          }}
        />
        <ScrollRestoration nonce={nonce} />
        <Scripts nonce={nonce} />
      </body>
    </html>
  );
}

function App() {
  const { user, profile, ...data } = useLoaderData<{
    user?: RootUser;
    profile?: RootProfile;
    isMobile: boolean;
    ENV: Record<string, string>;
    toast: Awaited<ReturnType<typeof getToast>>['toast'];
    requestInfo: {
      userPrefs: {
        theme: Theme;
      };
    };
  }>();
  const nonce = useNonce();

  // const theme = useTheme()
  const theme = 'light'; // Persist light mode for now

  const allowIndexing = data.ENV.ALLOW_INDEXING !== 'false';
  useToast(data.toast);

  return (
    <UserProvider initialUser={user}>
      <QueryClientProvider client={queryClient}>
        <ProfileProvider initialProfile={profile}>
          <MobileProvider initialIsMobile={data.isMobile}>
            <NotificationProvider>
              <Document
                nonce={nonce}
                theme={theme}
                allowIndexing={allowIndexing}
                env={data.ENV}
              >
                <RootLayout
                  user={user as RootUser}
                  profile={profile as RootProfile}
                />
                <EpicToaster closeButton position="top-center" theme={theme} />
                <EpicProgress />
              </Document>
            </NotificationProvider>
          </MobileProvider>
        </ProfileProvider>
      </QueryClientProvider>
    </UserProvider>
  );
}

function AppWithProviders() {
  const data = useLoaderData<typeof loader>();
  return (
    <HoneypotProvider {...data.honeyProps}>
      <App />
    </HoneypotProvider>
  );
}

export default withSentry(AppWithProviders);

export function ErrorBoundary() {
  // the nonce doesn't rely on the loader so we can access that
  const nonce = useNonce();

  // NOTE: you cannot use useLoaderData in an ErrorBoundary because the loader
  // likely failed to run so we have to do the best we can.
  // We could probably do better than this (it's possible the loader did run).
  // This would require a change in Remix.

  // Just make sure your root route never errors out and you'll always be able
  // to give the user a better UX.

  return (
    <Document nonce={nonce}>
      <GeneralErrorBoundary />
    </Document>
  );
}
