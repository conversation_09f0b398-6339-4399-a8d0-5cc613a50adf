import { $Enums } from '@prisma/client';
import { type FastifyInstance } from 'fastify';
import { prisma } from '#server/utils/db.server.js';
import { BadRequestError } from '#server/utils/errors.server.js';
import { FacebookUserOptInStatus, SocialProvider } from '#shared/constants/enums.js';
import { FACEBOOK_GRAPH_API_BASE_URL } from '#shared/constants/facebook.js';
import { apiGet } from '#shared/utils/fetch.js';

// see https://developers.facebook.com/docs/messenger-platform/marketing-messages#notification-message-tokens
type FacebookWebhookPayload = {
  object: string;
  entry: Array<{
    id: string;
    time: number;
    messaging: Array<{
      // Our Facebook Page ID
      recipient: { id: string };
      timestamp: number;
      // PSID of user who opted in
      sender: { id: string };
      optin: {
        type: string;
        payload: string;
        // Facebook's identifier for our page and the user's conversation
        notification_messages_token: string;
        token_expiry_timestamp: number;
        user_token_status: string;
        // Whether the user has opted to resume or stop notifications
        notification_messages_status: FacebookUserOptInStatus;
        title: string;
      };
    }>;
  }>;
};

type FacebookAppListResponse = {
  data: {
    // User's provider ID
    id: string;
    app: {
      link: string;
      name: string;
      // App ID from Facebook dev portal
      id: string;
    };
  }[];
  paging: {
    cursors: {
      before: string;
      after: string;
    };
  };
};

export async function facebookWebhookRouter(fastify: FastifyInstance) {
  fastify.post('/facebook', async (req, res) => {
    const payload = req.body as FacebookWebhookPayload;
    console.log('Received a POST request');

    if (payload.entry[0]?.messaging[0]?.optin) {
      const optIn = payload.entry[0].messaging[0].optin;
      const { id: psid} = payload.entry[0].messaging[0].sender;

      if (!process.env.FACEBOOK_CLIENT_ID || !process.env.FACEBOOK_MESSENGER_API_TOKEN) {
        throw new BadRequestError('Missing FACEBOOK_CLIENT_ID or FACEBOOK_MESSENGER_API_TOKEN');
      }

      const params = new URLSearchParams({
        app_id: process.env.FACEBOOK_CLIENT_ID,
        access_token: process.env.FACEBOOK_MESSENGER_API_TOKEN,
      })

      const response: FacebookAppListResponse = await apiGet(
        `${FACEBOOK_GRAPH_API_BASE_URL}/${psid}/ids_for_apps?${params}`
      );

      const appData = response.data.find(({ app }) => {
        return app.id === process.env.FACEBOOK_CLIENT_ID
      })

      if (!appData) {
        throw new BadRequestError('Facebook app not found');
      }

      const { id: providerId } = appData;

      const socialConnection = await prisma.socialConnection.findUnique({
        where: {
          providerName_providerId: {
            providerId,
            providerName: SocialProvider.FACEBOOK,
          }
        }
      });
      
      if (!socialConnection) {
        res.status(200).send({ status: 'EVENT_RECEIVED' });
        return;
      }

      if (!socialConnection.privateUserId)  {
        res.status(200).send({ status: 'EVENT_RECEIVED' });
        return;
      }

      const FacebookStatusToMessageNotificationSettingMap = {
        [FacebookUserOptInStatus.RESUME_NOTIFICATIONS] : $Enums.MessageNotificationSettingEnum.NEW_CONVERSATION,
        [FacebookUserOptInStatus.STOP_NOTIFICATIONS] : $Enums.MessageNotificationSettingEnum.NEVER,
      }

      const { id: messageNotificationSetttingId } = await prisma.messageNotificationSetting.upsert({
        where: {
          privateUserId_communicationChannel: {
            privateUserId: socialConnection.privateUserId,
            communicationChannel: $Enums.ExternalCommunicationChannelEnum.FACEBOOK,
          },
        },
        create: {
          communicationChannel: $Enums.ExternalCommunicationChannelEnum.FACEBOOK,
          setting: FacebookStatusToMessageNotificationSettingMap[optIn.notification_messages_status],
          privateUserId: socialConnection.privateUserId,
        },
        update: {
          setting: FacebookStatusToMessageNotificationSettingMap[optIn.notification_messages_status],
        },
      },)

      await prisma.facebookUserOptIn.upsert({
        where: {
          socialConnectionId: socialConnection.id,
        },
        update: {
          notificationMessageToken: optIn.notification_messages_token,
          messageNotificationSettingId: messageNotificationSetttingId,
        },
        create: {
          socialConnectionId: socialConnection.id,
          notificationMessageToken: optIn.notification_messages_token,
          title: optIn.title,
          messageNotificationSettingId: messageNotificationSetttingId,
        },
      });
      const { title, notification_messages_token, notification_messages_status } = optIn;
      console.log(title, notification_messages_token, notification_messages_status);
    }

    res.status(200).send({ status: 'EVENT_RECEIVED' });
  });

  fastify.get('/facebook', async (req, res) => {
    const verifyToken = process.env.FACEBOOK_VERIFY_TOKEN;
    console.log('Received a GET request');
    type QueryParams = {
      'hub.mode': string;
      'hub.verify_token': string;
      'hub.challenge': string;
    };
    // Parse the query params
    const { 'hub.mode': mode, 'hub.verify_token': token, 'hub.challenge': challenge } = req.query as QueryParams;
      // Check if a token and mode is in the query string of the request
      if (mode && token) {
        // Check the mode and token sent is correct
        if (mode === "subscribe" && token === verifyToken) {
          // Respond with the challenge token from the request
          console.log("WEBHOOK_VERIFIED");
          res.status(200).send(challenge);
        } else {
          // Respond with '403 Forbidden' if verify tokens do not match
          res.status(403).send();
        }
      }
  });
}
