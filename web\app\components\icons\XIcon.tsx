import * as React from 'react';

interface XIconProps extends React.SVGProps<SVGSVGElement> {
  className?: string;
  title?: string;
}

export const XIcon: React.FC<XIconProps> = ({
  className = '',
  title,
  ...props
}) => {
  return (
    <svg
      width="9"
      height="9"
      viewBox="0 0 9 9"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M5.62267 4.5L8.76509 1.35758C8.91423 1.2087 8.99812 1.00668 8.99831 0.795953C8.99849 0.585226 8.91496 0.383055 8.76608 0.233916C8.61721 0.0847776 8.41519 0.00088793 8.20446 0.00070183C7.99373 0.000515729 7.79156 0.0840485 7.64242 0.232924L4.5 3.37534L1.35758 0.232924C1.20844 0.0837853 1.00617 0 0.795252 0C0.584338 0 0.382063 0.0837853 0.232924 0.232924C0.0837853 0.382063 0 0.584338 0 0.795252C0 1.00617 0.0837853 1.20844 0.232924 1.35758L3.37534 4.5L0.232924 7.64242C0.0837853 7.79156 0 7.99383 0 8.20475C0 8.41566 0.0837853 8.61794 0.232924 8.76708C0.382063 8.91621 0.584338 9 0.795252 9C1.00617 9 1.20844 8.91621 1.35758 8.76708L4.5 5.62466L7.64242 8.76708C7.79156 8.91621 7.99383 9 8.20475 9C8.41566 9 8.61794 8.91621 8.76708 8.76708C8.91621 8.61794 9 8.41566 9 8.20475C9 7.99383 8.91621 7.79156 8.76708 7.64242L5.62267 4.5Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default XIcon;
