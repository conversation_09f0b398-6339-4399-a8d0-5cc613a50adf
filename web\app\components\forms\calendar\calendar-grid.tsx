// REACT
import { useMemo, useCallback, memo } from 'react';

// STYLES
import { getDayStyle } from '#app/styles/calendar-picker-styles';

// TYPES
import {
  type DayStyleProps,
  type CalendarDayProps,
} from '#app/types/calendar-types';

// UTILS
import { isDateTooRecent, isHovered } from '#app/utils/calendar-helpers';

interface CalendarGridProps {
  viewDate: Date;
  selectedDate?: Date;
  hoveredDate: Date | null;
  setHoveredDate: (date: Date | null) => void;
  onDateSelect: (date: Date) => void;
}

export function CalendarGrid({
  viewDate,
  selectedDate,
  hoveredDate,
  setHoveredDate,
  onDateSelect,
}: CalendarGridProps) {
  const days = useMemo(() => {
    const monthStart = new Date(viewDate.getFullYear(), viewDate.getMonth(), 1);
    const monthEnd = new Date(
      viewDate.getFullYear(),
      viewDate.getMonth() + 1,
      0,
    );
    const startDate = new Date(monthStart);
    startDate.setDate(startDate.getDate() - startDate.getDay());

    // Calculate total days needed
    const daysInMonth = monthEnd.getDate();
    const firstDayOfMonth = monthStart.getDay();
    const totalDaysToShow = Math.ceil((daysInMonth + firstDayOfMonth) / 7) * 7;

    const calendarDays: JSX.Element[] = [];

    for (let i = 0; i < totalDaysToShow; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);

      // Skip days not in current month in last week
      if (i >= 35 && currentDate.getMonth() !== viewDate.getMonth()) continue;

      calendarDays.push(
        <CalendarDay
          key={i}
          date={new Date(currentDate)}
          viewDate={viewDate}
          selectedDate={selectedDate}
          hoveredDate={hoveredDate}
          onDateSelect={onDateSelect}
          setHoveredDate={setHoveredDate}
        />,
      );
    }

    return calendarDays;
  }, [viewDate, selectedDate, hoveredDate, onDateSelect, setHoveredDate]);

  return <>{days}</>;
}

const isSameDay = (
  date1: Date | undefined,
  date2: Date | undefined,
): boolean => {
  return Boolean(
    date1 &&
      date2 &&
      date1.getDate() === date2.getDate() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getFullYear() === date2.getFullYear(),
  );
};

const CalendarDay = memo(
  ({
    date,
    viewDate,
    selectedDate,
    hoveredDate,
    onDateSelect,
    setHoveredDate,
  }: CalendarDayProps) => {
    const isCurrentMonth = date.getMonth() === viewDate.getMonth();
    const isTooRecent = isDateTooRecent(date);
    const isSelected = selectedDate && isSameDay(date, selectedDate);
    const dateIsHovered = isHovered(date, hoveredDate);

    const handleClick = useCallback(() => {
      if (isCurrentMonth && !isTooRecent) {
        onDateSelect(new Date(date));
      }
    }, [date, isCurrentMonth, isTooRecent, onDateSelect]);

    const handleMouseEnter = useCallback(() => {
      if (isCurrentMonth && !isTooRecent && !isSelected) {
        setHoveredDate(new Date(date));
      }
    }, [date, isCurrentMonth, isTooRecent, isSelected, setHoveredDate]);

    const handleMouseLeave = useCallback(() => {
      return setHoveredDate(null);
    }, [setHoveredDate]);

    const styleProps: DayStyleProps = {
      isCurrentMonth,
      isTooRecent,
      dateIsHovered,
      isSelected: Boolean(isSelected),
    };

    return (
      <div
        onClick={handleClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        style={getDayStyle(styleProps)}
      >
        {date.getDate()}
      </div>
    );
  },
);

CalendarDay.displayName = 'CalendarDay';
