import 'dotenv/config';
import * as fs from 'fs';
import chalk from 'chalk';
import closeWithGrace from 'close-with-grace';
import { config } from 'dotenv';
import sourceMapSupport from 'source-map-support';

config({ path: '../.env' });

sourceMapSupport.install({
  retrieveSourceMap: function (source) {
    // get source file without the `file://` prefix or `?t=...` suffix
    const match = source.match(/^file:\/\/(.*)\?t=[.\d]+$/);
    if (match) {
      return {
        url: source,
        map: fs.readFileSync(`${match[1]}.map`, 'utf8'),
      };
    }
    return null;
  },
});

closeWithGrace(async ({ err }) => {
  if (err) {
    console.error(chalk.red(err));
    console.error(chalk.red(err.stack));
    process.exit(1);
  }
});

if (process.env.MOCKS === 'true') {
  await import('./tests/mocks/index.ts');
}

if (process.env.NODE_ENV === 'production') {
  await import('./server-build/web/server/index.js');
} else {
  await import('./server/index.ts');
}
