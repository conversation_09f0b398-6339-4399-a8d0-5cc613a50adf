import { useMemo } from 'react';
import { cn } from '#app/utils/misc';
import {
  HeightUnitsEnum,
  UnitTypeEnum,
  WeightUnitsEnum,
} from '#shared/constants/enums';

interface UnitToggleProps {
  value?: string;
  name: string;
  inputRef?: React.Ref<HTMLInputElement>;
  type: UnitTypeEnum;
  defaultUnit?: HeightUnitsEnum | WeightUnitsEnum;
  onChange?: (value: string) => void;
}

function useGetOptions(type: UnitTypeEnum) {
  const weightOptions = useMemo(
    () => [
      { value: WeightUnitsEnum.KG, label: 'kg' },
      { value: WeightUnitsEnum.LB, label: 'lb' },
    ],
    [],
  );

  const heightOptions = useMemo(
    () => [
      { value: HeightUnitsEnum.CM, label: 'cm' },
      { value: HeightUnitsEnum.IN, label: 'in' },
    ],
    [],
  );

  if (type === UnitTypeEnum.WEIGHT) return weightOptions;
  if (type === UnitTypeEnum.HEIGHT) return heightOptions;

  return [];
}

export function UnitToggle({
  value,
  name,
  inputRef,
  type,
  onChange,
}: UnitToggleProps) {
  const options = useGetOptions(type);

  return (
    <div className="flex w-fit">
      {options.map((option, index) => {
        return (
          <button
            key={option.value}
            type="button"
            onClick={() => {
              onChange?.(option.value);
            }}
            className={cn(
              'px-3 py-0.5 text-xs font-medium transition-colors',
              value === option.value
                ? 'bg-brand-primary text-white shadow'
                : 'text-gray-500 hover:text-gray-900',
              index === 0 && 'rounded-l-lg border-b border-l border-r border-t',
              index === options.length - 1 &&
                'rounded-r-lg border-b border-r border-t',
              index !== 0 &&
                index !== options.length - 1 &&
                'border-b border-r border-t',
            )}
          >
            {option.label}
          </button>
        );
      })}
      <input
        key={value}
        type="hidden"
        name={name}
        value={value ?? ''}
        ref={inputRef}
      />
    </div>
  );
}
