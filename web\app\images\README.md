# Other

The "other" directory is where we put stuff that doesn't really have a place,
but we don't want in the root of the project. In fact, we want to move as much
stuff here from the root as possible. The only things that should stay in the
root directory are those things that have to stay in the root for most editor
and other tool integrations (like most configuration files sadly). Maybe one day
we can convince tools to adopt a new `.config` directory in the future. Until
then, we've got this `./other` directory to keep things cleaner.
