import { z } from 'zod';
import { FormFromConfig } from '#app/components/forms/form-from-config';

import { handleNewSession } from '#app/utils/remix-auth.server';
import {
  handleFormActionForAdmin,
  handleLoaderForAdmin,
} from '#app/utils/remix-helpers.server';
import { createSessionFromPrivateUser } from '#server/utils/auth.server';
import { prisma } from '#server/utils/db.server';
import { NotFoundError } from '#server/utils/errors.server';
import {
  PROFILE_EDIT_HOME_ROUTE,
  ADMIN_IMPERSONATE_ROUTE,
} from '#shared/constants/routes';
import { type TranslationFunction } from '#shared/types/translations';

function createImpersonateValidator(t: TranslationFunction) {
  return z.object({
    username: z.string({ message: t('Username is required') }),
  });
}

export const loader = handleLoaderForAdmin();

export const action = handleFormActionForAdmin(
  createImpersonateValidator,
  async ({ data, request, redirect }) => {
    const { username } = data;
    const profile = await prisma.profile.findUnique({
      where: { username },
      include: {
        user: {
          include: { privateUser: true },
        },
      },
    });
    if (!profile) {
      throw new NotFoundError('Profile not found');
    }
    const session = await createSessionFromPrivateUser({
      privateUser: profile.user.privateUser,
    });
    return handleNewSession({
      request,
      session,
      redirectTo: PROFILE_EDIT_HOME_ROUTE,
      redirect,
    });
  },
);

function Impersonate() {
  const fields = [
    {
      label: 'Username',
      name: 'username',
      type: 'text',
    },
  ] as const;
  return (
    <FormFromConfig
      fields={fields}
      method="POST"
      action={ADMIN_IMPERSONATE_ROUTE}
      createValidator={createImpersonateValidator}
    />
  );
}

export default Impersonate;
