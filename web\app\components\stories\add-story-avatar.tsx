import ArrowLongRightIcon from '@heroicons/react/24/solid/ArrowLongRightIcon';
import { useFetchers } from '@remix-run/react';
import { type MouseEventHandler, useMemo, useRef, useState } from 'react';
import { FaPlus } from 'react-icons/fa';
import {
  FormFromConfig,
  FormType,
} from '#app/components/forms/form-from-config';
import { Button } from '#app/components/ui/button';
import { createNewStoryValidator } from '#app/routes/stories+/new.js';
import { type RootProfile } from '#app/types/serialized-types.js';
import StoryAvatar from './story-avatar';

export function AddStoryAvatar({ profile }: { profile: RootProfile }) {
  const [showDialog, setShowDialog] = useState(false);
  const [formKey, setFormKey] = useState(0);
  const backgroundDivRef = useRef<HTMLDivElement | null>(null);
  const fetchers = useFetchers();

  const handleAddStoryClick = () => {
    setShowDialog(!showDialog);
  };

  const handleSuccess = () => {
    setFormKey((prev) => prev + 1);
  };

  const handleBackgroundClick: MouseEventHandler<HTMLDivElement> = (event) => {
    if (event.target === backgroundDivRef.current) {
      setShowDialog(false);
    }
  };

  const isFetching = useMemo(() => {
    return (
      fetchers.length > 0 &&
      fetchers.find((fetcher) => {
        return fetcher.formData?.get('story');
      })?.state !== 'idle'
    );
  }, [fetchers]);

  return (
    <>
      <div
        className="cursor-pointer flex flex-col items-center"
        onClick={handleAddStoryClick}
      >
        <div className="relative">
          <StoryAvatar
            url={profile.mainPhoto?.smallUrl}
            userName={profile.username}
          />
          <div className="flex items-center justify-center h-5 w-5 bg-brand-primary rounded-full absolute bottom-0 right-0">
            <FaPlus fontSize={10} className="text-white" />
          </div>
        </div>
        <div>Add Story</div>
      </div>
      {showDialog && (
        <div className="relative z-10">
          <div className="top-[0px] left-[0px] fixed bg-black tablet-min:opacity-75  h-full w-full" />
          <div
            className="top-[0px] left-[0px] fixed tablet-min:backdrop-blur-md h-full w-full flex items-center justify-center"
            onMouseDown={handleBackgroundClick}
            ref={backgroundDivRef}
          >
            <FormFromConfig
              key={formKey}
              encType="multipart/form-data"
              onSuccess={handleSuccess}
              formType={FormType.FETCHER}
              action={'/stories/new'}
              method="POST"
              createValidator={createNewStoryValidator}
              fields={[
                {
                  name: 'story',
                  type: 'video-upload',
                  videoSize: 'very-large-vertical',
                  showLoadingSpinner: isFetching,
                },
              ]}
              renderFields={({ fields }) => {
                return (
                  <div className="relative flex flex-col items-center">
                    {fields.story.node}
                    <div className="m-1 tablet-min:m-3" />
                    <Button
                      type="submit"
                      className="bg-white rounded-xl w-[90%] flex justify-center items-center h-10"
                      disabled={isFetching}
                      variant="outline"
                    >
                      <div className="mr-1">Post Story</div>
                      <ArrowLongRightIcon width={25} />
                    </Button>
                  </div>
                );
              }}
            />
          </div>
        </div>
      )}
    </>
  );
}
