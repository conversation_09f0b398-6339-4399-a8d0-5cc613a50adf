import { useLoaderData } from '@remix-run/react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { FormFromConfig } from '#app/components/forms/form-from-config';
import { ProfileLayout } from '#app/components/profile/profile-layout';
import { convertHeight, convertWeight } from '#app/utils/converters';
import { enrichFieldsWithDefaultValues } from '#app/utils/forms';
import {
  handleFormActionForProfile,
  handleLoaderForProfileRoute,
} from '#app/utils/remix-helpers.server';
import { prisma } from '#server/utils/db.server';
import { getSerializableAppearance } from '#server/utils/profile.server';
import {
  BodyTypeEnum,
  EthnicityEnum,
  EyeColorEnum,
  HairColorEnum,
  HeightUnitsEnum,
  WeightUnitsEnum,
} from '#shared/constants/enums';
import {
  BODY_TYPES,
  ETHNICITIES,
  EYE_COLORS,
  HAIR_COLORS,
} from '#shared/constants/profile';
import { type FieldsFromConfig } from '#shared/types/forms';
import { type TranslationFunction } from '#shared/types/translations';
import {
  createMultiSelectEnumValidator,
  createOptionalEnumValidator,
  createOptionalNumericValidator,
} from '#shared/utils/validators';

const createAppearanceValidator = (t: TranslationFunction) => {
  return z.object({
    height: createOptionalNumericValidator(t('Height must be a number')),
    heightUnit: createOptionalEnumValidator(
      HeightUnitsEnum,
      t('Height unit must be a valid option'),
    ),
    weight: createOptionalNumericValidator(t('Weight must be a number')),
    weightUnit: createOptionalEnumValidator(
      WeightUnitsEnum,
      t('Weight unit must be a valid option'),
    ),
    hairColor: createOptionalEnumValidator(
      HairColorEnum,
      t('Hair Color must be a valid option'),
    ),
    eyeColor: createOptionalEnumValidator(
      EyeColorEnum,
      t('Eye Color must be a valid option'),
    ),
    bodyType: createOptionalEnumValidator(
      BodyTypeEnum,
      t('Body Type must be a valid option'),
    ),
    ethnicity: createMultiSelectEnumValidator(
      EthnicityEnum,
      t('Ethnicity must be a valid option'),
    ).optional(),
  });
};

export const loader = handleLoaderForProfileRoute(async ({ profile }) => {
  const appearance = await prisma.appearance.findUnique({
    where: { profileId: profile.id },
  });
  // add the enums to the loader data
  return {
    HairColorEnum,
    EyeColorEnum,
    BodyTypeEnum,
    EthnicityEnum,
    appearance: getSerializableAppearance(appearance),
  };
});

export const action = handleFormActionForProfile(
  createAppearanceValidator,
  async ({ profile, data, successWithMessage }) => {
    const { heightUnit, weightUnit, height, weight, ...rest } = data;

    const heightValue = height
      ? Math.round(convertHeight(height, heightUnit))
      : undefined;
    const weightValue = weight
      ? Math.round(convertWeight(weight, weightUnit))
      : undefined;

    await prisma.appearance.upsert({
      where: { profileId: profile.id },
      update: {
        ...rest,
        height: heightValue,
        weight: weightValue,
      },
      create: {
        ...rest,
        height: heightValue,
        weight: weightValue,
        profile: { connect: { id: profile.id } },
      },
    });
    return successWithMessage('Appearance updated successfully');
  },
);

export default function EditAppearanceRoute() {
  // enums come from the loader
  const { t } = useTranslation();
  const { appearance } = useLoaderData<typeof loader>();

  const { ethnicities, hairColors, bodyType, eyeColor } = useMemo(() => {
    return {
      ethnicities: ETHNICITIES(t),
      hairColors: HAIR_COLORS(t),
      bodyType: BODY_TYPES(t),
      eyeColor: EYE_COLORS(t),
    };
  }, [t]);

  const fields: FieldsFromConfig<typeof createAppearanceValidator> =
    useMemo(() => {
      let initialFields: FieldsFromConfig<typeof createAppearanceValidator> = [
        {
          name: 'height',
          label: t('Height'),
          type: 'height',
          placeholder: t('Enter your height'),
        },
        {
          name: 'weight',
          label: t('Weight'),
          type: 'weight',
          placeholder: t('Enter your weight'),
        },
        {
          name: 'bodyType',
          label: t('Body Type'),
          type: 'select',
          options: bodyType,
          placeholder: t('Select your body type'),
        },
        {
          name: 'hairColor',
          label: t('Hair Color'),
          type: 'select',
          options: hairColors,
          placeholder: t('Select your current hair color'),
        },
        {
          name: 'eyeColor',
          label: t('Eye Color'),
          type: 'select',
          options: eyeColor,
          placeholder: t('Select your eye color'),
        },
        {
          name: 'ethnicity',
          label: t('Ethnicity'),
          type: 'multi-select',
          options: ethnicities,
        },
      ] as const;

      if (appearance) {
        initialFields = enrichFieldsWithDefaultValues(
          initialFields,
          appearance,
        );
      }

      return initialFields;
    }, [appearance, bodyType, ethnicities, eyeColor, hairColors, t]);

  return (
    <FormFromConfig
      showResetButton
      method="POST"
      action="/profile/edit/appearance"
      fields={fields}
      createValidator={createAppearanceValidator}
      renderFields={({ fields, submitButton, resetButton }) => (
        <ProfileLayout
          renderSideActions={() => (
            <div className="flex gap-2 tablet:flex-col">
              {resetButton}
              {submitButton}
            </div>
          )}
        >
          <div className="mb-6">
            <h2 className="text-2xl font-semibold">{t('Appearance')}</h2>
            <p className="text-xs text-muted-foreground">
              {t('Share your physical characteristics to enhance your profile')}
            </p>
          </div>
          <div className="grid grid-cols-2 gap-x-8 tablet:grid-cols-1 [&>div>label]:pb-3 [&>div>button]:mb-7">
            {fields.height?.node}
            {fields.weight?.node}
            {fields.hairColor?.node}
            {fields.eyeColor?.node}
            {fields.bodyType?.node}
            {fields.ethnicity?.node}
          </div>
        </ProfileLayout>
      )}
    />
  );
}
