import { type FastifyReply, type FastifyRequest } from 'fastify';
import { ZodError } from 'zod';
import { RaisedError, ValidationError } from '#server/utils/errors.server';

// TODO: Add a type for the data for Payload
type Payload = any;
type SendSuccessMessage = (message: string, data?: Payload) => void;
type SendSuccesPayload = (data: Payload) => void;

export function handleFastifyRoute<
  TRequest extends FastifyRequest,
  TReturnType,
>(
  handler: (args: {
    request: TRequest;
    reply: FastifyReply;
    sendSuccessMessage: SendSuccessMessage;
    sendSuccessPayload: SendSuccesPayload;
  }) => Promise<TReturnType>,
): (request: TRequest, reply: FastifyReply) => Promise<TReturnType> {
  return async (request, reply) => {
    // unlike in Remix, we have error handlers in case of errors so we don't need
    // success: true like do in Remix
    const sendSuccessMessage: SendSuccessMessage = (message, data) => {
      return reply.status(200).send({
        message,
        ...data,
      });
    };
    const sendSuccessPayload: SendSuccesPayload = (data) => {
      return reply.status(200).send(data);
    };
    try {
      return await handler({
        request,
        reply,
        sendSuccessMessage,
        sendSuccessPayload,
      });
    } catch (error) {
      if (error instanceof RaisedError) {
        if (error instanceof ValidationError) {
          if (error instanceof ZodError) {
            return await reply.status(400).send({
              error: 'Validation Error',
              errorType: 'ValidationError',
              errorDetail: error.flatten().fieldErrors,
            });
          } else {
            return await reply.status(error.statusCode).send({
              error: error.message,
              errorType: error.name,
              errorDetail: error.errorDetail,
            });
          }
        } else {
          return await reply.status(error.statusCode).send({
            error: error.message,
            errorType: error.name,
          });
        }
      } else {
        console.error('Internal Server Error:', error);
        return await reply.status(500).send({
          error: 'Internal Server Error',
          errorType: 'InternalServerError',
        });
      }
    }
  };
}
