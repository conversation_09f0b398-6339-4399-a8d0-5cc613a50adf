export function getBaseUrlFromRequestUrl(requestUrl: string): string {
  const url = new URL(requestUrl);
  return `${url.protocol}//${url.host}`;
}

export function generateFullUrl(
  baseUrl: string,
  queryParams: Record<string, string | number | undefined>,
) {
  if (baseUrl.startsWith('http')) {
    const url = new URL(baseUrl);
    Object.entries(queryParams).forEach(([key, value]) => {
      if (value === undefined) return;
      url.searchParams.append(key, value.toString());
    });
    return url.toString();
  } else {
    const searchParams = new URLSearchParams();
    Object.entries(queryParams).forEach(([key, value]) => {
      if (value === undefined) return;
      searchParams.append(key, value.toString());
    });
    return `${baseUrl}?${searchParams.toString()}`;
  }
}

/**
 * Combine multiple header objects into one (uses append so headers are not overridden)
 */
export function combineHeaders(
  ...headers: Array<ResponseInit['headers'] | null | undefined>
) {
  const combined = new Headers();
  for (const header of headers) {
    if (!header) continue;
    for (const [key, value] of new Headers(header).entries()) {
      combined.append(key, value);
    }
  }
  return combined;
}

/**
 * Combine multiple response init objects into one (uses combineHeaders)
 */
export function combineResponseInits(
  ...responseInits: Array<ResponseInit | null | undefined>
) {
  let combined: ResponseInit = {};
  for (const responseInit of responseInits) {
    combined = {
      ...responseInit,
      headers: combineHeaders(combined.headers, responseInit?.headers),
    };
  }
  return combined;
}
