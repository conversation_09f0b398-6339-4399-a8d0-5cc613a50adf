import { type SerializedFullProfile } from '#app/types/serialized-types.js';
import { performStoriesSearch } from '#app/utils/stories.server.js';
import { StoryView } from './story-view';

type Props = {
  profile: SerializedFullProfile;
  stories: Awaited<ReturnType<typeof performStoriesSearch>>[number][number][];
};

function ProfilePhotoWidget({ profile, stories }: Props) {
  const hasStory = stories?.length ?? 0 > 0;

  return (
    <>
      <div className="relative">
        <div className="relative w-[320px] h-[350px] rounded-[20px] overflow-hidden">
          <img
            src={
              profile?.mainPhoto?.smallUrl ||
              'https://www.gravatar.com/avatar/94d093eda664addd6e450d7e9881bcad?s=320&d=identicon&r=PG'
            }
            alt="Profile picture"
            className="w-full h-full object-cover object-center absolute inset-0"
          />

          {hasStory ? (
            <StoryView
              firstName={profile.firstName}
              profile={profile.mainPhoto}
              stories={stories ?? []}
            />
          ) : null}
        </div>
      </div>
    </>
  );
}

export { ProfilePhotoWidget };
