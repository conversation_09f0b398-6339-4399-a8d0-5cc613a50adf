import { cn } from '#app/utils/misc.js';

export const storyAvatarSize =
  'lg:min-h-[80px] lg:min-w-[80px] md:min-h-[60px] md:min-w-[60px] min-h-[40px] min-w-[40px]';

interface StoryAvatarProps {
  containerClassName?: string;
  url?: string;
  userName: string;
}

function StoryAvatar({ containerClassName, url, userName }: StoryAvatarProps) {
  return (
    <>
      <div
        className={cn(
          'relative cursor-pointer rounded-full p-[3px] bg-gradient-to-r from-brand-secondary to-brand-primary overflow-hidden',
          containerClassName,
          storyAvatarSize,
        )}
      >
        {url ? (
          <img className="h-full w-full object-cover rounded-full" src={url} alt={`${userName}'s profile picture`} />
        ) : (
          <div
            className={cn(
              'flex justify-center items-center bg-muted rounded-full size-[100px]',
              containerClassName,
              storyAvatarSize,
            )}
          >
            {userName.slice(0, 2).toUpperCase()}
          </div>
        )}
      </div>
    </>
  );
}

export default StoryAvatar;
