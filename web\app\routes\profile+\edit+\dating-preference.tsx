import { useLoaderData } from '@remix-run/react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { FormFromConfig } from '#app/components/forms/form-from-config';
import { ProfileLayout } from '#app/components/profile/profile-layout';

import {
  handleFormActionForProfile,
  handleLoaderForProfileRoute,
} from '#app/utils/remix-helpers.server';
import { prisma } from '#server/utils/db.server';
import {
  BodyTypeEnum,
  NeverSometimesOften,
  RelationshipEnum,
  type GenderEnum,
} from '#shared/constants/enums';
import { AGES } from '#shared/constants/profile';
import { type FieldsFromConfig } from '#shared/types/forms';
import { type TranslationFunction } from '#shared/types/translations';
import {
  getBodyTypeEnumFormOptions,
  getNeverSometimesOftenFormOptions,
  getRelationshipEnumFormOptions,
} from '#shared/utils/form-options';
import {
  createAgeRangeValidator,
  createGenderInterestValidator,
  createMultiSelectEnumValidator,
} from '#shared/utils/validators';

const createdatingPreferenceValidator = (t: TranslationFunction) => {
  return z.object({
    ageRange: createAgeRangeValidator(t),
    genderInterest: createGenderInterestValidator(t),
    relationshipPreference: createMultiSelectEnumValidator(
      RelationshipEnum,
      t('Invalid option for relationship preference'),
    ),
    lookingFor: z.string({ message: t('Looking For must be a string') }),
    drinking: createMultiSelectEnumValidator(
      NeverSometimesOften,
      t('Invalid option for drinks'),
    ),
    smoking: createMultiSelectEnumValidator(
      NeverSometimesOften,
      t('Invalid option for smokes'),
    ),
    bodyTypes: createMultiSelectEnumValidator(
      BodyTypeEnum,
      t('Invalid option for body types'),
    ),
  });
};

export const loader = handleLoaderForProfileRoute(async ({ profile }) => {
  const datingPreference = await prisma.datingPreference.findUnique({
    where: { profileId: profile.id },
  });
  return {
    profile,
    datingPreference,
  };
});

export const action = handleFormActionForProfile(
  createdatingPreferenceValidator,
  async ({ successWithMessage, data, profile }) => {
    // gender interest is on the main profile row
    await prisma.profile.update({
      where: { id: profile.id },
      data: {
        genderInterest: data.genderInterest,
      },
    });

    const commonData = {
      drinking: data.drinking,
      smoking: data.smoking,
      bodyTypes: data.bodyTypes,
      minAge: data.ageRange.minAge,
      maxAge: data.ageRange.maxAge,
      relationshipPreference: data.relationshipPreference,
    };
    await prisma.datingPreference.upsert({
      where: { profileId: profile.id },

      update: commonData,
      create: {
        profileId: profile.id,
        ...commonData,
      },
    });

    return successWithMessage('Profile updated successfully');
  },
);

export default function DatingPreference() {
  const { t } = useTranslation();
  const { profile, datingPreference } = useLoaderData<typeof loader>();

  const fields: FieldsFromConfig<typeof createdatingPreferenceValidator> = [
    {
      name: 'genderInterest',
      type: 'gender-multi-select',
      label: t('Gender Interest'),
      // this sits on the profile row instead of dating preference row
      defaultValue: profile.genderInterest as GenderEnum[],
    },
    {
      name: 'relationshipPreference',
      label: t('Relationship Preference'),
      type: 'multi-select',
      options: getRelationshipEnumFormOptions(t),
      defaultValue: datingPreference?.relationshipPreference || [],
    },
    {
      name: 'ageRange',
      label: t('Age Range'),
      type: 'age-range-slider',
      // if you're interested in dudes, you probably want to see older dudes lol
      showHighUpperAge: profile.genderInterest.includes('MALE'),
      defaultValue: [
        datingPreference?.minAge ?? AGES.MIN_AGE,
        datingPreference?.maxAge ?? AGES.MAX_AGE,
      ],
    },
    {
      name: 'lookingFor',
      label: t('Looking For'),
      type: 'textarea',
    },
    {
      name: 'drinking',
      label: t('Drinking'),
      type: 'multi-select',
      options: getNeverSometimesOftenFormOptions(t),
      defaultValue: datingPreference?.drinking || [],
    },
    {
      name: 'smoking',
      label: t('Smokes'),
      type: 'multi-select',
      options: getNeverSometimesOftenFormOptions(t),
      defaultValue: datingPreference?.smoking || [],
    },
    {
      name: 'bodyTypes',
      label: t('Body Types'),
      type: 'multi-select',
      options: getBodyTypeEnumFormOptions(t),
      defaultValue: datingPreference?.bodyTypes || [],
    },
  ];
  return (
    <ProfileLayout>
      <div className="form-container">
        <div className="mb-6">
          <h2 className="text-2xl font-semibold">{t('Dating Preferences')}</h2>
          <p className="text-xs text-muted-foreground">
            Set your dating preferences to find the connection that's right for
            you
          </p>
        </div>
        <FormFromConfig
          method="POST"
          action="/profile/edit/dating-preference"
          fields={fields}
          createValidator={createdatingPreferenceValidator}
        />
      </div>
    </ProfileLayout>
  );
}
