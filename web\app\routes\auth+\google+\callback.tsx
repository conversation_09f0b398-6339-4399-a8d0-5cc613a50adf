import {
  getGoogleAccessToken,
  getGoogleProfileData,
} from '#app/utils/google.server';
import { loaderForSocialAuthCallback } from '#app/utils/social.server';
import { SocialProvider } from '#shared/constants/enums';

export const loader = loaderForSocialAuthCallback(
  SocialProvider.GOOGLE,
  async ({ request, code }) => {
    const clientId = process.env.GOOGLE_CLIENT_ID;
    const clientSecret = process.env.GOOGLE_CLIENT_SECRET;
    if (!clientId || !clientSecret) {
      throw new Error('Google client ID or client secret is not set');
    }

    const response = await getGoogleAccessToken({
      code,
      requestUrl: request.url,
    });
    const { access_token, expires_in, scope, refresh_token } = response;

    // get the profile data
    const profileData = await getGoogleProfileData({
      accessToken: access_token,
    });
    const { given_name, email, id: providerId } = profileData;

    return {
      email,
      providerId,
      firstName: given_name,
      oAuthTokenData: {
        accessToken: access_token,
        expiryDate: new Date(Date.now() + expires_in * 1000),
        scope,
        refreshToken: refresh_token,
      },
    };
  },
);
