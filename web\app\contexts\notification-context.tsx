import { useQuery, useQueryClient } from '@tanstack/react-query';
import React, {
  useMemo,
  createContext,
  useContext,
  useCallback,
  useEffect,
  useState,
} from 'react';
import { apiGet, apiPost } from '#app/utils/fetch.client';
import { NOTIFICATION_TYPE_MAP } from '#shared/constants/profile';
import { API_PROFILE_NOTIFCATIONS_UNREAD_ROUTE } from '#shared/constants/routes';
import { type NotificationType } from '#shared/types/profile';
import { useProfile } from './profile-context';

// Constants for refreshing data intervals
const INITIAL_STALE_TIME = 5 * 60 * 1000; // 5 minutes in milliseconds
const MAX_STALE_TIME = 4 * 60 * 60 * 1000; // 4 hours in milliseconds
const TWENTY_MINUTES = 20 * 60 * 1000; // 20 minutes in milliseconds

interface NotificationData {
  messageNotificationsCount: number;
  profileLikeNotificationsCount: number;
  profileViewNotificationsCount: number;
}

const notificationTypeToPropertyName: Record<
  NotificationType,
  keyof NotificationData
> = {
  [NOTIFICATION_TYPE_MAP.MESSAGE]: 'messageNotificationsCount',
  [NOTIFICATION_TYPE_MAP.PROFILE_LIKE]: 'profileLikeNotificationsCount',
  [NOTIFICATION_TYPE_MAP.PROFILE_VIEW]: 'profileViewNotificationsCount',
};

interface NotificationContextType {
  notificationData: NotificationData;
  isLoading: boolean;
  clearNotificationsOfType: (type: NotificationType) => void;
  refetchNotifications: () => void;
  totalUnreadNotifications: number;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined,
);

// Function to calculate exponential backoff for staleTime
const calculateNextRefreshInterval = (currentStaleTime: number): number => {
  const nextStaleTime = currentStaleTime * 2; // Double the staleTime
  return Math.min(nextStaleTime, MAX_STALE_TIME); // Ensure it doesn't exceed the maximum limit
};

export const NotificationProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const queryClient = useQueryClient();
  const { profile } = useProfile();
  const [refetchInterval, setRefreshInterval] = useState(INITIAL_STALE_TIME);
  const [mountedTime] = useState(Date.now());
  const hasProfile = !!profile;

  const queryKey = useMemo(
    () => [API_PROFILE_NOTIFCATIONS_UNREAD_ROUTE, profile?.id || 'unknown'],
    [profile?.id],
  );

  // Fetch notifications using useQuery, enabled only if profile exists
  const { data, isLoading, isFetching, refetch, isSuccess } =
    useQuery<NotificationData>({
      queryKey,
      queryFn: () => {
        const currentTime = Date.now();
        const timeSinceMounted = currentTime - mountedTime;
        const queryParams =
          timeSinceMounted < TWENTY_MINUTES ? { user_online: 'true' } : {};
        return apiGet<NotificationData>(API_PROFILE_NOTIFCATIONS_UNREAD_ROUTE, {
          queryParams,
        });
      },
      enabled: hasProfile,
      refetchInterval,
      // refetchIntervalInBackground: true,
    });

  const notificationData = useMemo(() => {
    if (!data) {
      return {
        messageNotificationsCount: 0,
        profileLikeNotificationsCount: 0,
        profileViewNotificationsCount: 0,
      };
    }
    return data;
  }, [data]);

  const totalUnreadNotifications = useMemo(() => {
    return (
      notificationData.messageNotificationsCount +
      notificationData.profileLikeNotificationsCount +
      notificationData.profileViewNotificationsCount
    );
  }, [notificationData]);

  // Handle exponential backoff after successful data fetching
  useEffect(() => {
    if (!isFetching) {
      return;
    }
    if (isSuccess) {
      setRefreshInterval(calculateNextRefreshInterval); // Increase staleTime on success
    } else {
      setRefreshInterval(INITIAL_STALE_TIME); // Reset staleTime on failure
    }
  }, [isFetching, isSuccess]);

  const clearNotificationsOfType = useCallback(
    async (type: NotificationType) => {
      if (!hasProfile) return;

      const propertyName = notificationTypeToPropertyName[type];
      const notificationCount = notificationData[propertyName];
      if (notificationCount === 0) return;

      try {
        await apiPost(
          `/api/profile/notifications/types/${type}/mark-as-read`,
          {},
        );
        queryClient.setQueryData(
          queryKey,
          (oldData: NotificationData | undefined) => {
            console.log('old data:', oldData);
            const newData = {
              ...oldData,
              [propertyName]: 0,
            };
            console.log('Setting new data:', newData);
            return newData;
          },
        );
      } catch (error) {
        console.error(`Failed to clear ${type} notifications:`, error);
      }
    },
    [hasProfile, queryKey, queryClient, notificationData],
  );

  return (
    <NotificationContext.Provider
      value={{
        notificationData,
        isLoading,
        clearNotificationsOfType,
        refetchNotifications: refetch,
        totalUnreadNotifications,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotificationContext = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error(
      'useNotificationContext must be used within a NotificationProvider',
    );
  }
  return context;
};

export const useClearNotificationOfType = (type: NotificationType) => {
  const { clearNotificationsOfType } = useNotificationContext();
  useEffect(() => {
    clearNotificationsOfType(type);
  }, [clearNotificationsOfType, type]);
};
