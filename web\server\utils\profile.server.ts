import {
  PhotoType,
  Prisma,
  VerificationStatus,
  VideoType,
  type $Enums,
  type Appearance,
  type DatingPreference,
  type Lifestyle,
  type Profile,
  type ProfileLike,
  type ParentPhoto,
} from '@prisma/client';
import {
  cacheGet,
  cacheGetKeys,
  cacheGetMany,
  cacheSet,
} from '#server/utils/cache.server';
import { prisma } from '#server/utils/db.server';
import { ProfileNotFoundError } from '#server/utils/errors.server';
import {
  attachImageUrlsToPhoto,
  attachVideoUrlFromVideo,
} from '#server/utils/media.server';
import {
  ONLINE_STATUS_MAP,
  ONLINE_TIME_THRESHOLD_SECONDS,
} from '#shared/constants/profile';
import {
  type OnlineStatusType,
  type OnlineStatusWithTime,
  type ProfileWithMedia,
} from '#shared/types/profile';
import { calculateAge } from '#shared/utils/date';

export type ProfileProgress = {
  progress: number;
  completedSteps: string[];
  uncompletedSteps: string[];
  isPartial: Record<string, boolean>;
};

export const EXTRA_PROFILE_CARD_INCLUDES = {
  datingPreference: true,
  location: true,
  bio: true,
  appearance: true,
  lifestyle: true,
  user: {
    select: {
      verificationStatus: true,
    },
  },
  parentPhotos: {
    include: {
      photoVariants: true,
    },
  },
};

export function getCacheKeyForProfileOnline(profileId: string) {
  return `profile/${profileId}/last-online`;
}

const PROFILE_VIEW_INCLUDES = {
  allVideos: {
    where: {
      videoType: {
        not: VideoType.STORY,
      },
    },
  },
  location: true,
  appearance: true,
  lifestyle: true,
  bio: true,
  datingPreference: true,
  user: {
    select: {
      verificationStatus: true,
    },
  },
  parentPhotos: {
    include: {
      photoVariants: true,
    },
  },
} as const;

const PHOTO_TYPES_FOR_PROFILE_CARD: $Enums.PhotoType[] = [
  PhotoType.MAIN,
  PhotoType.VERIFICATION,
];
const VIDEO_TYPES_FOR_PROFILE_CARD: $Enums.VideoType[] = [VideoType.MAIN];

// this is the include query we need to render a profile card
// not we do need other fields we from other models we will enrich with later
export const PROFILE_CARD_INCLUDES = {
  // we can limit the fields we want to return later
  datingPreference: true,
  location: true,
  bio: true,
  appearance: true,
  lifestyle: true,
  user: {
    select: {
      verificationStatus: true,
    },
  },
  parentPhotos: {
    where: {
      photoType: {
        in: PHOTO_TYPES_FOR_PROFILE_CARD,
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  },
  allVideos: {
    where: {
      videoType: {
        in: VIDEO_TYPES_FOR_PROFILE_CARD,
      },
    },
  },
};

export async function getProfileWithExtrasByUsername(username: string) {
  const profile = await prisma.profile.findFirst({
    where: {
      username: {
        equals: username,
        mode: 'insensitive' as const,
      },
    },
    include: {
      ...PROFILE_VIEW_INCLUDES,
    },
  });

  if (!profile) {
    throw new ProfileNotFoundError('Profile not found');
  }
  return profile;
}

export async function getProfileWithExtrasByUserId(userId: string) {
  return await prisma.profile.findUniqueOrThrow({
    where: { userId },
    include: PROFILE_VIEW_INCLUDES,
  });
}

export async function getProfileWithExtrasByProfileId(profileId: string) {
  return await prisma.profile.findUniqueOrThrow({
    where: { id: profileId },
    include: PROFILE_VIEW_INCLUDES,
  });
}

export async function enrichProfileForProfileCard<
  TProfile extends ProfileWithMedia,
>({ allPhotos, allVideos, user, ...profile }: TProfile) {
  // check if we have a verified photo
  const hasVerifiedPhoto = allPhotos.some(
    (photo) =>
      photo.photoType === PhotoType.VERIFICATION &&
      photo.verificationStatus === VerificationStatus.APPROVED,
  );
  // get the pre-signed url
  let mainPhoto = allPhotos.find((photo) => photo.photoType === PhotoType.MAIN);
  const enrichedMainPhoto = mainPhoto
    ? attachImageUrlsToPhoto(mainPhoto)
    : undefined;
  // check if the email is verified but ignore other user fieldds
  const emailIsVerified =
    user.verificationStatus === VerificationStatus.APPROVED;
  return {
    ...profile,
    allPhotos,
    allVideos,
    mainPhoto: enrichedMainPhoto,
    hasVerifiedPhoto,
    emailIsVerified,
  };
}

export async function enrichProfileForFullProfileView(
  profile: Awaited<ReturnType<typeof getProfileWithExtrasByUsername>>,
  viewerProfileId: string,
) {
  const age = await getUserAge(profile.username);
  const { allPhotos, allVideos, ...baseEnrichedProfile } =
    await enrichProfileForProfileCard({
      ...profile,
      allPhotos: profile.parentPhotos,
      appearance: getSerializableAppearance(profile.appearance),
      age,
    });
  let galleryPhotos = allPhotos
    .filter((photo) => photo.photoType === PhotoType.GALLERY)
    .map(attachImageUrlsToPhoto);
  let galleryVideos = allVideos
    .filter((video) => video.videoType === VideoType.GALLERY)
    .map(attachVideoUrlFromVideo);
  const mainPhoto = allPhotos.find(
    (photo) => photo.photoType === PhotoType.MAIN,
  );
  const enrichedMainPhoto = mainPhoto
    ? attachImageUrlsToPhoto(mainPhoto)
    : undefined;
  if (enrichedMainPhoto) {
    // Insert at index 0
    galleryPhotos.unshift(enrichedMainPhoto);
  }
  const [
    enrichedGalleryPhotos,
    enrichedGalleryVideos,
    onlineStatus,
    blockStatus,
    profileLike,
  ] = await Promise.all([
    Promise.all(galleryPhotos),
    Promise.all(galleryVideos),
    getOnlineStatusForProfileId(profile.id),
    getBlockStatus(viewerProfileId, profile.id),
    getLikeStatus(viewerProfileId, profile.id),
  ]);
  return {
    ...baseEnrichedProfile,
    galleryPhotos: enrichedGalleryPhotos,
    galleryVideos: enrichedGalleryVideos,
    onlineStatus,
    signedMainPhoto: enrichedMainPhoto,
    likesThisProfile: !!profileLike,
    isBlocked: !!blockStatus,
  };
}

export async function getProfileCardInfoFromProfileIds({
  profileIds,
  viewerProfileId,
}: {
  profileIds: string[];
  viewerProfileId: string;
}) {
  const [_profiles, ages, profileLikesOfOthers, onlineStatuses] =
    await Promise.all([
      prisma.profile.findMany({
        where: {
          id: {
            in: profileIds,
          },
        },
        // make sure to keep in sync with performSearch
        include: {
          ...EXTRA_PROFILE_CARD_INCLUDES,
          allVideos: {
            where: {
              videoType: VideoType.MAIN,
            },
          },
          profileLikesOfMe: true,
        },
      }),
      getUserAges(profileIds),
      prisma.profileLike.findMany({
        where: {
          likerProfileId: viewerProfileId,
          likedProfileId: {
            in: profileIds,
          },
        },
        select: {
          likedProfileId: true,
        },
      }),
      getOnlineStatusForProfileIds(profileIds),
    ]);
  // now add in the likes and online statuses
  const profiles = _profiles.map((profile) => {
    const likesThisProfile = profileLikesOfOthers.some(
      (like) => like.likedProfileId === profile.id,
    );
    const onlineStatusWithTime = onlineStatuses[profile.id] ?? {
      onlineStatus: ONLINE_STATUS_MAP.OFFLINE,
      lastOnlineTime: null,
    };
    return {
      likesThisProfile,
      onlineStatus: onlineStatusWithTime,
      ...profile,
      appearance: getSerializableAppearance(profile.appearance),
    };
  });
  return Promise.all(
    profiles.map((profile) =>
      enrichProfileForProfileCard({
        ...profile,
        allPhotos: profile.parentPhotos,
        age: ages[profile.id] ?? null,
      }),
    ),
  );
}

export async function markProfileAsOnline(profileId: string) {
  const cacheKey = getCacheKeyForProfileOnline(profileId);
  await cacheSet(
    cacheKey,
    new Date().toISOString(),
    ONLINE_TIME_THRESHOLD_SECONDS,
  );
}

export async function markProfileAsOffline(profileId: string) {
  const cacheKey = getCacheKeyForProfileOnline(profileId);
  // Instead of deleting, set the current timestamp so we can show "x minutes ago"
  await cacheSet(cacheKey, new Date().toISOString(), 24 * 60 * 60); // Keep for 24 hours
}

function getOnlineStatus(lastOnline?: string | null): OnlineStatusWithTime {
  if (!lastOnline) {
    return {
      onlineStatus: ONLINE_STATUS_MAP.OFFLINE,
      lastOnlineTime: null,
    };
  }

  const lastOnlineDate = new Date(lastOnline);
  const currentTime = new Date();
  const timeDifferenceInSeconds =
    (currentTime.getTime() - lastOnlineDate.getTime()) / 1000;

  const onlineStatus =
    timeDifferenceInSeconds <= ONLINE_TIME_THRESHOLD_SECONDS
      ? ONLINE_STATUS_MAP.ONLINE
      : ONLINE_STATUS_MAP.OFFLINE;

  return {
    onlineStatus,
    lastOnlineTime: lastOnlineDate,
  };
}

export async function getOnlineStatusForProfileId(
  profileId: string,
): Promise<OnlineStatusWithTime> {
  const cacheKey = getCacheKeyForProfileOnline(profileId);
  const lastOnline = await cacheGet(cacheKey);
  return getOnlineStatus(lastOnline);
}

export async function getOnlineStatusForProfileIds(
  profileIds: string[],
): Promise<Record<string, OnlineStatusWithTime>> {
  // if no profile ids then return empty object
  if (profileIds.length === 0) {
    return {};
  }
  const cacheKeys = profileIds.map(getCacheKeyForProfileOnline);
  const lastOnlineTimes = await cacheGetMany(cacheKeys);

  const statuses: Record<string, OnlineStatusWithTime> = {};
  for (let i = 0; i < profileIds.length; i++) {
    const profileId = profileIds[i];
    if (profileId) {
      statuses[profileId] = getOnlineStatus(lastOnlineTimes[i]);
    }
  }
  return statuses;
}

// Helper function for backward compatibility where only the status is needed
export function getOnlineStatusOnly(
  statusWithTime: OnlineStatusWithTime,
): OnlineStatusType {
  return statusWithTime.onlineStatus;
}

// create the profile view and handle notifications
export async function handleProfileView({
  viewedUserId,
  viewedProfileId,
  viewerProfileId,
}: {
  viewedUserId: string;
  viewedProfileId: string;
  viewerProfileId: string;
}) {
  if (!viewerProfileId) {
    console.log('Guest view detected, no profile view logging required.');
    return;
  }
  let handleNotification = false;
  let profileView = await prisma.profileView.findUnique({
    where: {
      viewerProfileId_viewedProfileId: {
        viewerProfileId,
        viewedProfileId: viewedProfileId,
      },
    },
  });
  const currentTime = new Date();
  if (!profileView) {
    handleNotification = true;
    profileView = await prisma.profileView.create({
      data: {
        viewerProfileId,
        viewedProfileId: viewedProfileId,
        lastViewedTime: currentTime,
      },
    });
  } else {
    // only update the view time if it has been at least an hour
    const oneHourAgo = new Date(currentTime.getTime() - 60 * 60 * 1000);
    if (profileView.lastViewedTime < oneHourAgo) {
      profileView = await prisma.profileView.update({
        where: { id: profileView.id },
        data: { lastViewedTime: new Date() },
      });
      handleNotification = true;
    }
  }
  // create a notification if it is a new profile view or it has been an hour
  if (handleNotification) {
    await prisma.profileViewNotification.upsert({
      where: {
        profileViewId_userId: {
          profileViewId: profileView.id,
          userId: viewedUserId,
        },
      },
      update: {
        readAt: null,
      },
      create: {
        profileView: { connect: { id: profileView.id } },
        user: { connect: { id: viewedUserId } },
      },
    });
  }
}

export async function createProfileLikeNotification({
  profileLike,
  likedProfile,
}: {
  profileLike: ProfileLike;
  likedProfile: Profile;
}) {
  return await prisma.profileLikeNotification.create({
    data: {
      profileLike: { connect: { id: profileLike.id } },
      user: { connect: { id: likedProfile.userId } },
    },
  });
}

export async function getUserAge(username: string) {
  const profile = await prisma.profile.findUnique({
    where: { username },
    include: {
      user: { include: { privateUser: { select: { dateOfBirth: true } } } },
    },
  });
  if (!profile) {
    return null;
  }
  return profile.user.privateUser.dateOfBirth
    ? calculateAge(profile.user.privateUser.dateOfBirth)
    : null;
}

export async function getUserAges(profileIds: string[]) {
  const profiles = await prisma.profile.findMany({
    where: { id: { in: profileIds } },
    include: {
      user: { include: { privateUser: { select: { dateOfBirth: true } } } },
    },
  });
  return profiles.reduce(
    (acc, profile) => {
      acc[profile.id] = profile.user.privateUser.dateOfBirth
        ? calculateAge(profile.user.privateUser.dateOfBirth)
        : null;
      return acc;
    },
    {} as Record<string, number | null>,
  );
}

export const getSerializableAppearance = (appearance: Appearance | null) => {
  if (!appearance) return null;

  const { height, weight, ...rest } = appearance;
  return {
    ...rest,
    height: height?.toNumber() ?? null,
    weight: weight?.toNumber() ?? null,
  };
};

// TODO: ⚠️ This `keys` operation is **not scalable** for millions of users.
// Redis `keys('pattern*')` is **blocking** and **inefficient** for large datasets.
// Consider switching to:
// - Redis SCAN (non-blocking cursor-based iteration)
// - Using a **Sorted Set (ZSET)** for online users with timestamps
// - Storing **online user IDs in a Redis Set** (and updating on user activity)
export const getAllOnlineUsers = async (): Promise<string[]> => {
  const now = Date.now();
  const keys = (await cacheGetKeys('profile/*/last-online')) ?? [];

  if (keys.length === 0) return [];

  const timestamps = (await cacheGetMany(keys)).filter(
    (timestamp) => !!timestamp,
  );
  return keys
    .map((key, i) => {
      const userId = key.split('/')[1];
      const lastSeen = new Date(timestamps[i]!).getTime();
      return now - lastSeen <= ONLINE_TIME_THRESHOLD_SECONDS * 1000
        ? userId
        : null;
    })
    .filter((id): id is string => id !== null);
};

export const getProfileProgress = ({
  mainPhoto,
  galleryPhotos,
  appearance,
  lifestyle,
  datingPreference,
}: {
  mainPhoto?: ParentPhoto | null;
  galleryPhotos?: ParentPhoto[];
  appearance?: Appearance | null;
  lifestyle?: Lifestyle | null;
  datingPreference?: DatingPreference | null;
}): ProfileProgress => {
  const excludedFields = ['id', 'createdAt', 'updatedAt', 'profileId'];
  const appearanceFields = Object.values(
    Prisma.AppearanceScalarFieldEnum,
  ).filter((field) => !excludedFields.includes(field));
  const lifestyleFields = Object.values(Prisma.LifestyleScalarFieldEnum).filter(
    (field) => !excludedFields.includes(field),
  );

  const calculateSectionProgress = <T extends Record<string, unknown>>(
    section: T | null | undefined,
    fields: string[],
    weight: number,
  ) => {
    if (!section) return { progress: 0, isPartial: false };
    const completedFields = fields.filter((field) => {
      const value = section[field];
      if (Array.isArray(value)) {
        return value.length > 0;
      }
      return value !== null && value !== undefined;
    }).length;
    const progress = (completedFields / fields.length) * weight;
    return {
      progress,
      isPartial: completedFields > 0 && completedFields < fields.length,
    };
  };

  const appearanceProgress = calculateSectionProgress(
    appearance,
    appearanceFields,
    20,
  );
  const lifestyleProgress = calculateSectionProgress(
    lifestyle,
    lifestyleFields,
    20,
  );

  const steps = {
    mainPhoto: { weight: 20, completed: !!mainPhoto, isPartial: false },
    galleryPhotos: {
      weight: 20,
      completed: (galleryPhotos?.length ?? 0) > 0,
      isPartial: false,
    },
    appearance: {
      weight: 20,
      completed: appearanceProgress.progress === 20,
      isPartial: appearanceProgress.isPartial,
    },
    lifestyle: {
      weight: 20,
      completed: lifestyleProgress.progress === 20,
      isPartial: lifestyleProgress.isPartial,
    },
    datingPreference: {
      weight: 20,
      completed: !!datingPreference,
      isPartial: false,
    },
  };

  let progress = 0;
  const completedSteps: string[] = [];
  const uncompletedSteps: string[] = [];
  const isPartial: Record<string, boolean> = {};

  const progressMap = {
    appearance: appearanceProgress,
    lifestyle: lifestyleProgress,
  };

  for (const [stepName, stepDetails] of Object.entries(steps)) {
    if (stepName === 'appearance' || stepName === 'lifestyle') {
      const partialProgress = progressMap[stepName as keyof typeof progressMap];
      progress += partialProgress.progress;
      isPartial[stepName] = stepDetails.isPartial;
    } else {
      if (stepDetails.completed) {
        progress += stepDetails.weight;
      }
    }

    if (stepDetails.completed) {
      completedSteps.push(stepName);
    } else {
      uncompletedSteps.push(stepName);
    }
  }

  return {
    progress: Math.round(progress),
    completedSteps,
    uncompletedSteps,
    isPartial,
  };
};

export { attachImageUrlsToPhoto };

export async function getBlockStatus(
  viewerProfileId: string,
  viewedProfileId: string,
) {
  return await prisma.profileBlock.findFirst({
    where: {
      OR: [
        {
          blockerProfileId: viewerProfileId,
          blockedProfileId: viewedProfileId,
        },
        {
          blockerProfileId: viewedProfileId,
          blockedProfileId: viewerProfileId,
        },
      ],
    },
  });
}

export async function getLikeStatus(
  viewerProfileId: string,
  likedProfileId: string,
) {
  return await prisma.profileLike.findUnique({
    where: {
      likerProfileId_likedProfileId: {
        likerProfileId: viewerProfileId,
        likedProfileId: likedProfileId,
      },
    },
  });
}

export async function getRecommendedProfiles(viewerProfileId: string) {
  return await prisma.profile.findMany({
    where: {
      NOT: {
        id: viewerProfileId,
      },
    },
    include: {
      ...PROFILE_VIEW_INCLUDES,
    },
  });
}
