import { HeightUnitsEnum, WeightUnitsEnum } from '#shared/constants/enums';

export const convertWeight = (
  weight: number,
  unit: WeightUnitsEnum = WeightUnitsEnum.KG,
) => {
  if (unit === WeightUnitsEnum.KG) return weight;

  // Convert from lb to kg (1 lb = 0.453592 kg)
  return weight * 0.453592;
};

export const convertHeight = (
  height: number,
  unit: HeightUnitsEnum = HeightUnitsEnum.CM,
) => {
  if (unit === HeightUnitsEnum.CM) return height;

  // Convert from inches to cm (1 inch = 2.54 cm)
  return height * 2.54;
};
