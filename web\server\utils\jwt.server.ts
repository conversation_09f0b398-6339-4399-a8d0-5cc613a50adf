import jwt, { type SignOptions } from 'jsonwebtoken';

export function signJwt(
  payload: Record<string, any>,
  options?: SignOptions,
): string {
  if (!process.env.JWT_SECRET) {
    throw new Error('JWT_SECRET not set');
  }
  return jwt.sign(payload, process.env.JWT_SECRET, options);
}

export function verifyJwt(token: string, options?: jwt.VerifyOptions) {
  if (!process.env.JWT_SECRET) {
    throw new Error('JWT_SECRET not set');
  }
  return jwt.verify(token, process.env.JWT_SECRET, options);
}
