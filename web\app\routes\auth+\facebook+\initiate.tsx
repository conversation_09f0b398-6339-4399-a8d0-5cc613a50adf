import crypto from 'crypto';
import { generateFacebookOauthUrl } from '#app/utils/facebook.server';
import { handleLoader } from '#app/utils/remix-helpers.server';
import { authSessionStorage } from '#server/utils/session.server.js';
import { SocialProvider } from '#shared/constants/enums.js';

export const loader = handleLoader(async ({ request, redirect, }) => {
  // TODO: handle next url for redirect
  const { getSession } = authSessionStorage;
  const session = await getSession();
  const state = crypto.randomBytes(32).toString('hex');
  
  session.set('oauth_state', state);
  session.set('oauth_provider', SocialProvider.FACEBOOK);
  session.set('oauth_timestamp', Date.now());

  const redirectUrl = await generateFacebookOauthUrl(request.url, state);
  return redirect(
    redirectUrl,
    {
      headers: {
        'Set-Cookie': await authSessionStorage.commitSession(session),
      },
    },
  );
});
