import {
  type User,
  VerificationStatus,
  type SocialProvider,
} from '@prisma/client';
import { prisma } from '#server/utils/db.server';
import {
  SEARCH_BASIC_ROUTE,
  PROFILE_NEW_ROUTE,
  PROFILE_SETUP_BIO_ROUTE,
} from '#shared/constants/routes';

// TODO: consolidate with other usser.server file, it should live here instead of shared
export async function getBestRouteToLandUser(user: User) {
  const profile = await prisma.profile.findUnique({
    where: { userId: user.id },
  });

  if (profile && !profile.completedProfileSetup) {
    return PROFILE_SETUP_BIO_ROUTE;
  }

  // if the user has a profile, send them to the search page
  if (profile) {
    return SEARCH_BASIC_ROUTE;
  }
  // otherwise, send them to the profile creation page
  return PROFILE_NEW_ROUTE;
}

export async function changeUserVerificationStatus(user: User) {
  // may want diffe
  return await prisma.user.update({
    where: { id: user.id },
    data: {
      verifiedAt: new Date(),
      verificationStatus: VerificationStatus.APPROVED,
    },
  });
}

export function genUniqueIdFromEmail(email: string) {
  return `email:${email.toLowerCase()}`;
}

export function generateUniqueIdFromSocial(
  socialProvider: SocialProvider,
  providerId: string,
) {
  return `${socialProvider.toLocaleLowerCase()}:${providerId}`;
}
