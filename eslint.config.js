import { config as defaultConfig } from '@epic-web/config/eslint';
import react from 'eslint-plugin-react';
import reactHooks from 'eslint-plugin-react-hooks';

/** @type {import("eslint").Linter.Config[]} */
export default [
  ...defaultConfig,
  {
    files: ['**/*.{js,jsx,mjs,cjs,ts,tsx}'],
    plugins: {
      react,
      'react-hooks': reactHooks,
    },
    languageOptions: {
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
    settings: {
      react: {
        version: 'detect',
      },
    },
    rules: {
      ...react.configs.recommended.rules,
      ...reactHooks.configs.recommended.rules,
      'react/react-in-jsx-scope': 'off',
      'react/prop-types': 'off',
      'react/no-unescaped-entities': 'off',
    },
  },
  {
    languageOptions: {
      sourceType: 'module',
    },
    rules: {
      'no-unused-vars': ['warn', { args: 'none' }],
    },
  },
  {
    ignores: [
      '.config/*',
      '**/*build/*',
      'node_modules/*',
      '**/*public/*',
      'scripts/*',
      '**/*server-build/*',
      'src/*',
      'test/*',
      '.expo/*',
      '.github/*',
      '.vscode/*',
      '.yarn/*',
      'data/*',
      'mobile/*',
    ],
  },
];
