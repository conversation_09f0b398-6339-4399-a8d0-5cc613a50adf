// REACT
import type React from 'react';
import { useState, useEffect, useCallback, useMemo, useRef } from 'react';

// COMPONENTS
import { CalendarPopoverContent } from '#app/components/forms/calendar/calendar-popover-content';
import { CalendarPopoverTrigger } from '#app/components/forms/calendar/calendar-popover-trigger';
import { Input } from '#app/components/forms/inputs/input.js';
import { Popover } from '#app/components/ui/popover';

// STYLES
import { STYLES } from '#app/styles/calendar-picker-styles';

// UTILS
import {
  parseDate,
  formatDate,
  isDateTooRecent,
  MIN_AGE_DATE,
} from '#app/utils/calendar-helpers';

type DatePickerProps = {
  name: string;
  value: string;
  onChange?: (date: string | undefined) => void;
  calendarProps?: {
    disabled?: (date: Date) => boolean;
  };
  onBlur?: (date: any) => void;
};

export function DatePicker({ name, value, onChange, onBlur }: DatePickerProps) {
  const [date, setDate] = useState<Date | undefined>(parseDate(value));
  const [inputValue, setInputValue] = useState(formatDate(value));
  const [open, setOpen] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [viewDate, setViewDate] = useState<Date>(date || MIN_AGE_DATE);
  const [yearSelectOpen, setYearSelectOpen] = useState(false);
  const [monthSelectOpen, setMonthSelectOpen] = useState(false);
  const [animating, setAnimating] = useState(false);
  const [slideDirection, setSlideDirection] = useState('');
  const [hoveredDate, setHoveredDate] = useState<Date | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const handleFocus = () => setIsFocused(true);
    const handleBlur = () => setIsFocused(false);

    const input = inputRef.current;
    if (input) {
      input.addEventListener('focus', handleFocus);
      input.addEventListener('blur', handleBlur);

      return () => {
        input.removeEventListener('focus', handleFocus);
        input.removeEventListener('blur', handleBlur);
      };
    }
  }, []);

  const inputStyle = useMemo(() => {
    const baseStyle = {
      fontSize: '16px',
      padding: '12px 44px 12px 16px',
      width: '100%',
      borderRadius: '8px',
      border: '1px solid',
      borderColor: open || isFocused ? 'transparent' : '#EAEDF5',
      transition: 'all 0.2s ease',
      outline: 'none',
      boxShadow: 'none',
    };
    return open || isFocused
      ? { ...baseStyle, ...STYLES.gradientBorder }
      : baseStyle;
  }, [open, isFocused]);

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setInputValue(value);
      // Full validation occurs onBlur
      onChange?.(value);
    },
    [onChange],
  );

  const handleInputBlur = useCallback(
    (e: React.FocusEvent<HTMLInputElement>) => {
      const value = e.target.value;

      if (value === '') {
        setDate(undefined);
        setInputValue('');
        onBlur?.(undefined);
        return;
      }

      const parsedDate = parseDate(value, true);

      // Validate date
      const isValidDate =
        parsedDate &&
        !isDateTooRecent(parsedDate) &&
        parsedDate.getFullYear() >= 1900 &&
        parsedDate.getFullYear() <= new Date().getFullYear();

      if (!isValidDate) {
        // Reset to previous valid value or empty
        const fallbackDate = date ? formatDate(date.toISOString()) : '';
        setInputValue(fallbackDate);
        onBlur?.(undefined);
        return;
      }

      const formattedDate = formatDate(parsedDate.toISOString());
      setDate(parsedDate);
      setViewDate(parsedDate);
      setInputValue(formattedDate);
      onBlur?.(formattedDate);
    },
    [onBlur, date],
  );

  const handleDateSelect = useCallback(
    (selectedDate: Date) => {
      if (isDateTooRecent(selectedDate)) return;
      setDate(selectedDate);
      setInputValue(formatDate(selectedDate.toISOString()));
      onChange?.(selectedDate.toISOString());
      setTimeout(() => setOpen(false), 300);
    },
    [onChange],
  );

  const animate = useCallback((direction: string) => {
    setSlideDirection(direction);
    setAnimating(true);
    setTimeout(() => {
      setAnimating(false);
      setSlideDirection('');
    }, 250);
  }, []);

  return (
    <>
      <input type="hidden" name={name} value={date?.toISOString() || ''} />

      <Popover open={open} onOpenChange={setOpen}>
        <div className="relative w-full">
          <Input
            inputRef={inputRef}
            value={inputValue}
            placeholder="MM/DD/YYYY"
            onBlur={handleInputBlur}
            onChange={handleInputChange}
            maxLength={10}
            style={inputStyle}
            aria-label="Date selector"
          />

          <CalendarPopoverTrigger open={open} />
        </div>

        <CalendarPopoverContent
          viewDate={viewDate}
          selectedDate={date}
          hoveredDate={hoveredDate}
          yearSelectOpen={yearSelectOpen}
          monthSelectOpen={monthSelectOpen}
          animating={animating}
          slideDirection={slideDirection}
          setHoveredDate={setHoveredDate}
          setViewDate={setViewDate}
          setYearSelectOpen={setYearSelectOpen}
          setMonthSelectOpen={setMonthSelectOpen}
          onDateSelect={handleDateSelect}
          animate={animate}
        />
      </Popover>
    </>
  );
}

export default DatePicker;
