import { type z } from 'zod';
import { type TranslationFunction } from '#shared/types/translations';

// not sure about the any type
export type ZodValidator<T extends z.ZodRawShape = any> =
  | z.ZodEffects<z.ZodObject<T>>
  | z.ZodObject<T>
  | z.ZodObject<z.ZodRawShape>
  | z.ZodEffects<z.ZodObject<z.ZodRawShape>>;

export type CreateValidator<T extends z.ZodRawShape = any> = (
  t: TranslationFunction,
) => ZodValidator<T>;
