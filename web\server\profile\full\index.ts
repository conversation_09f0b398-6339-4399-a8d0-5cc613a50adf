import { type FastifyInstance } from 'fastify';
import { t } from '#server/utils/api.server';
import { handleFastifyRoute } from '#server/utils/fastify-helpers.server';
import {
  enrichProfileForFullProfileView,
  getProfileWithExtrasByProfileId,
} from '#server/utils/profile.server';

export async function fullProfileRouter(fastify: FastifyInstance) {
  // Define the GET route to fetch full profile data
  fastify.get(
    '/:profileId/full',
    handleFastifyRoute(async ({ request, sendSuccessPayload }) => {
      const { profileId } = request.params as { profileId: string };

      const profileWithExtras =
        await getProfileWithExtrasByProfileId(profileId);

      if (!profileWithExtras) {
        throw new Error(t('Profile not found.'));
      }

      const fullProfile = await enrichProfileForFullProfileView(
        profileWithExtras,
        request.profile.id,
      );

      sendSuccessPayload(fullProfile);
    }),
  );
}
