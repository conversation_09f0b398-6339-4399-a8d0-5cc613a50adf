import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { LinkButton } from '#app/components/buttons/link-button';
import { PhotoUpload } from '#app/components/files/photo-upload';
import { FormFromConfig } from '#app/components/forms/form-from-config.js';
import photoVerifyImage from '#app/images/photo-verify.webp';
import {
  handleFormActionForProfile,
  handleLoaderForProfileRoute,
} from '#app/utils/remix-helpers.server';
import { markPhotoAsVerified } from '#server/utils/media.server';
import { PhotoType } from '#shared/constants/enums.js';
import {
  PROFILE_EDIT_HOME_ROUTE,
  SEARCH_BASIC_ROUTE,
} from '#shared/constants/routes';
import { type FieldFromConfigType } from '#shared/types/forms';
import { type TranslationFunction } from '#shared/types/translations';
import { createPhotoValidator } from '#shared/utils/validators';

function createPhotoVerifyValidator(t: TranslationFunction) {
  return z.object({
    photo: createPhotoValidator(t),
  });
}

type Schema = z.infer<ReturnType<typeof createPhotoVerifyValidator>>;

// ensure the user is logged in and has a profile
export const loader = handleLoaderForProfileRoute();

export const action = handleFormActionForProfile(
  createPhotoVerifyValidator,
  async ({ successWithMessage, data: { photo } }) => {
    await markPhotoAsVerified({
      parentPhotoId: photo,
    });

    return successWithMessage('Photo uploaded successfully', {
      delayedRedirectTo: PROFILE_EDIT_HOME_ROUTE,
    });
  },
);

export default function PhotoVerify() {
  const { t } = useTranslation();
  const [fileId, setFileId] = useState('');
  const fields: FieldFromConfigType<Schema>[] = [
    {
      name: 'photo',
      type: 'hidden',
      defaultValue: fileId,
    },
  ];
  return (
    <div className="container mx-auto p-4">
      <h1 className="mb-4 text-2xl font-bold">Photo Verification</h1>
      <p className="mb-4">
        Please upload a photo that matches the pose in the image below:
      </p>
      <img src={photoVerifyImage} alt="Pose to match" className="mb-4" />
      {/* TODO: revisit again and incorporate in FormFromConfig */}
      <PhotoUpload
        value={fileId}
        onDelete={() => {
          setFileId('');
        }}
        onUpload={(fileId) => {
          setFileId(fileId);
        }}
        imageSize="large"
        photoType={PhotoType.VERIFICATION}
        path={PhotoType.VERIFICATION}
      />
      <FormFromConfig
        method="POST"
        action="/profile/photo-verify"
        // needed for file uploads
        encType="multipart/form-data"
        fields={fields}
        createValidator={createPhotoVerifyValidator}
        key={fileId}
      />
      <LinkButton to={SEARCH_BASIC_ROUTE}>{t('Skip')}</LinkButton>
    </div>
  );
}
