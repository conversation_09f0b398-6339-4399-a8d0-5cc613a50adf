import { UserType } from '@prisma/client';
import { type redirect as remixRedirect } from '@remix-run/node';
import { safeRedirect } from 'remix-utils/safe-redirect';
import { type CustomRedirect } from '#server/types/remix-helpers';
import { getBasicInfoFromRequest } from '#server/utils/auth.server';
import { prisma } from '#server/utils/db.server';
import {
  UserNotAdminError,
  UserNotLoggedInError,
} from '#server/utils/errors.server';
import { markProfileAsOffline } from '#server/utils/profile.server';
import { authSessionStorage } from '#server/utils/session.server';
import { SESSION_KEY } from '#shared/constants/auth';
import { LOGIN_ROUTE } from '#shared/constants/routes';
import { combineHeaders, combineResponseInits } from '#shared/utils/misc';

export async function handleNewSession(
  {
    request,
    session,
    redirectTo,
    redirect,
    remember = true,
  }: {
    request: Request;
    session: { privateUserId: string; id: string; expirationDate: Date };
    redirect: typeof remixRedirect;
    redirectTo: string;
    remember?: boolean;
  },
  responseInit?: ResponseInit,
) {
  const authSession = await authSessionStorage.getSession(
    request.headers.get('cookie'),
  );
  authSession.set(SESSION_KEY, session.id);

  return redirect(
    safeRedirect(redirectTo),
    combineResponseInits(
      {
        headers: {
          'set-cookie': await authSessionStorage.commitSession(authSession, {
            expires: remember ? session.expirationDate : undefined,
          }),
        },
      },
      responseInit,
    ),
  );
}

export async function requireUser<IncludeProfile extends boolean>({
  request,
  requireAdmin,
  includeProfile,
}: {
  request: Request;
  redirectTo?: string | null;
  redirect: typeof remixRedirect;
  includeProfile?: IncludeProfile;
  requireAdmin?: boolean;
}) {
  const { user, privateUser, profile, authSession } =
    await getBasicInfoFromRequest({
      request,
      includeProfile,
    });
  if (!user || !privateUser) {
    throw new UserNotLoggedInError({
      authSession,
      attemptedUrl: request.url,
    });
  }
  if (requireAdmin && user.userType !== UserType.ADMIN) {
    throw new UserNotAdminError();
  }

  return { user, privateUser, profile };
}

export async function logout({
  request,
  redirect,
  responseInit,
}: {
  request: Request;
  redirect: CustomRedirect;
  responseInit?: ResponseInit;
}) {
  const authSession = await authSessionStorage.getSession(
    request.headers.get('cookie'),
  );
  const sessionId = authSession.get(SESSION_KEY);

  // Mark user as offline before logging them out
  if (sessionId) {
    try {
      const { profile } = await getBasicInfoFromRequest({
        request,
        includeProfile: true,
      });
      if (profile) {
        await markProfileAsOffline(profile.id);
      }
    } catch (error) {
      // Don't block logout if marking offline fails
      console.error('Error marking profile offline during logout:', error);
    }

    // the .catch is important because that's what triggers the query.
    // learn more about PrismaPromise: https://www.prisma.io/docs/orm/reference/prisma-client-reference#prismapromise-behavior
    void prisma.session
      .deleteMany({ where: { id: sessionId } })
      .catch(() => {});
  }
  return redirect(safeRedirect(LOGIN_ROUTE), {
    ...responseInit,
    headers: combineHeaders(
      { 'set-cookie': await authSessionStorage.destroySession(authSession) },
      responseInit?.headers,
    ),
  });
}
