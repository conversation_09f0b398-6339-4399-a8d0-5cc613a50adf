// LIBRARIES
import {
  format,
  isValid,
  parse,
  addMonths,
  isAfter,
  isSameDay,
  subYears,
} from 'date-fns';

// Constants
export const TODAY = new Date();
export const MIN_AGE_YEARS = 18;
export const MIN_AGE_DATE = subYears(TODAY, MIN_AGE_YEARS);
export const CURRENT_YEAR = TODAY.getFullYear();
export const MIN_YEAR = MIN_AGE_DATE.getFullYear();

// Parses a date string into a Date object
export function parseDate(value: string, fromInput = false): Date | undefined {
  if (!value) return undefined;

  try {
    if (fromInput) {
      const parsedDate = parse(value, 'MM/dd/yyyy', new Date());
      return isValid(parsedDate) ? parsedDate : undefined;
    } else {
      const parsedDate = new Date(value);
      return isValid(parsedDate) ? parsedDate : undefined;
    }
  } catch {
    return undefined;
  }
}

// Formats a date string to MM/dd/yyyy format
export function formatDate(value: string): string {
  if (!value) return '';

  try {
    return format(new Date(value), 'MM/dd/yyyy');
  } catch {
    return value;
  }
}

// Checks if a date is too recent (less than MIN_AGE_YEARS old)
export function isDateTooRecent(date: Date): boolean {
  return isAfter(date, MIN_AGE_DATE);
}

// Checks if a date is hovered

export function isHovered(date: Date, hoveredDate: Date | null): boolean {
  return hoveredDate !== null && isSameDay(date, hoveredDate);
}

// Checks if the next month would exceed the minimum age date
export function isNextMonthDisabled(viewDate: Date): boolean {
  const nextMonth = addMonths(viewDate, 1);

  if (nextMonth.getFullYear() > MIN_AGE_DATE.getFullYear()) {
    return true;
  }

  if (
    nextMonth.getFullYear() === MIN_AGE_DATE.getFullYear() &&
    nextMonth.getMonth() > MIN_AGE_DATE.getMonth()
  ) {
    return true;
  }

  return false;
}
