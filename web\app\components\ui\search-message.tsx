import { useTranslation } from 'react-i18next';
import { IoSearchOutline, IoClose } from 'react-icons/io5';
import { cn } from '#app/utils/misc.js';

interface SearchMessageProps {
  searchValue: string;
  setSearchValue: (value: string) => void;
  onClearInput: () => void;
}

const SearchMessage = ({
  searchValue,
  setSearchValue,
  onClearInput,
}: SearchMessageProps) => {
  const { t } = useTranslation();

  return (
    <div className="px-6 mb-4">
      <div className="relative group">
        <input
          type="text"
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          placeholder={t('Search for a conversation')}
          className={cn(
            'w-full h-11 pl-11 pr-11 text-base bg-white',
            'border border-[#CACACA] rounded-full outline-none',
            'placeholder:text-muted-foreground/60',
            'focus:border-2 focus:border-transparent focus:ring-0 focus:gradient-border',
          )}
        />

        <IoSearchOutline
          className="absolute left-4 top-1/2 -translate-y-1/2 text-muted-foreground/60"
          size={20}
        />

        {searchValue && (
          <button
            onClick={onClearInput}
            className={cn(
              'absolute right-3 top-1/2 -translate-y-1/2',
              'p-1.5 rounded-full',
              'bg-gradient-to-r from-brand-secondary to-brand-primary',
              'active:transition-transform active:duration-300 active:ease-in-out',
              'active:scale-95',
            )}
          >
            <IoClose className="text-white" size={16} />
          </button>
        )}
      </div>
    </div>
  );
};

export default SearchMessage;
