import * as Tabs from '@radix-ui/react-tabs';
import {
  Link,
  useLoaderData,
  useNavigate,
  useSearchParams,
} from '@remix-run/react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FaAngleRight, FaSliders } from 'react-icons/fa6';
import { z } from 'zod';
import {
  FormFromConfig,
  FormType,
} from '#app/components/forms/form-from-config';
import { Tagline } from '#app/components/header/tagline';
import { ProfileResult } from '#app/components/search/profile-result';
import StoriesContainer from '#app/components/stories/stories-container.js';
import Typography from '#app/components/typography/index.js';
import { Button, buttonVariants } from '#app/components/ui/button';
import { Icon } from '#app/components/ui/icon';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '#app/components/ui/pagination';
import { cn, isMobileBrowser } from '#app/utils/misc';
import { getAgeRangeDefaults } from '#app/utils/profile';
import { handleLoaderForProfileRoute } from '#app/utils/remix-helpers.server';
import { performSearch, type SearchParameters } from '#app/utils/search.server';
import { performStoriesSearch } from '#app/utils/stories.server.js';
import { t } from '#server/utils/api.server';
import { prisma } from '#server/utils/db.server';
import { ZodValidationError } from '#server/utils/errors.server';
import {
  getLocationByProfileId,
  isInternational,
} from '#server/utils/location.server';
import {
  type GenderEnum,
  type NeverSometimesOften,
  ProfileCategoryEnum,
  RelationshipEnum,
  type ReligionEnum,
  type YesNo,
  type YesNoNotSure,
} from '#shared/constants/enums';
import { DEFAULT_PAGE } from '#shared/constants/pagination.js';
import { type FieldsFromConfig } from '#shared/types/forms';
import { type TranslationFunction } from '#shared/types/translations';
import {
  getNeverSometimesOftenFormOptions,
  getRelationshipEnumFormOptions,
  getReligionEnumFormOptions,
  getYesNoFormOptions,
  getYesNoNotSureFormOptions,
} from '#shared/utils/form-options';
import {
  createAgeRangeValidator,
  createChildrenPreferenceValidator,
  createDrinkingHabitsPreferenceValidator,
  createGenderInterestValidator,
  createMultiSelectEnumValidator,
  createReligionPreferenceValidator,
  createSmokingHabitsPreferenceValidator,
  createWantChildrenPreferenceValidator,
} from '#shared/utils/validators';

export const handle = { shouldShowFooter: true };

const categories = [
  { label: 'Recommended', value: ProfileCategoryEnum.RECOMMENDED },
  { label: 'New profiles', value: ProfileCategoryEnum.NEW_PROFILES },
  { label: 'Most liked', value: ProfileCategoryEnum.MOST_LIKED },
  { label: 'Online now', value: ProfileCategoryEnum.ONLINE_NOW },
];

/**
 * Make a page that has a form to make a GET request to the endpoint to load in the profiles from performSearch and then render them in a flex
 */

const createSearchValidator = (t: TranslationFunction) => {
  return z.object({
    ageRange: createAgeRangeValidator(t).optional(),
    genderInterest: createGenderInterestValidator(t).optional(),
    relationshipPreference: createMultiSelectEnumValidator(
      RelationshipEnum,
      t('Invalid option for relationship preference'),
    ).optional(),
    childrenPreference: createChildrenPreferenceValidator(t).optional(),
    wantChildrenPreference: createWantChildrenPreferenceValidator(t).optional(),
    religionPreference: createReligionPreferenceValidator(t).optional(),
    smokingHabitsPreference:
      createSmokingHabitsPreferenceValidator(t).optional(),
    drinkingHabitsPreference:
      createDrinkingHabitsPreferenceValidator(t).optional(),
    page: z
      .string()
      .optional()
      .transform((val) => (val ? parseInt(val, 10) : undefined))
      .refine((val) => val === undefined || !isNaN(val), {
        message: 'Page must be a valid number',
      }),
    size: z
      .string()
      .optional()
      .transform((val) => (val ? parseInt(val, 10) : undefined))
      .refine((val) => val === undefined || !isNaN(val), {
        message: 'Size must be a valid number',
      }),
    category: z.nativeEnum(ProfileCategoryEnum).optional(),
  });
};

const filterAnyValues = <T extends Record<string, any>>(data: T): Partial<T> =>
  Object.fromEntries(
    Object.entries(data).filter(([_, value]) => value !== 'Any'),
  ) as Partial<T>;

export const loader = handleLoaderForProfileRoute(
  async ({ profile, request }) => {
    const userAgent = request.headers.get('user-agent') || '';
    const isMobile = isMobileBrowser(userAgent);
    const url = new URL(request.url);
    const params = Object.fromEntries(url.searchParams.entries());

    // Filter out "Any" values
    const filteredParams = filterAnyValues(params);

    // Pass the filtered data to Zod
    const searchValidator = createSearchValidator(t);
    const result = searchValidator.safeParse(filteredParams);

    if (!result.success) {
      throw new ZodValidationError(result.error);
    }
    const datingPreference = await prisma.datingPreference.findUnique({
      where: { profileId: profile.id },
    });
    const { ageRange, ...validatedParams } = result.data;

    // Dynamically construct search parameters based on presence in URL
    const searchParams: Partial<SearchParameters> = {
      gender: profile.gender,
      genderInterest: validatedParams.genderInterest ?? profile.genderInterest,
      page: validatedParams.page,
      size: validatedParams.size,
      category: validatedParams.category,
    };

    if (ageRange) {
      searchParams.minAge = ageRange.minAge ?? datingPreference?.minAge;
      searchParams.maxAge = ageRange.maxAge ?? datingPreference?.maxAge;
    }

    if (filteredParams.smokingHabitsPreference) {
      searchParams.smoking = [
        params.smokingHabitsPreference as NeverSometimesOften,
      ];
    }

    if (filteredParams.drinkingHabitsPreference) {
      searchParams.drinking = [
        params.drinkingHabitsPreference as NeverSometimesOften,
      ];
    }

    if (filteredParams.childrenPreference) {
      searchParams.haveChildren = params.childrenPreference as YesNo;
    }

    if (filteredParams.wantChildrenPreference) {
      searchParams.wantChildren = params.wantChildrenPreference as YesNoNotSure;
    }

    if (filteredParams.religionPreference) {
      searchParams.religion = params.religionPreference as ReligionEnum;
    }

    if (validatedParams.relationshipPreference) {
      searchParams.relationshipPreference =
        validatedParams.relationshipPreference;
    }

    const [searchResults, storiesResults] = await Promise.all([
      performSearch(profile, searchParams),
      performStoriesSearch(profile),
    ]);

    const location = await getLocationByProfileId(profile.id);

    return {
      searchResults,
      profile,
      datingPreference,
      isMobile,
      storiesResults,
      isInternational: isInternational(location),
    };
  },
);

export default function SearchPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const {
    searchResults,
    profile,
    datingPreference,
    isMobile,
    storiesResults,
    isInternational,
  } = useLoaderData<typeof loader>();
  const [searchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState<string>('basic');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const currentPage = Number(searchParams.get('page') || DEFAULT_PAGE);

  const activeCategory = searchParams.get('category') || 'recommended';

  const ageRange: [number, number] | undefined = searchParams.get('ageRange')
    ? (searchParams.get('ageRange')!.split(',').map(Number) as [number, number])
    : getAgeRangeDefaults(datingPreference);

  const genderInterest: GenderEnum[] | undefined = searchParams.get(
    'genderInterest',
  )
    ? (searchParams.get('genderInterest')!.split(',') as GenderEnum[])
    : (profile.genderInterest as GenderEnum[]);

  const relationshipPreference: string[] | undefined = searchParams.get(
    'relationshipPreference',
  )
    ? searchParams.get('relationshipPreference')!.split(',')
    : datingPreference?.relationshipPreference || [];

  const childrenPreference: YesNo =
    (searchParams.get('childrenPreference') as YesNo) ?? 'Any';
  const wantChildrenPreference: YesNoNotSure =
    (searchParams.get('wantChildrenPreference') as YesNoNotSure) ?? 'Any';

  const religionPreference: ReligionEnum =
    (searchParams.get('religionPreference') as ReligionEnum) ?? 'Any';

  const smokingHabitsPreference: NeverSometimesOften =
    (searchParams.get('smokingHabitsPreference') as NeverSometimesOften) ??
    'Any';
  const drinkingHabitsPreference: NeverSometimesOften =
    (searchParams.get('drinkingHabitsPreference') as NeverSometimesOften) ??
    'Any';

  const basicFields: FieldsFromConfig<typeof createSearchValidator> = [
    {
      name: 'ageRange',
      label: t('Age Range'),
      type: 'age-range-slider',
      defaultValue: ageRange,
    },
    {
      name: 'genderInterest',
      label: t('Gender Interest'),
      type: 'gender-multi-checkbox',
      defaultValue: genderInterest,
      showAny: true,
    },
    {
      name: 'relationshipPreference',
      label: t('Relationship Preference'),
      type: 'multi-checkbox',
      options: getRelationshipEnumFormOptions(t),
      defaultValue: relationshipPreference,
      showAny: true,
    },
  ];

  const rowFilterStyling =
    'flex flex-row gap-4 space-y-0 items-center flex-wrap';

  const tabTriggerStyling =
    'w-1/2 px-4 py-2 font-bold transition-colors data-[state=active]:text-white data-[state=active]:bg-brand-primary rounded-2xl';

  const advancedFields: FieldsFromConfig<typeof createSearchValidator> = [
    {
      name: 'childrenPreference',
      label: t('Have Children'),
      type: 'radio-select',
      options: getYesNoFormOptions(t),
      defaultValue: childrenPreference,
      showAny: true,
      containerClassName: rowFilterStyling,
    },
    {
      name: 'wantChildrenPreference',
      label: t('Want Children'),
      type: 'radio-select',
      options: getYesNoNotSureFormOptions(t),
      defaultValue: wantChildrenPreference,
      showAny: true,
      containerClassName: rowFilterStyling,
    },
    {
      name: 'religionPreference',
      label: t('Religion Preference'),
      type: 'radio-select',
      options: getReligionEnumFormOptions(t),
      defaultValue: religionPreference,
      showAny: true,
      containerClassName: 'grid grid-cols-2 gap-1',
    },
    {
      name: 'smokingHabitsPreference',
      label: t('Smoking Habits'),
      type: 'radio-select',
      options: getNeverSometimesOftenFormOptions(t),
      defaultValue: smokingHabitsPreference,
      showAny: true,
    },
    {
      name: 'drinkingHabitsPreference',
      label: t('Drinking Habits'),
      type: 'radio-select',
      options: getNeverSometimesOftenFormOptions(t),
      defaultValue: drinkingHabitsPreference,
      showAny: true,
    },
  ];

  const allFields: FieldsFromConfig<typeof createSearchValidator> = [
    ...basicFields,
    ...advancedFields,
  ];

  const handleFilterToggle = () => {
    setIsFilterOpen((prev) => !prev);
  };

  const getPageNumbers = (totalPages: number, page: number) => {
    const pages = [];
    const showPagesAround = 2;

    if (totalPages <= 7) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      pages.push(1);
      let left = Math.max(2, page - showPagesAround);
      let right = Math.min(totalPages - 1, page + showPagesAround);

      if (left > 2) pages.push('...');
      for (let i = left; i <= right; i++) {
        pages.push(i);
      }
      if (right < totalPages - 1) pages.push('...');
      pages.push(totalPages);
    }

    return pages;
  };

  const updateQueryParams = (updates: Record<string, number | string>) => {
    const params = new URLSearchParams(searchParams);

    Object.entries(updates).forEach(([key, value]) => {
      params.set(key, value.toString());
    });

    return `?${params.toString()}`;
  };

  return (
    <>
      <Tagline isInternational={isInternational} />

      <div className="flex flex-col items-center justify-center w-full max-w-[1366px] mx-auto">
        <div className="p-8 w-full pb-0">
          <div className="bg-card p-0 rounded-[1.5rem]">
            <Typography
              variant="h3"
              className="px-6 pt-6 bg-gradient-to-r from-brand-secondary to-brand-primary bg-clip-text text-transparent mb-4"
            >
              {t('Explore stories, meet more people')}
            </Typography>
            <StoriesContainer stories={storiesResults} />
          </div>
        </div>
        <div className="flex mt-10 sm:p-8 p-0 gap-7 w-full">
          {isFilterOpen && (
            <Tabs.Root
              onValueChange={setActiveTab}
              defaultValue="basic"
              className="h-max min-w-80 border rounded-xl bg-white tablet:hidden p-4"
            >
              <div className="mb-2 flex items-center justify-between">
                <div className="flex gap-2 items-center">
                  <FaSliders />
                  <Typography variant="h5">{t('Filters')}</Typography>
                </div>
                <div
                  className="cursor-pointer"
                  onClick={() => setIsFilterOpen(false)}
                >
                  <FaAngleRight />
                </div>
              </div>
              <Tabs.List className="flex w-full space-x-4 bg-muted rounded-xl">
                <Tabs.Trigger value="basic" className={tabTriggerStyling}>
                  {t('Basic')}
                </Tabs.Trigger>

                <Tabs.Trigger value="advanced" className={tabTriggerStyling}>
                  {t('Advanced')}
                </Tabs.Trigger>
              </Tabs.List>

              {/* Consolidate into a single FormFromConfig */}
              <FormFromConfig
                method="GET"
                action="/search/basic"
                fields={allFields}
                createValidator={createSearchValidator}
                formType={FormType.PRIMITIVE}
                showResetButton
                handleReset={() => {
                  navigate(window.location.pathname);
                }}
                activeTab={activeTab}
                basicFields={basicFields.map((field) => field.name)}
                renderFields={({ fields, submitButton, resetButton }) => {
                  return (
                    <div className="space-y-4 py-4">
                      {activeTab === 'basic' && (
                        <div className="flex flex-col gap-4">
                          <div className="border-b pb-4">
                            {fields.ageRange.node}
                          </div>
                          <div className="border-b pb-4">
                            {fields.genderInterest.node}
                          </div>
                          <div>{fields.relationshipPreference.node}</div>
                        </div>
                      )}

                      {activeTab === 'advanced' && (
                        <div className="flex flex-col gap-4">
                          <div className="border-b pb-4">
                            {fields.childrenPreference.node}
                          </div>
                          <div className="border-b pb-4">
                            {fields.wantChildrenPreference.node}
                          </div>
                          <div className="border-b pb-4">
                            {fields.religionPreference.node}
                          </div>
                          <div className="border-b pb-4">
                            {fields.smokingHabitsPreference.node}
                          </div>
                          <div>{fields.drinkingHabitsPreference.node}</div>
                        </div>
                      )}
                      <div className="ml-auto flex w-fit justify-end gap-2">
                        {resetButton}
                        {submitButton}
                      </div>
                    </div>
                  );
                }}
              />
            </Tabs.Root>
          )}

          <div className="flex flex-col gap-8 tablet:flex-1 tablet:items-center tablet:gap-4 grow mb-10">
            <div className="flex flex-row gap-4 tablet:gap-1">
              <Button
                onClick={handleFilterToggle}
                className="rounded bg-gradient-to-r from-brand-secondary to-brand-primary transition-colors duration-300 hover:from-brand-primary hover:to-brand-secondary focus:outline-none focus:ring-2 focus:ring-brand-primary tablet:hidden"
              >
                <Icon name="options" color="white" className="h-4 w-4" />
              </Button>

              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
                {categories.map((category) => {
                  const isActive = activeCategory === category.value;

                  return (
                    <Link
                      key={category.value}
                      to={updateQueryParams({
                        category: category.value,
                        page: 1,
                      })}
                      className={cn(
                        buttonVariants({
                          variant: isActive ? 'default' : 'ghost',
                        }),
                        isActive ? '' : 'text-black',
                      )}
                    >
                      {t(category.label)}
                    </Link>
                  );
                })}
              </div>
            </div>

            <div
              className={cn(
                'grid justify-items-center gap-y-8 pt-10 pb-10 rounded-[1.5rem] bg-white  border border-muted tablet:grid-cols-2 tablet:gap-4 tablet:bg-transparent tablet:p-4',
                isFilterOpen
                  ? 'desktop-md-lg:grid-cols-3 desktop-sm-md:grid-cols-1 grid-cols-3 desktop-md:grid-cols-2'
                  : 'desktop-md-lg:grid-cols-4 desktop-sm-md:grid-cols-3 grid-cols-4 desktop-sm:grid-cols-2',
              )}
            >
              {searchResults.data.map((profile) => (
                <ProfileResult
                  isMobile={isMobile}
                  key={profile.id}
                  profile={profile}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
      {searchResults.totalPages > 1 && (
        <Pagination className="flex pb-8 w-full justify-center">
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                to={
                  currentPage > 1
                    ? updateQueryParams({ page: currentPage - 1 })
                    : '#'
                }
                disabled={currentPage <= 1}
                className="flex"
              />
            </PaginationItem>

            {getPageNumbers(searchResults.totalPages, searchResults.page).map(
              (pageNumber, index) =>
                typeof pageNumber === 'number' ? (
                  <PaginationItem key={index}>
                    <PaginationLink
                      className="rounded-full"
                      to={updateQueryParams({ page: pageNumber })}
                      isActive={pageNumber === currentPage}
                    >
                      {pageNumber}
                    </PaginationLink>
                  </PaginationItem>
                ) : (
                  <PaginationItem key={index}>
                    <PaginationEllipsis />
                  </PaginationItem>
                ),
            )}

            <PaginationItem>
              <PaginationNext
                to={
                  currentPage < searchResults.totalPages
                    ? updateQueryParams({ page: currentPage + 1 })
                    : '#'
                }
                disabled={currentPage >= searchResults.totalPages}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
    </>
  );
}
