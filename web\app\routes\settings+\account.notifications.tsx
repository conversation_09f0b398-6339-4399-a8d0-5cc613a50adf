import { useState } from 'react';
import Typography from '#app/components/typography';
import { Button } from '#app/components/ui/button';
import { handleLoaderForLoggedInUser } from '#app/utils/remix-helpers.server';
import {
  ExternalCommunicationChannelEnum,
  MessageNotificationSettingEnum,
} from '#shared/constants/enums';

export const loader = handleLoaderForLoggedInUser(async ({ privateUser }) => {
  return { privateUser };
});

const RadioOption = ({
  name,
  value,
  checked,
  onChange,
  label,
}: {
  name: string;
  value: string;
  checked: boolean;
  onChange: (value: string) => void;
  label: string;
}) => {
  return (
    <div className="flex items-center justify-between py-2 max-w-sm">
      <span className="text-gray-700 font-medium">{label}</span>
      <div className="relative">
        <input
          type="radio"
          name={name}
          value={value}
          checked={checked}
          onChange={() => onChange(value)}
          className="sr-only"
        />
        <div
          className={`w-5 h-5 rounded-full border-2 transition-colors cursor-pointer flex items-center justify-center ${
            checked
              ? 'border-red-500 bg-red-500'
              : 'border-gray-300 bg-white hover:border-gray-400'
          }`}
          onClick={() => onChange(value)}
        >
          {checked && <div className="w-2 h-2 bg-white rounded-full"></div>}
        </div>
      </div>
    </div>
  );
};

export default function Notifications() {
  const [notificationType, setNotificationType] =
    useState<ExternalCommunicationChannelEnum>(
      ExternalCommunicationChannelEnum.EMAIL,
    );
  const [messageNotifications, setMessageNotifications] =
    useState<MessageNotificationSettingEnum>(
      MessageNotificationSettingEnum.EVERY_MESSAGE,
    );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Typography variant="h3" className="text-2xl font-bold">
          Notifications
        </Typography>
        <Button className="bg-red-500 hover:bg-red-600 text-white px-8 py-2 min-w-[140px]">
          Save changes
        </Button>
      </div>

      <div className="space-y-8">
        {/* Notification Type Section */}
        <div className="space-y-4">
          <Typography className="text-xs font-semibold">
            Notification type
          </Typography>

          <div className="space-y-3">
            <RadioOption
              name="notification-type"
              value={ExternalCommunicationChannelEnum.EMAIL}
              checked={
                notificationType === ExternalCommunicationChannelEnum.EMAIL
              }
              onChange={(value) =>
                setNotificationType(value as ExternalCommunicationChannelEnum)
              }
              label="Email"
            />
            <RadioOption
              name="notification-type"
              value={ExternalCommunicationChannelEnum.SMS}
              checked={
                notificationType === ExternalCommunicationChannelEnum.SMS
              }
              onChange={(value) =>
                setNotificationType(value as ExternalCommunicationChannelEnum)
              }
              label="SMS"
            />
          </div>
        </div>

        {/* Message Notifications Section */}
        <div className="space-y-4">
          <Typography className="text-xs font-semibold">
            Message notifications
          </Typography>

          <div className="space-y-3">
            <RadioOption
              name="message-notifications"
              value={MessageNotificationSettingEnum.EVERY_MESSAGE}
              checked={
                messageNotifications ===
                MessageNotificationSettingEnum.EVERY_MESSAGE
              }
              onChange={(value) =>
                setMessageNotifications(value as MessageNotificationSettingEnum)
              }
              label="Every message received"
            />
            <RadioOption
              name="message-notifications"
              value={MessageNotificationSettingEnum.NEW_CONVERSATION}
              checked={
                messageNotifications ===
                MessageNotificationSettingEnum.NEW_CONVERSATION
              }
              onChange={(value) =>
                setMessageNotifications(value as MessageNotificationSettingEnum)
              }
              label="Only on new conversations"
            />
            <RadioOption
              name="message-notifications"
              value={MessageNotificationSettingEnum.NEVER}
              checked={
                messageNotifications === MessageNotificationSettingEnum.NEVER
              }
              onChange={(value) =>
                setMessageNotifications(value as MessageNotificationSettingEnum)
              }
              label="Never"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
