import { CheckCircleIcon } from '@heroicons/react/20/solid';
import { useNavigate, useRevalidator } from '@remix-run/react';
import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import ProfileViewWithLoading from '#app/components/profile/profile-view-with-loading';
import { Icon } from '#app/components/ui/icon';
import Vignette from '#app/components/vignette';
import { type SearchProfile } from '#app/types/serialized-types';
import { cn } from '#app/utils/misc';
import { formatLastOnlineTime } from '#shared/utils/last-online-formatter';
import { getUserTimeZone } from '#shared/utils/timezone';

type ProfileResultProps = {
  profile: SearchProfile;
  isMobile?: boolean;
};

const ProfileResult: React.FC<ProfileResultProps> = ({
  profile,
  isMobile = false,
}) => {
  const { onlineStatus: statusWithTime } = profile;
  const isOnline = statusWithTime.onlineStatus === 'ONLINE';
  const userTimeZone = getUserTimeZone();
  const { t } = useTranslation();
  const onlineStatusText = formatLastOnlineTime({
    statusWithTime,
    userTimeZone,
    t,
  });

  const navigate = useNavigate();
  const revalidator = useRevalidator();

  const [isProfileViewOpen, setIsProfileViewOpen] = React.useState(false);

  const { username, firstName, age } = profile;

  const handleOpenProfileView = useCallback(() => {
    setIsProfileViewOpen(true);
  }, []);

  const handleOpenChange = useCallback(
    (isOpen: boolean) => {
      setIsProfileViewOpen(isOpen);
      if (!isOpen) {
        revalidator.revalidate();
      }
    },
    [revalidator],
  );

  return (
    <div
      onClick={() => {
        const action = isMobile
          ? () => navigate(`/profile/${username}`)
          : handleOpenProfileView;

        action();
      }}
      className="cursor-pointer flex h-[282px] tablet:h-[152px] "
    >
      <div className="flex flex-1 tablet:rounded-[1rem] rounded-[1.2rem] bg-gray-200 sm:w-[232px] w-[140px]">
        <div
          className={cn(
            'flex h-full w-full flex-col justify-between tablet:rounded-[1rem] rounded-[1.2rem] bg-cover bg-center',
          )}
          style={{
            backgroundImage: profile.mainPreviewPhoto?.mediumUrl
              ? `url(${profile.mainPreviewPhoto.mediumUrl})`
              : 'none',
          }}
          aria-label={t("{{username}}'s main photo", {
            username: profile.username,
          })}
        >
          <div className="flex flex-wrap items-center gap-2 justify-between px-4 pt-4 tablet:px-3 tablet:pt-3">
            <div
              className={cn(
                'w-fit flex items-center gap-1 rounded-md bg-[#1E1E1E80] px-2 py-0.5 mobile:px-1 mobile:py-0.5',
                isOnline ? 'text-background' : 'text-gray-300',
              )}
            >
              <div
                className={cn(
                  'h-3 w-3 rounded-full',
                  isOnline
                    ? 'bg-green-500 border-white border'
                    : 'bg-gray-500 border-gray-500 border',
                )}
              />
              <span className="tablet:text-xs text-sm font-medium">
                {onlineStatusText}
              </span>
            </div>
            {profile.emailIsVerified && (
              <div className="h-6 w-6 tablet:h-5 tablet:w-5 text-[#2F80ED] relative rounded-full">
                <CheckCircleIcon className="relative z-[1]" />
                <div className="bg-white absolute h-3 w-3 tablet:h-2 tablet:w-2 rounded-full top-1.5 right-1.5" />
              </div>
            )}
          </div>

          <div className="bg-[#1E1E1E05] tablet:p-2 relative px-4 pb-4 tablet:px-3 tablet:pb-3">
            <div className="flex justify-between relative z-[2]">
              <div>
                <p className="text-xl font-bold text-white tablet:text-base">{`${firstName}, ${age}`}</p>
                {profile.location && (
                  <div className="-mt-1 flex items-center">
                    <Icon
                      name="location-outline"
                      className="h-[18px] w-[18px] text-white tablet:h-[14px] tablet:w-[14px]"
                    />

                    <p className="text-white tablet:text-xs">{`${profile.location?.cityOrTown}, ${profile.location?.countryIso2}`}</p>
                  </div>
                )}
              </div>
              <div className="mt-1">
                <Icon
                  name={
                    profile.likesThisProfile ? 'heart-filled' : 'heart-outline'
                  }
                  className={cn(
                    'tablet:h-[18px] tablet:w-[18px] h-[24px] w-[24px]',
                    profile.likesThisProfile ? 'text-red-500' : 'text-white',
                  )}
                />
              </div>
            </div>

            <Vignette className="tablet:rounded-br-[1rem] z-[1] tablet:rounded-bl-[1rem] rounded-br-[1.2rem] rounded-bl-[1.2rem] h-[100px] opacity-[90%] w-full bottom-0 absolute left-0" />
          </div>
        </div>
      </div>

      <ProfileViewWithLoading
        profileId={profile.id}
        open={isProfileViewOpen}
        onOpenChange={handleOpenChange}
      />
    </div>
  );
};

export { ProfileResult };
