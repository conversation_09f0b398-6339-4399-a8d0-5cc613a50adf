import {
  $Enums,
  PhotoType,
  type PhotoVariant,
  type Video,
  VideoType,
} from '@prisma/client';
import sharp from 'sharp';
import { v4 } from 'uuid';
import { getCloudinaryUrl } from '#server/utils/cloudinary.server';
import { prisma } from '#server/utils/db.server';
import {
  DEFAULT_PHOTO_CROP,
  LARGE_PHOTO_MAX_DIMENSION,
  PREVIEW_PHOTO_MAX_DIMENSION,
  THUMBNAIL_PHOTO_MAX_DIMENSION,
} from '#shared/constants/media';
import {
  type ParentPhotoWithPhotoVariants,
  type VideoWithUrl,
} from '#shared/types/media';
import { deleteFileFromR2, uploadFileToR2 } from './cloudflare.server';
import { BadRequestError, NotFoundError } from './errors.server';
import {
  makeURLFromPhotoVariant,
  transformImageAndUploadToR2AndDatabase,
} from './image.server';

export interface PhotoUrls {
  largeUrl: string;
  mediumUrl: string;
  smallUrl: string;
}

async function uploadNewVideo({
  profileId,
  fileId,
  videoType,
  deleteOthers,
}: {
  profileId: string;
  fileId: string;
  videoType: VideoType;
  deleteOthers?: boolean;
  path?: string;
}) {
  return await prisma.$transaction(async (prisma) => {
    // Upload the new video
    const newVideo = await saveVideoToDatabase({
      videoType,
      profileId,
      fileId,
    });
    // Delete all other videos of the same type for the profile
    if (deleteOthers) {
      await prisma.video.deleteMany({
        where: {
          profileId,
          videoType,
          id: {
            not: newVideo.id,
          },
        },
      });
    }

    return newVideo;
  });
}

async function saveVideoToDatabase({
  videoType,
  profileId,
  fileId,
}: {
  videoType: VideoType;
  profileId: string;
  fileId: string;
}) {
  return await prisma.video.create({
    data: {
      videoType,
      profileId,
      fileId,
    },
  });
}

export async function markPhotoAsVerified({
  parentPhotoId,
}: {
  parentPhotoId: string;
}) {
  return await prisma.parentPhoto.update({
    where: {
      id: parentPhotoId,
    },
    data: {
      verificationStatus: $Enums.VerificationStatus.APPROVED,
    },
  });
}

export async function uploadAndSaveMainVideoPhoto({
  fileId,
  profileId,
}: {
  fileId: string;
  profileId: string;
}) {
  const subPath = `${profileId}/main-videos`;
  return await uploadNewVideo({
    videoType: VideoType.MAIN,
    profileId,
    fileId,
    deleteOthers: true,
    path: subPath,
  });
}

export async function uploadAndSaveStory({
  fileId,
  profileId,
}: {
  fileId: string;
  profileId: string;
}) {
  const subPath = `${profileId}/stories`;
  return await uploadNewVideo({
    videoType: VideoType.STORY,
    profileId,
    fileId,
    deleteOthers: false,
    path: subPath,
  });
}

export function createImgUrls(
  fileId: string,
  opts?: {
    crop?: string;
  },
) {
  const smallUrl = getCloudinaryUrl(fileId, {
    height: THUMBNAIL_PHOTO_MAX_DIMENSION,
    width: THUMBNAIL_PHOTO_MAX_DIMENSION,
    crop: opts?.crop ?? DEFAULT_PHOTO_CROP,
  });
  const mediumUrl = getCloudinaryUrl(fileId, {
    height: PREVIEW_PHOTO_MAX_DIMENSION,
    width: PREVIEW_PHOTO_MAX_DIMENSION,
    crop: opts?.crop ?? DEFAULT_PHOTO_CROP,
  });
  const largeUrl = getCloudinaryUrl(fileId, {
    height: LARGE_PHOTO_MAX_DIMENSION,
    width: LARGE_PHOTO_MAX_DIMENSION,
    crop: opts?.crop ?? DEFAULT_PHOTO_CROP,
  });
  return { smallUrl, mediumUrl, largeUrl };
}

export function createVideoUrl(fileId: string) {
  return `${process.env.CLOUDFLARE_PUBLIC_URL}/${fileId}`;
}

export function attachVideoUrlFromVideo(video: Video): VideoWithUrl {
  const url = createVideoUrl(video.fileId);

  return {
    url,
    ...video,
  };
}

function getVariantPhotoUrlFromVariants({
  photoVariants,
  variantType,
}: {
  photoVariants: PhotoVariant[];
  variantType: $Enums.VariantType;
}) {
  const photoVariant = photoVariants.find(
    (photoVariant) => photoVariant.variant === variantType,
  );

  if (!photoVariant) {
    throw new NotFoundError('PhotoVariant not found');
  }

  return makeURLFromPhotoVariant({ photoVariant });
}

export function attachImageUrlsToPhoto(photo: ParentPhotoWithPhotoVariants) {
  const photoVariants = photo.photoVariants;

  return {
    ...photo,
    smallUrl: getVariantPhotoUrlFromVariants({
      photoVariants,
      variantType: $Enums.VariantType.SMALL,
    }),
    mediumUrl: getVariantPhotoUrlFromVariants({
      photoVariants,
      variantType: $Enums.VariantType.MEDIUM,
    }),
    largeUrl: getVariantPhotoUrlFromVariants({
      photoVariants,
      variantType: $Enums.VariantType.LARGE,
    }),
  };
}

export async function getImageDimensions(file: File | Buffer) {
  const buffer =
    file instanceof Buffer
      ? file
      : Buffer.from(await (file as File).arrayBuffer());
  const metadata = await sharp(buffer).metadata();

  if (!metadata.width || !metadata.height) {
    throw new BadRequestError('Invalid image file');
  }

  return {
    width: metadata.width,
    height: metadata.height,
  };
}

async function uploadVideoAndSaveToDB({
  file,
  profileId,
  videoType,
}: {
  file: File;
  profileId: string;
  videoType: VideoType;
}) {
  const key = v4();
  await uploadFileToR2({
    file,
    key,
  });
  try {
    await prisma.video.create({
      data: {
        fileId: key,
        videoType,
        profileId,
      },
    });
  } catch {
    await deleteFileFromR2({ key });
  }
}

function isPhotoType(value: unknown): value is PhotoType {
  return Object.values(PhotoType).includes(value as PhotoType);
}

function isVideoType(value: unknown): value is VideoType {
  return Object.values(VideoType).includes(value as VideoType);
}

export async function uploadMediaFileAndStoreToDB({
  file,
  fileType,
  profileId,
}: {
  file: File;
  fileType: VideoType | PhotoType;
  profileId: string;
}) {
  const mimeType = file.type;
  const isImage = mimeType.includes('image/');
  const isVideo = mimeType.includes('video/');

  if (isImage && isPhotoType(fileType)) {
    await transformImageAndUploadToR2AndDatabase({
      file,
      photoType: fileType,
      profileId,
    });
    return;
  }

  if (isVideo && isVideoType(fileType)) {
    await uploadVideoAndSaveToDB({
      file,
      profileId,
      videoType: fileType as VideoType,
    });
    return;
  }

  throw new BadRequestError('File type is not allowed for upload');
}
