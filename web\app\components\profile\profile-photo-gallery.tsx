import { useTranslation } from 'react-i18next';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from '#app/components/ui/carousel';
import { type PhotoWithSignedUrl } from '#app/types/serialized-types.js';
import { cn } from '#app/utils/misc';
import { type OnlineStatusWithTime } from '#shared/types/profile';
import { formatLastOnlineTime } from '#shared/utils/last-online-formatter';
import { getUserTimeZone } from '#shared/utils/timezone';

export const ProfilePhotoGallery = ({
  photos,
  onlineStatus,
}: {
  photos?: PhotoWithSignedUrl[];
  onlineStatus?: OnlineStatusWithTime;
}) => {
  const { t } = useTranslation();
  const userTimeZone = getUserTimeZone();

  const hasOnlineStatusProp = typeof onlineStatus !== 'undefined';
  const isOnline = onlineStatus?.onlineStatus === 'ONLINE';
  const hasPhotos = photos && photos.length > 0;

  return (
    <div className="h-full w-full">
      <Carousel className="h-full relative">
        {hasOnlineStatusProp && (
          <div className="absolute top-0 left-0 z-[1] flex flex-wrap items-center gap-2 justify-between px-4 pt-4 tablet:px-3 tablet:pt-3">
            <div
              className={cn(
                'w-fit flex items-center gap-1 rounded-md bg-[#1E1E1E80] px-2 py-0.5 mobile:px-1 mobile:py-0.5',
                isOnline ? 'text-background' : 'text-gray-300',
              )}
            >
              <div
                className={cn(
                  'h-3 w-3 rounded-full',
                  isOnline
                    ? 'bg-green-500 border-white border'
                    : 'bg-gray-500 border-gray-500 border',
                )}
              />
              <span className="tablet:text-xs text-sm font-medium">
                {formatLastOnlineTime({
                  statusWithTime: onlineStatus!,
                  userTimeZone,
                  t,
                })}
              </span>
            </div>
          </div>
        )}

        <CarouselContent className="h-full gap-4 pl-4">
          {hasPhotos ? (
            photos.map((gp) => (
              <CarouselItem key={gp.id} className="h-full bg-muted p-0">
                <div className="h-full w-full">
                  <img
                    src={gp.largeUrl}
                    alt="Profile"
                    className="object-cover w-full h-full"
                  />
                </div>
              </CarouselItem>
            ))
          ) : (
            <CarouselItem className="h-full flex items-center justify-center text-sm text-muted-foreground">
              {t('No photos available')}
            </CarouselItem>
          )}
        </CarouselContent>
      </Carousel>
    </div>
  );
};
