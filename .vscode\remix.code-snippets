{
	"loader": {
		"prefix": "/loader",
		"scope": "typescriptreact,javascriptreact,typescript,javascript",
		"body": [
			"import { type LoaderFunctionArgs, json } from \"@remix-run/node\"",
			"",
			"export async function loader({ request }: LoaderFunctionArgs) {",
			"  return json({})",
			"}",
		],
	},
	"action": {
		"prefix": "/action",
		"scope": "typescriptreact,javascriptreact,typescript,javascript",
		"body": [
			"import { type ActionFunctionArgs, json } from \"@remix-run/node\"",
			"",
			"export async function action({ request }: ActionFunctionArgs) {",
			"  return json({})",
			"}",
		],
	},
	"default": {
		"prefix": "/default",
		"scope": "typescriptreact,javascriptreact,typescript,javascript",
		"body": [
			"export default function ${TM_FILENAME_BASE/[^a-zA-Z0-9]*([a-zA-Z0-9])([a-zA-Z0-9]*)/${1:/capitalize}${2}/g}() {",
			" return (",
			"  <div>",
			"    <h1>Unknown Route</h1>",
			"  </div>",
			" )",
			"}",
		],
	},
	"headers": {
		"prefix": "/headers",
		"scope": "typescriptreact,javascriptreact,typescript,javascript",
		"body": [
			"import type { HeadersFunction } from '@remix-run/node'",
			"",
			"export const headers: HeadersFunction = ({ loaderHeaders }) => ({",
			"  'Cache-Control': loaderHeaders.get('Cache-Control') ?? '',",
			"})",
		],
	},
	"links": {
		"prefix": "/links",
		"scope": "typescriptreact,javascriptreact,typescript,javascript",
		"body": [
			"import type { LinksFunction } from '@remix-run/node'",
			"",
			"export const links: LinksFunction = () => {",
			"  return []",
			"}",
		],
	},
	"meta": {
		"prefix": "/meta",
		"scope": "typescriptreact,javascriptreact,typescript,javascript",
		"body": [
			"import type { MetaFunction } from '@remix-run/node'",
			"",
			"export const meta: MetaFunction<typeof loader> = ({ data }) => [{",
			"  title: 'Title',",
			"}]",
		],
	},
	"shouldRevalidate": {
		"prefix": "/shouldRevalidate",
		"scope": "typescriptreact,javascriptreact,typescript,javascript",
		"body": [
			"import type { ShouldRevalidateFunction } from '@remix-run/react'",
			"",
			"export const shouldRevalidate: ShouldRevalidateFunction = ({",
			"  defaultShouldRevalidate",
			"}) => {",
			"  return defaultShouldRevalidate",
			"}",
		],
	},
}
