import { type SocialProvider } from '@prisma/client';
import { handleLoader } from '#app/utils/remix-helpers.server';
import { detectAndCacheUserLocation } from '#server/utils/auth-helpers.server';
import { signupWithSocialConnection } from '#server/utils/auth.server';
import { PROFILE_NEW_ROUTE } from '#shared/constants/routes';
import { getBaseUrlFromRequestUrl } from '#shared/utils/misc.js';
import { handleNewSession } from './remix-auth.server';

type SocialAuthCallback = (params: {
  state: string | null;
  code: string;
  request: Request;
}) => Promise<{
  email?: string;
  providerId: string;
  firstName?: string;
  oAuthTokenData: {
    accessToken: string;
    expiryDate: Date;
    scope: string;
    refresToken?: string;
  };
}>;

export const loaderForSocialAuthCallback = (
  providerName: SocialProvider,
  callback: SocialAuthCallback,
) => {
  return handleLoader(async ({ request, redirect }) => {
    const url = new URL(request.url);
    const state = url.searchParams.get('state');
    const code = url.searchParams.get('code');
    if (!code) {
      throw new Error('Missing code');
    }
    const { email, providerId, firstName, oAuthTokenData } = await callback({
      request,
      state,
      code,
    });
    const session = await signupWithSocialConnection({
      providerId,
      providerName,
      email,
      firstName,
      oAuthTokenData,
    });

    // Trigger IP location detection
    if (session.privateUser) {
      detectAndCacheUserLocation(request, session.privateUser).catch((error: any) => {
        console.error('Error detecting user location:', error);
      });
    }

    return handleNewSession({
      request,
      session,
      redirectTo: PROFILE_NEW_ROUTE,
      redirect,
    });
  });
};

export function getSocialRedirectUrl(
  provider: SocialProvider,
  requestUrl: string,
) {
  const baseUrl = getBaseUrlFromRequestUrl(requestUrl);
  // ngrok gives us http when really it's https
  const secureBaseUrl = baseUrl.replace('http://', 'https://');
  return `${secureBaseUrl}/auth/${provider.toLocaleLowerCase()}/callback`;
}
