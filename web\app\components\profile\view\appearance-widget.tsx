import { type TFunction } from 'i18next';
import { useTranslation } from 'react-i18next';
import { CgGirl } from 'react-icons/cg';
import {
  IoEarthOutline,
  IoEyeOutline,
  IoScaleOutline,
  IoShirtOutline,
} from 'react-icons/io5';
import { PiRuler } from 'react-icons/pi';
import { IconDetailItem } from '#app/components/profile/view';
import Typography from '#app/components/typography';
import { type SerializedAppearance } from '#app/types/serialized-types';
import {
  getBodyTypeLabelFromEnum,
  getHairColorLabelFromEnum,
  getEyeColorLabelFromEnum,
  getEthnicityLabelFromEnum,
} from '#shared/utils/form-options';

type Prop = {
  appearance: Omit<
    SerializedAppearance,
    'id' | 'createdAt' | 'updatedAt' | 'profileId'
  > | null;
};

const AppearanceMap = (
  appearance: Required<Prop['appearance']>,
  t: TFunction,
) => ({
  hairColor: (
    <IconDetailItem
      label={getHairColorLabelFromEnum(appearance?.hairColor!, t)}
      icon={<CgGirl fontSize={18} />}
    />
  ),
  eyeColor: (
    <IconDetailItem
      label={getEyeColorLabelFromEnum(appearance?.eyeColor!, t)}
      icon={<IoEyeOutline fontSize={18} />}
    />
  ),
  height: (
    <IconDetailItem
      label={`${appearance?.height} cm`}
      icon={<PiRuler fontSize={18} />}
    />
  ),
  weight: (
    <IconDetailItem
      label={`${appearance?.weight} kg`}
      icon={<IoScaleOutline fontSize={18} />}
    />
  ),
  bodyType: (
    <IconDetailItem
      label={getBodyTypeLabelFromEnum(appearance?.bodyType!, t)}
      icon={<IoShirtOutline fontSize={18} />}
    />
  ),
  ethnicity: (
    <IconDetailItem
      label={
        appearance?.ethnicity
          ? appearance.ethnicity
              .map((e) => getEthnicityLabelFromEnum(e, t))
              .join(', ')
          : ''
      }
      icon={<IoEarthOutline fontSize={18} />}
    />
  ),
});

function AppearanceWidget({ appearance }: Prop) {
  const { t } = useTranslation();

  const userAppearance = appearance ?? {
    hairColor: 'BLACK',
    eyeColor: 'BLACK',
    height: 0,
    weight: 0,
    bodyType: 'PETITE',
    ethnicity: ['FILIPINO'],
    gender: 'FEMALE',
  };

  return (
    <div>
      <Typography variant="h5" className="font-medium mb-2 !leading-none">
        {t('Appearance')}
      </Typography>

      <div className="grid grid-cols-2 grid-rows-3 gap-1 w-full mx-auto">
        {Object.entries(AppearanceMap(userAppearance, t)).map(
          ([key, value]) => (
            <div key={key}>{value}</div>
          ),
        )}
      </div>
    </div>
  );
}

export { AppearanceWidget };
