export interface DayStyleProps {
  isCurrentMonth: boolean;
  isTooRecent: boolean;
  dateIsHovered: boolean;
  isSelected: boolean;
}

export interface CalendarDayProps {
  date: Date;
  viewDate: Date;
  selectedDate?: Date;
  hoveredDate: Date | null;
  onDateSelect: (date: Date) => void;
  setHoveredDate: (date: Date | null) => void;
}

export interface YearItemProps {
  year: number;
  currentYear: number;
  viewDate: Date;
  setViewDate: (date: Date) => void;
  setYearSelectOpen: (open: boolean) => void;
}

export interface Month {
  name: string;
  value: number;
}

export interface MonthItemProps {
  month: Month;
  isActive: boolean;
  isDisabled: boolean;
  viewDate: Date;
  setViewDate: (date: Date) => void;
  setMonthSelectOpen: (open: boolean) => void;
}
