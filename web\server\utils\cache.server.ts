import Redis from 'ioredis';

// Configure Redis client using environment variables
const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
console.log(`Connecting to Redis at URL ${redisUrl}`);
const redis = new Redis(redisUrl);

export async function cacheGetKeys(keys: string) {
  try{
    return await redis.keys(keys);
  } catch (error) {
    console.error(`Error getting keys from cache for keys ${keys}:`, error);
    return null;
  }
}


// Wrapper function to get a single value by key
export async function cacheGet(key: string): Promise<string | null> {
  try {
    const value = await redis.get(key);
    return value;
  } catch (error) {
    console.error(`Error getting value from cache for key ${key}:`, error);
    return null;
  }
}

// Wrapper function to get multiple values by keys
export async function cacheGetMany(keys: string[]): Promise<(string | null)[]> {
  try {
    const values = await redis.mget(...keys);
    return values;
  } catch (error) {
    console.error(`<PERSON>rror getting values from cache for keys ${keys}:`, error);
    return keys.map(() => null); // Return an array of nulls if an error occurs
  }
}

// Wrapper function to set a single key-value pair in cache
export async function cacheSet(
  key: string,
  value: string,
  expirationInSeconds?: number,
): Promise<void> {
  try {
    if (expirationInSeconds) {
      await redis.set(key, value, 'EX', expirationInSeconds); // Set with expiration
    } else {
      await redis.set(key, value); // Set without expiration
    }
  } catch (error) {
    console.error(`Error setting value in cache for key ${key}:`, error);
  }
}

// Wrapper function to delete a single key from cache
export async function cacheDelete(key: string): Promise<void> {
  try {
    await redis.del(key);
  } catch (error) {
    console.error(`Error deleting value from cache for key ${key}:`, error);
  }
}
