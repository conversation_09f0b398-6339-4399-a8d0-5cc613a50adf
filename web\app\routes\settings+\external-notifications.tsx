import { useLoaderData } from '@remix-run/react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { FormFromConfig } from '#app/components/forms/form-from-config';
import {
  handleFormActionForProfile,
  handleLoaderForLoggedInUser,
} from '#app/utils/remix-helpers.server';
import { prisma } from '#server/utils/db.server';
import {
  ExternalCommunicationChannelEnum,
  MessageNotificationSettingEnum,
} from '#shared/constants/enums.js';
import { type FieldFromConfigType } from '#shared/types/forms.js';

export const loader = handleLoaderForLoggedInUser(
  async ({ privateUser, redirect }) => {
    if (!privateUser) {
      return redirect('/onboarding/profile/new');
    }

    const notificationSetting =
      await prisma.messageNotificationSetting.findFirst({
        where: { privateUserId: privateUser.id },
      });

    return { userInfo: privateUser, notificationSetting };
  },
);

function upsertExternalNotificationsValidator() {
  return z.object({
    communicationChannel: z.nativeEnum(ExternalCommunicationChannelEnum),
    notificationSetting: z.nativeEnum(MessageNotificationSettingEnum),
  });
}

type Schema = z.infer<ReturnType<typeof upsertExternalNotificationsValidator>>;

export const action = handleFormActionForProfile(
  upsertExternalNotificationsValidator,
  async ({ data, successWithMessage, privateUser }) => {
    // upsert
    await prisma.messageNotificationSetting.upsert({
      where: {
        privateUserId_communicationChannel: {
          communicationChannel: data.communicationChannel,
          privateUserId: privateUser.id,
        },
      },
      update: {
        setting: data.notificationSetting,
      },
      create: {
        privateUserId: privateUser.id,
        communicationChannel: data.communicationChannel,
        setting: data.notificationSetting,
      },
    });

    return successWithMessage('Success');
  },
);

export default function ExternalNotifications() {
  const { userInfo, notificationSetting } = useLoaderData<typeof loader>();
  const { t } = useTranslation();
  const fields: FieldFromConfigType<Schema>[] = useMemo(
    () => [
      {
        name: 'communicationChannel',
        type: 'radio-select',
        label: t('Notification type'),
        defaultValue:
          notificationSetting?.communicationChannel ||
          ExternalCommunicationChannelEnum.EMAIL,
        options: [
          {
            value: ExternalCommunicationChannelEnum.EMAIL,
            label: t('Email'),
          },
          {
            value: ExternalCommunicationChannelEnum.SMS,
            label: t('SMS'),
            disabled: !userInfo.phoneNumber,
          },
        ],
      },
      {
        name: 'notificationSetting',
        type: 'radio-select',
        label: t('Message notifications'),
        defaultValue:
          notificationSetting?.setting ||
          MessageNotificationSettingEnum.NEW_CONVERSATION,
        options: [
          {
            value: MessageNotificationSettingEnum.EVERY_MESSAGE,
            label: t('All messages'),
          },
          {
            value: MessageNotificationSettingEnum.NEW_CONVERSATION,
            label: t('New conversations'),
          },
          {
            value: MessageNotificationSettingEnum.NEVER,
            label: t('Never'),
          },
        ],
      },
    ],
    [
      t,
      userInfo.phoneNumber,
      notificationSetting?.communicationChannel,
      notificationSetting?.setting,
    ],
  );

  return (
    <div className="flex items-start m-5 justify-center flex-col gap-4">
      <h3 className="text-h3"> {t('Notifications')} </h3>

      <FormFromConfig
        method="POST"
        action="/settings/external-notifications"
        fields={fields}
        createValidator={upsertExternalNotificationsValidator}
        renderFields={({ fields, submitButton }) => (
          <div className="flex flex-col gap-4">
            <div className="rounded-lg shadow-lg p-4 w-fit-content">
              {fields.communicationChannel.node}
            </div>
            <div className="rounded-lg shadow-lg p-4 w-fit-content">
              {fields.notificationSetting.node}
            </div>
            {submitButton}
          </div>
        )}
      />
    </div>
  );
}
