{"name": "filipina-meet-mobile", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@react-navigation/native": "^6.0.2", "expo": "^45.0.8", "expo-font": "~12.0.9", "expo-linking": "~6.3.1", "expo-router": "~3.5.23", "expo-secure-store": "~13.0.2", "expo-splash-screen": "~0.27.5", "expo-status-bar": "~1.12.1", "expo-system-ui": "~3.0.7", "expo-web-browser": "~13.0.3", "filipina-meet-mobile": "file:", "intl-pluralrules": "^2.0.1", "react-native": "0.75.3", "react-native-paper": "^5.12.5", "react-native-reanimated": "~3.10.1", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-web": "~0.19.10"}, "devDependencies": {"@babel/core": "^7.20.0", "babel-plugin-module-resolver": "^5.0.2", "jest": "^29.2.1", "jest-expo": "~51.0.3", "typescript": "~5.3.3"}, "private": true}