import { useTranslation } from 'react-i18next';
import Typography from '#app/components/typography';
import { cn } from '#app/utils/misc';

interface TaglineProps {
  isInternational: boolean;
}

const Tagline: React.FC<TaglineProps> = ({ isInternational }) => {
  const { t } = useTranslation();
  const customFontSize =
    'text-[1.3rem] sm:text-[2rem] md:text-[2rem] lg:text-[3.375rem] ';

  return (
    <div className="w-full bg-gradient-to-r from-brand-secondary via-brand-primary to-brand-tertiary">
      <Typography
        variant="h1"
        className={cn(
          customFontSize,
          'font-xl text-center lg:text-left px-4 py-3 tablet-min:px-16 tablet-min:py-6 text-white',
        )}
      >
        {t('Meet your perfect')}{' '}
        <Typography
          className={cn('font-libre-bodoni italic font-normal', customFontSize)}
          variant="h1"
          as={'span'}
        >
          {!isInternational ? t('International') : t('Filipina')}
        </Typography>{' '}
        {t('match')}
      </Typography>
    </div>
  );
};

export { Tagline };
