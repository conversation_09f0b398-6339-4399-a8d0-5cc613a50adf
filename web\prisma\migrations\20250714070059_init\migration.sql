-- CreateEnum
CREATE TYPE "GenderEnum" AS ENUM ('MALE', 'FEMALE', 'TRANSGENDER_MALE', 'TRANSGENDER_FEMALE', 'NON_BINARY');

-- CreateEnum
CREATE TYPE "PhotoType" AS ENUM ('MAIN', 'VERIFICATION', 'GALLERY');

-- CreateEnum
CREATE TYPE "VideoType" AS ENUM ('MAIN', 'GALLERY', 'STORY');

-- CreateEnum
CREATE TYPE "VerificationStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED');

-- CreateEnum
CREATE TYPE "HairColorEnum" AS ENUM ('BA<PERSON>_SHAVEN', 'BLACK', 'BLONDE', 'BROWN', 'GREY_WHITE', 'LIGHT_BROWN', 'RED', 'CHANGES_FREQUENTLY');

-- C<PERSON><PERSON>num
CREATE TYPE "EyeColorEnum" AS ENUM ('BLACK', 'BLUE', 'BROWN', 'GRE<PERSON>', 'GREY', '<PERSON><PERSON><PERSON><PERSON>');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "BodyTypeEnum" AS ENUM ('PETITE', 'SLIM', 'ATHLETIC', 'AVERAGE', 'FEW_EXTRA_POUNDS', 'FULL_FIGURED', 'LARGE');

-- CreateEnum
CREATE TYPE "EthnicityEnum" AS ENUM ('MIDDLE_EASTERN', 'EAST_ASIAN', 'SOUTH_ASIAN', 'SOUTHEAST_ASIAN', 'FILIPINO', 'BLACK', 'WHITE', 'HISPANIC', 'PACIFIC_ISLANDER', 'OTHER');

-- CreateEnum
CREATE TYPE "NeverSometimesOften" AS ENUM ('NEVER', 'SOMETIMES', 'OFTEN');

-- CreateEnum
CREATE TYPE "YesNo" AS ENUM ('YES', 'NO');

-- CreateEnum
CREATE TYPE "YesNoNotSure" AS ENUM ('YES', 'NO', 'NOT_SURE');

-- CreateEnum
CREATE TYPE "ReligionEnum" AS ENUM ('CHRISTIAN', 'MUSLIM', 'HINDU', 'BUDDHIST', 'JEWISH', 'SPIRITUAL', 'AGNOSTIC', 'ATHEIST', 'OTHER');

-- CreateEnum
CREATE TYPE "RelationshipEnum" AS ENUM ('MARRIAGE', 'SERIOUS_RELATIONSHIP', 'OPEN_RELATIONSHIP', 'FRIENDSHIP_OR_COMPANIONSHIP', 'CASUAL', 'UNSURE');

-- CreateEnum
CREATE TYPE "SocialProvider" AS ENUM ('FACEBOOK', 'GOOGLE');

-- CreateEnum
CREATE TYPE "UserType" AS ENUM ('USER', 'ADMIN');

-- CreateEnum
CREATE TYPE "MessageNotificationSettingEnum" AS ENUM ('EVERY_MESSAGE', 'NEW_CONVERSATION', 'NEVER');

-- CreateEnum
CREATE TYPE "ExternalCommunicationChannelEnum" AS ENUM ('EMAIL', 'SMS', 'FACEBOOK');

-- CreateEnum
CREATE TYPE "Provider" AS ENUM ('CLOUDINARY', 'CLOUDFLARE_R2');

-- CreateEnum
CREATE TYPE "VariantType" AS ENUM ('SMALL', 'MEDIUM', 'LARGE');

-- CreateEnum
CREATE TYPE "LocationType" AS ENUM ('COUNTRY', 'REGION', 'CITY_OR_TOWN');

-- CreateEnum
CREATE TYPE "SpecialLocationSearchPreference" AS ENUM ('WORLDWIDE', 'WESTERN_COUNTRIES', 'ASIAN_COUNTRIES');

-- CreateTable
CREATE TABLE "PrivateUser" (
    "id" TEXT NOT NULL,
    "email" TEXT,
    "phoneNumber" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "uniqueId" TEXT NOT NULL,
    "dateOfBirth" TIMESTAMP(3),
    "locale" TEXT,

    CONSTRAINT "PrivateUser_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MessageNotificationSetting" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "communicationChannel" "ExternalCommunicationChannelEnum" NOT NULL,
    "setting" "MessageNotificationSettingEnum" NOT NULL,
    "privateUserId" TEXT NOT NULL,

    CONSTRAINT "MessageNotificationSetting_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userType" "UserType" NOT NULL DEFAULT 'USER',
    "verificationStatus" "VerificationStatus" NOT NULL DEFAULT 'PENDING',
    "verifiedAt" TIMESTAMP(3),
    "privateUserId" TEXT NOT NULL,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Password" (
    "hash" TEXT NOT NULL,
    "privateUserId" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "Session" (
    "id" TEXT NOT NULL,
    "expirationDate" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "privateUserId" TEXT NOT NULL,

    CONSTRAINT "Session_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Profile" (
    "id" TEXT NOT NULL,
    "username" TEXT NOT NULL,
    "firstName" TEXT NOT NULL,
    "gender" "GenderEnum" NOT NULL,
    "genderInterest" "GenderEnum"[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT NOT NULL,
    "completedProfileSetup" BOOLEAN,

    CONSTRAINT "Profile_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SocialConnection" (
    "id" TEXT NOT NULL,
    "providerName" "SocialProvider" NOT NULL,
    "providerId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "privateUserId" TEXT,
    "firstName" TEXT,

    CONSTRAINT "SocialConnection_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OAuthToken" (
    "id" TEXT NOT NULL,
    "accessToken" TEXT NOT NULL,
    "refreshToken" TEXT,
    "scope" TEXT NOT NULL,
    "expiryDate" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "socialConnectionId" TEXT NOT NULL,

    CONSTRAINT "OAuthToken_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FacebookUserOptIn" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "notificationMessageToken" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "lastMessageSentAt" TIMESTAMP(3),
    "messageNotificationSettingId" TEXT NOT NULL,
    "socialConnectionId" TEXT NOT NULL,

    CONSTRAINT "FacebookUserOptIn_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ParentPhoto" (
    "id" TEXT NOT NULL,
    "caption" TEXT,
    "provider" "Provider" NOT NULL,
    "fileId" TEXT NOT NULL,
    "format" TEXT NOT NULL,
    "width" INTEGER NOT NULL,
    "height" INTEGER NOT NULL,
    "photoType" "PhotoType" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "verifiedAt" TIMESTAMP(3),
    "verificationStatus" "VerificationStatus" NOT NULL DEFAULT 'PENDING',
    "profileId" TEXT NOT NULL,

    CONSTRAINT "ParentPhoto_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RawPhoto" (
    "id" TEXT NOT NULL,
    "fileId" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "format" TEXT NOT NULL,
    "width" INTEGER NOT NULL,
    "height" INTEGER NOT NULL,
    "fileSize" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "verifiedAt" TIMESTAMP(3),
    "parentPhotoId" TEXT NOT NULL,

    CONSTRAINT "RawPhoto_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PhotoVariant" (
    "id" TEXT NOT NULL,
    "variant" "VariantType" NOT NULL,
    "provider" "Provider" NOT NULL,
    "key" TEXT NOT NULL,
    "format" TEXT NOT NULL,
    "width" INTEGER NOT NULL,
    "height" INTEGER NOT NULL,
    "fileSize" INTEGER NOT NULL,
    "profileId" TEXT NOT NULL,
    "parentPhotoId" TEXT NOT NULL,

    CONSTRAINT "PhotoVariant_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Video" (
    "id" TEXT NOT NULL,
    "fileId" TEXT NOT NULL,
    "caption" TEXT,
    "videoType" "VideoType" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "profileId" TEXT NOT NULL,
    "verificationStatus" "VerificationStatus" NOT NULL DEFAULT 'PENDING',

    CONSTRAINT "Video_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Location" (
    "id" TEXT NOT NULL,
    "latitude" DOUBLE PRECISION NOT NULL,
    "longitude" DOUBLE PRECISION NOT NULL,
    "cityOrTown" TEXT NOT NULL,
    "regionCode" TEXT NOT NULL,
    "countryIso2" TEXT NOT NULL,
    "openToRelocation" BOOLEAN,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "profileId" TEXT NOT NULL,

    CONSTRAINT "Location_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Appearance" (
    "id" TEXT NOT NULL,
    "hairColor" "HairColorEnum",
    "eyeColor" "EyeColorEnum",
    "height" DECIMAL(65,30),
    "weight" DECIMAL(65,30),
    "bodyType" "BodyTypeEnum",
    "ethnicity" "EthnicityEnum"[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "profileId" TEXT NOT NULL,

    CONSTRAINT "Appearance_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Lifestyle" (
    "id" TEXT NOT NULL,
    "drinking" "NeverSometimesOften",
    "smoking" "NeverSometimesOften",
    "haveChildren" "YesNo",
    "wantChildren" "YesNoNotSure",
    "religion" "ReligionEnum",
    "interests" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "profileId" TEXT NOT NULL,

    CONSTRAINT "Lifestyle_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Bio" (
    "id" TEXT NOT NULL,
    "profileId" TEXT NOT NULL,
    "tagline" TEXT,
    "aboutMe" TEXT,
    "occupation" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Bio_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DatingPreference" (
    "id" TEXT NOT NULL,
    "lookingFor" TEXT,
    "relationshipPreference" "RelationshipEnum"[],
    "drinking" "NeverSometimesOften"[],
    "smoking" "NeverSometimesOften"[],
    "bodyTypes" "BodyTypeEnum"[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "profileId" TEXT NOT NULL,
    "minAge" INTEGER NOT NULL DEFAULT 18,
    "maxAge" INTEGER NOT NULL DEFAULT 120,

    CONSTRAINT "DatingPreference_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Conversation" (
    "id" TEXT NOT NULL,
    "subject" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "initiatorProfileId" TEXT NOT NULL,
    "receiverProfileId" TEXT NOT NULL,

    CONSTRAINT "Conversation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Message" (
    "id" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "readAt" TIMESTAMP(3),
    "conversationId" TEXT NOT NULL,
    "senderProfileId" TEXT NOT NULL,

    CONSTRAINT "Message_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProfileLike" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "likerProfileId" TEXT NOT NULL,
    "likedProfileId" TEXT NOT NULL,

    CONSTRAINT "ProfileLike_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VideoLike" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "videoLikerProfileId" TEXT NOT NULL,
    "likedVideoId" TEXT NOT NULL,

    CONSTRAINT "VideoLike_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProfileView" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "lastViewedTime" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "viewerProfileId" TEXT NOT NULL,
    "viewedProfileId" TEXT NOT NULL,

    CONSTRAINT "ProfileView_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProfileBlock" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "blockerProfileId" TEXT NOT NULL,
    "blockedProfileId" TEXT NOT NULL,

    CONSTRAINT "ProfileBlock_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProfileLikeNotification" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "profileLikeId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "readAt" TIMESTAMP(3),

    CONSTRAINT "ProfileLikeNotification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MessageNotification" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "readAt" TIMESTAMP(3),
    "messageId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,

    CONSTRAINT "MessageNotification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProfileViewNotification" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "profileViewId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "readAt" TIMESTAMP(3),

    CONSTRAINT "ProfileViewNotification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MessageAttachment" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "messageId" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "type" TEXT NOT NULL,

    CONSTRAINT "MessageAttachment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LocationPreference" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "profileId" TEXT NOT NULL,
    "specialLocationSearchPreferences" "SpecialLocationSearchPreference"[],

    CONSTRAINT "LocationPreference_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LocationSelection" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "locationPreferenceId" TEXT NOT NULL,
    "locationType" "LocationType" NOT NULL,
    "countryIso2" TEXT NOT NULL,
    "regionCode" TEXT,
    "cityOrTown" TEXT,

    CONSTRAINT "LocationSelection_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_Conversations" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_Conversations_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE UNIQUE INDEX "PrivateUser_uniqueId_key" ON "PrivateUser"("uniqueId");

-- CreateIndex
CREATE UNIQUE INDEX "MessageNotificationSetting_privateUserId_communicationChann_key" ON "MessageNotificationSetting"("privateUserId", "communicationChannel");

-- CreateIndex
CREATE UNIQUE INDEX "User_privateUserId_key" ON "User"("privateUserId");

-- CreateIndex
CREATE UNIQUE INDEX "Password_privateUserId_key" ON "Password"("privateUserId");

-- CreateIndex
CREATE INDEX "Session_privateUserId_idx" ON "Session"("privateUserId");

-- CreateIndex
CREATE UNIQUE INDEX "Profile_username_key" ON "Profile"("username");

-- CreateIndex
CREATE UNIQUE INDEX "Profile_userId_key" ON "Profile"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "SocialConnection_providerName_providerId_key" ON "SocialConnection"("providerName", "providerId");

-- CreateIndex
CREATE UNIQUE INDEX "OAuthToken_socialConnectionId_key" ON "OAuthToken"("socialConnectionId");

-- CreateIndex
CREATE UNIQUE INDEX "FacebookUserOptIn_messageNotificationSettingId_key" ON "FacebookUserOptIn"("messageNotificationSettingId");

-- CreateIndex
CREATE UNIQUE INDEX "FacebookUserOptIn_socialConnectionId_key" ON "FacebookUserOptIn"("socialConnectionId");

-- CreateIndex
CREATE UNIQUE INDEX "ParentPhoto_fileId_key" ON "ParentPhoto"("fileId");

-- CreateIndex
CREATE UNIQUE INDEX "RawPhoto_fileId_key" ON "RawPhoto"("fileId");

-- CreateIndex
CREATE UNIQUE INDEX "RawPhoto_parentPhotoId_key" ON "RawPhoto"("parentPhotoId");

-- CreateIndex
CREATE UNIQUE INDEX "Video_fileId_key" ON "Video"("fileId");

-- CreateIndex
CREATE INDEX "Video_profileId_idx" ON "Video"("profileId");

-- CreateIndex
CREATE INDEX "Video_fileId_idx" ON "Video"("fileId");

-- CreateIndex
CREATE UNIQUE INDEX "Location_profileId_key" ON "Location"("profileId");

-- CreateIndex
CREATE INDEX "Location_countryIso2_idx" ON "Location"("countryIso2");

-- CreateIndex
CREATE INDEX "Location_regionCode_idx" ON "Location"("regionCode");

-- CreateIndex
CREATE INDEX "Location_cityOrTown_idx" ON "Location"("cityOrTown");

-- CreateIndex
CREATE UNIQUE INDEX "Appearance_profileId_key" ON "Appearance"("profileId");

-- CreateIndex
CREATE UNIQUE INDEX "Lifestyle_profileId_key" ON "Lifestyle"("profileId");

-- CreateIndex
CREATE UNIQUE INDEX "Bio_profileId_key" ON "Bio"("profileId");

-- CreateIndex
CREATE UNIQUE INDEX "DatingPreference_profileId_key" ON "DatingPreference"("profileId");

-- CreateIndex
CREATE UNIQUE INDEX "Conversation_initiatorProfileId_receiverProfileId_key" ON "Conversation"("initiatorProfileId", "receiverProfileId");

-- CreateIndex
CREATE UNIQUE INDEX "ProfileLike_likerProfileId_likedProfileId_key" ON "ProfileLike"("likerProfileId", "likedProfileId");

-- CreateIndex
CREATE UNIQUE INDEX "VideoLike_videoLikerProfileId_likedVideoId_key" ON "VideoLike"("videoLikerProfileId", "likedVideoId");

-- CreateIndex
CREATE UNIQUE INDEX "ProfileView_viewerProfileId_viewedProfileId_key" ON "ProfileView"("viewerProfileId", "viewedProfileId");

-- CreateIndex
CREATE UNIQUE INDEX "ProfileBlock_blockerProfileId_blockedProfileId_key" ON "ProfileBlock"("blockerProfileId", "blockedProfileId");

-- CreateIndex
CREATE UNIQUE INDEX "ProfileLikeNotification_profileLikeId_userId_key" ON "ProfileLikeNotification"("profileLikeId", "userId");

-- CreateIndex
CREATE UNIQUE INDEX "MessageNotification_messageId_userId_key" ON "MessageNotification"("messageId", "userId");

-- CreateIndex
CREATE UNIQUE INDEX "ProfileViewNotification_profileViewId_userId_key" ON "ProfileViewNotification"("profileViewId", "userId");

-- CreateIndex
CREATE UNIQUE INDEX "LocationPreference_profileId_key" ON "LocationPreference"("profileId");

-- CreateIndex
CREATE UNIQUE INDEX "LocationSelection_locationPreferenceId_countryIso2_regionCo_key" ON "LocationSelection"("locationPreferenceId", "countryIso2", "regionCode", "cityOrTown");

-- CreateIndex
CREATE INDEX "_Conversations_B_index" ON "_Conversations"("B");

-- AddForeignKey
ALTER TABLE "MessageNotificationSetting" ADD CONSTRAINT "MessageNotificationSetting_privateUserId_fkey" FOREIGN KEY ("privateUserId") REFERENCES "PrivateUser"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_privateUserId_fkey" FOREIGN KEY ("privateUserId") REFERENCES "PrivateUser"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Password" ADD CONSTRAINT "Password_privateUserId_fkey" FOREIGN KEY ("privateUserId") REFERENCES "PrivateUser"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Session" ADD CONSTRAINT "Session_privateUserId_fkey" FOREIGN KEY ("privateUserId") REFERENCES "PrivateUser"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Profile" ADD CONSTRAINT "Profile_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SocialConnection" ADD CONSTRAINT "SocialConnection_privateUserId_fkey" FOREIGN KEY ("privateUserId") REFERENCES "PrivateUser"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OAuthToken" ADD CONSTRAINT "OAuthToken_socialConnectionId_fkey" FOREIGN KEY ("socialConnectionId") REFERENCES "SocialConnection"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FacebookUserOptIn" ADD CONSTRAINT "FacebookUserOptIn_messageNotificationSettingId_fkey" FOREIGN KEY ("messageNotificationSettingId") REFERENCES "MessageNotificationSetting"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FacebookUserOptIn" ADD CONSTRAINT "FacebookUserOptIn_socialConnectionId_fkey" FOREIGN KEY ("socialConnectionId") REFERENCES "SocialConnection"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ParentPhoto" ADD CONSTRAINT "ParentPhoto_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RawPhoto" ADD CONSTRAINT "RawPhoto_parentPhotoId_fkey" FOREIGN KEY ("parentPhotoId") REFERENCES "ParentPhoto"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PhotoVariant" ADD CONSTRAINT "PhotoVariant_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PhotoVariant" ADD CONSTRAINT "PhotoVariant_parentPhotoId_fkey" FOREIGN KEY ("parentPhotoId") REFERENCES "ParentPhoto"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Video" ADD CONSTRAINT "Video_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Location" ADD CONSTRAINT "Location_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Appearance" ADD CONSTRAINT "Appearance_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Lifestyle" ADD CONSTRAINT "Lifestyle_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Bio" ADD CONSTRAINT "Bio_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DatingPreference" ADD CONSTRAINT "DatingPreference_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Conversation" ADD CONSTRAINT "Conversation_initiatorProfileId_fkey" FOREIGN KEY ("initiatorProfileId") REFERENCES "Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Conversation" ADD CONSTRAINT "Conversation_receiverProfileId_fkey" FOREIGN KEY ("receiverProfileId") REFERENCES "Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Message" ADD CONSTRAINT "Message_conversationId_fkey" FOREIGN KEY ("conversationId") REFERENCES "Conversation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Message" ADD CONSTRAINT "Message_senderProfileId_fkey" FOREIGN KEY ("senderProfileId") REFERENCES "Profile"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProfileLike" ADD CONSTRAINT "ProfileLike_likerProfileId_fkey" FOREIGN KEY ("likerProfileId") REFERENCES "Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProfileLike" ADD CONSTRAINT "ProfileLike_likedProfileId_fkey" FOREIGN KEY ("likedProfileId") REFERENCES "Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "VideoLike" ADD CONSTRAINT "VideoLike_videoLikerProfileId_fkey" FOREIGN KEY ("videoLikerProfileId") REFERENCES "Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "VideoLike" ADD CONSTRAINT "VideoLike_likedVideoId_fkey" FOREIGN KEY ("likedVideoId") REFERENCES "Video"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProfileView" ADD CONSTRAINT "ProfileView_viewerProfileId_fkey" FOREIGN KEY ("viewerProfileId") REFERENCES "Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProfileView" ADD CONSTRAINT "ProfileView_viewedProfileId_fkey" FOREIGN KEY ("viewedProfileId") REFERENCES "Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProfileBlock" ADD CONSTRAINT "ProfileBlock_blockerProfileId_fkey" FOREIGN KEY ("blockerProfileId") REFERENCES "Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProfileBlock" ADD CONSTRAINT "ProfileBlock_blockedProfileId_fkey" FOREIGN KEY ("blockedProfileId") REFERENCES "Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProfileLikeNotification" ADD CONSTRAINT "ProfileLikeNotification_profileLikeId_fkey" FOREIGN KEY ("profileLikeId") REFERENCES "ProfileLike"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProfileLikeNotification" ADD CONSTRAINT "ProfileLikeNotification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MessageNotification" ADD CONSTRAINT "MessageNotification_messageId_fkey" FOREIGN KEY ("messageId") REFERENCES "Message"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MessageNotification" ADD CONSTRAINT "MessageNotification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProfileViewNotification" ADD CONSTRAINT "ProfileViewNotification_profileViewId_fkey" FOREIGN KEY ("profileViewId") REFERENCES "ProfileView"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProfileViewNotification" ADD CONSTRAINT "ProfileViewNotification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MessageAttachment" ADD CONSTRAINT "MessageAttachment_messageId_fkey" FOREIGN KEY ("messageId") REFERENCES "Message"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LocationPreference" ADD CONSTRAINT "LocationPreference_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LocationSelection" ADD CONSTRAINT "LocationSelection_locationPreferenceId_fkey" FOREIGN KEY ("locationPreferenceId") REFERENCES "LocationPreference"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_Conversations" ADD CONSTRAINT "_Conversations_A_fkey" FOREIGN KEY ("A") REFERENCES "Conversation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_Conversations" ADD CONSTRAINT "_Conversations_B_fkey" FOREIGN KEY ("B") REFERENCES "Profile"("id") ON DELETE CASCADE ON UPDATE CASCADE;
