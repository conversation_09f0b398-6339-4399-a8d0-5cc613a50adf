import { type FastifyInstance } from 'fastify';
import { BadRequestError } from '#server/utils/errors.server';
import { handleFastifyRoute } from '#server/utils/fastify-helpers.server';
import { handleProfileView } from '#server/utils/profile.server';

export async function profileViewRouter(fastify: FastifyInstance) {
  fastify.post(
    '/:profileId/view',
    handleFastifyRoute(async ({ request, sendSuccessPayload }) => {
      const { profileId } = request.params as { profileId: string };
      const { viewerProfileId, viewedUserId } = request.body as {
        viewerProfileId: string;
        viewedUserId: string;
      };

      if (!profileId || !viewerProfileId) {
        throw new BadRequestError(
          'Invalid profile or viewer information provided.',
        );
      }

      // Log the profile view
      await handleProfileView({
        viewerProfileId,
        viewedUserId,
        viewedProfileId: profileId,
      });

      // Send a success response indicating the view was logged
      sendSuccessPayload({ success: true });
    }),
  );
}
