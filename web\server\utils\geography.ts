import countriesData from '#data/geography/countries.json' with { type: 'json' };
import countriesWithRegionsData from '#data/geography/countriesWithRegions.json' with { type: 'json' };

import {
  type JsonCountry,
  type JsonCountryWithRegions,
  type ApiRegion,
  type GoogleGeocode,
} from '#shared/types/geography';

const PRIORITY_MAP: Record<string, number> = {
  PH: 3,
  US: 2,
  CA: 1,
  GB: 1,
  AU: 1,
  DE: 1,
  FR: 1,
  IT: 1,
  ES: 1,
};

export function getCountries() {
  const data: JsonCountry[] = countriesData.map((country: any) => ({
    name: country.name,
    iso2: country.iso2,
  }));
  return data;
}

export function getRegionsOfCountry(iso2: string): ApiRegion[] {
  const data = countriesWithRegionsData
    .filter(
      (countryWithRegion: JsonCountryWithRegions) =>
        countryWithRegion.iso2 === iso2,
    )
    .flatMap((countryWithRegion: JsonCountryWithRegions) => {
      const regions = countryWithRegion.states.map(
        (state: { name: string; state_code: string }) => ({
          name: state.name,
          regionCode: state.state_code,
          countryIso2: countryWithRegion.iso2,
        }),
      );
      if (!regions.length) {
        // hack for countries without regions
        return [
          {
            name: countryWithRegion.name,
            regionCode: countryWithRegion.iso2,
            countryIso2: countryWithRegion.iso2,
          },
        ];
      }
      return regions;
    });
  return data;
}

export function getLocationInfo(countryIso2: string, regionCode: string) {
  const country = countriesData.find(
    (country: JsonCountry) => country.iso2 === countryIso2,
  );
  const regions = getRegionsOfCountry(countryIso2);
  const region = regions.find((region) => region.regionCode === regionCode);
  return { country, region };
}

export function isValidIso2(iso2: string) {
  return countriesData.some((country: any) => country.iso2 === iso2);
}

export function sortCountriesByPriority(countries: JsonCountry[]) {
  // may be confusing if people miss the fact that the priority is only for the first few countries
  return countries.sort((a, b) => {
    const aPriority = PRIORITY_MAP[a.iso2] ?? 0;
    const bPriority = PRIORITY_MAP[b.iso2] ?? 0;
    if (aPriority !== bPriority) {
      return bPriority - aPriority;
    }
    return a.name.localeCompare(b.name);
  });
}

// google stuff
export async function getGeocodeData(address: string) {
  const response = await fetch(
    `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(
      address,
    )}&key=${process.env.GOOGLE_MAPS_API_KEY}`,
  );
  return (await response.json()) as GoogleGeocode;
}
