import {
  type Appearance,
  type Bio,
  type DatingPreference,
  type Lifestyle,
  type Location,
  type Profile,
  type ParentPhoto,
  type Video,
  type ProfileBlock,
} from '@prisma/client';
import { type getConversationWithMessages } from '#app/utils/conversations.server';
import { type performSearch } from '#app/utils/search.server';
import { type performStoriesSearch } from '#app/utils/stories.server';
import { type getBasicInfoFromRequest } from '#server/utils/auth.server';
import {
  type attachVideoUrlFromVideo,
  type attachImageUrlsToPhoto,
} from '#server/utils/media.server';
import { type OnlineStatusWithTime } from '#shared/types/profile';

type BasicInfoReturnType = Awaited<
  ReturnType<typeof getBasicInfoFromRequest<true, true>>
>;

export type RootUser = Exclude<BasicInfoReturnType['user'], undefined>;

export type RootProfile = Exclude<BasicInfoReturnType['profile'], undefined>;

export type SerializedAppearance = Omit<Appearance, 'height' | 'weight'> & {
  height: number | null;
  weight: number | null;
};

// profile fields
export interface SerializedFullProfile extends Profile {
  emailIsVerified: boolean;
  hasVerifiedPhoto: boolean;
  onlineStatus: OnlineStatusWithTime;
  datingPreference: DatingPreference | null;
  lifestyle: Lifestyle | null;
  appearance: SerializedAppearance | null;
  bio: Bio | null;
  location: Location | null;
  galleryPhotos: PhotoWithSignedUrl[];
  galleryVideos: SerializedVideo[];
  mainPhoto?: PhotoWithSignedUrl;
  age: number | null;
  likesThisProfile: boolean;
  isBlocked: boolean;
}
export type SearchProfile = Awaited<
  ReturnType<typeof performSearch>
>['data'][number] & {
  onlineStatus: OnlineStatusWithTime;
  likesThisProfile: boolean;
};

// messages/conversations
export type ConversationWithMessages = Awaited<
  ReturnType<typeof getConversationWithMessages>
>;

export type Stories = Awaited<ReturnType<typeof performStoriesSearch>>[number];

export type VerificationPhoto = ReturnType<
    typeof attachImageUrlsToPhoto
  >;

export type PhotoWithSignedUrl = ParentPhoto &
  ReturnType<typeof attachImageUrlsToPhoto>;

export type SerializedVideo = Video &
  ReturnType<typeof attachVideoUrlFromVideo>;

export type EnrichedProfileBlock = Omit<
  ProfileBlock,
  'createdAt' | 'updatedAt' | 'blockedProfile'
> & {
  blockedProfile: SerializedFullProfile;
};
