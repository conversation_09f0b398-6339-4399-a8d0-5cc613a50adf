import { IdentificationIcon } from '@heroicons/react/20/solid';
import { Link, useLocation, useRouteLoaderData } from '@remix-run/react';
import { Images, LeafIcon, Stars, Users } from 'lucide-react';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '#app/components/ui/button';
import { Icon } from '#app/components/ui/icon';
import { type RootProfile, type RootUser } from '#app/types/serialized-types';
import { cn } from '#app/utils/misc';
import { type ProfileProgress as ProfileProgressType } from '#server/utils/profile.server';
import {
  PROFILE_EDIT_APPEARANCE_ROUTE,
  PROFILE_EDIT_BASIC_INFO_ROUTE,
  PROFILE_EDIT_DATING_PREFERENCE_ROUTE,
  PROFILE_EDIT_LIFESTYLE_ROUTE,
  PROFILE_EDIT_PHOTOS_ROUTE,
} from '#shared/constants/routes';
import { type PhotoWithUrls } from '#shared/types/media.js';

const NavItem = ({
  route,
  title,
  icon,
}: {
  route: string;
  title: string;
  icon?: React.ReactNode;
}) => {
  const location = useLocation();
  return (
    <div className="relative flex w-full items-center ">
      {location.pathname === route && (
        <div className="absolute -right-8 h-full w-1.5 rounded-bl-lg rounded-tl-lg bg-brand-primary tablet:hidden" />
      )}
      <Link
        to={route}
        className={cn(
          'relative z-10 flex w-full items-center gap-2 py-2 tablet:whitespace-nowrap',
          location.pathname === route && 'text-brand-primary',
        )}
      >
        <div className="tablet:hidden">{icon}</div>
        {title}
      </Link>
    </div>
  );
};

const ProgressBanner = ({
  progress,
  mainPhoto,
  className,
  profile,
}: {
  progress: ProfileProgressType;
  mainPhoto?: PhotoWithUrls;
  className?: string;
  profile: RootProfile;
}) => {
  const { t } = useTranslation();

  const uncompletedStepMap: Record<
    string,
    { text: string; to: string; partialText?: string }
  > = {
    mainPhoto: {
      text: t('Add a profile picture'),
      to: PROFILE_EDIT_PHOTOS_ROUTE,
    },
    galleryPhotos: {
      text: t('Add a gallery photo'),
      to: PROFILE_EDIT_PHOTOS_ROUTE,
    },
    appearance: {
      text: t('Add some appearance info'),
      to: PROFILE_EDIT_APPEARANCE_ROUTE,
      partialText: t('Complete appearance info'),
    },
    lifestyle: {
      text: t('Add lifestyle info'),
      to: PROFILE_EDIT_LIFESTYLE_ROUTE,
      partialText: t('Complete lifestyle info'),
    },
    datingPreference: {
      text: t('Add dating preferences'),
      to: PROFILE_EDIT_DATING_PREFERENCE_ROUTE,
    },
  };

  const nextStep = progress.uncompletedSteps[0];
  const nextStepDetails = nextStep ? uncompletedStepMap[nextStep] : null;
  const isPartial = nextStep ? progress.isPartial[nextStep] : false;

  return (
    progress.progress !== 100 && (
      <div
        className={cn(
          'rounded-3xl bg-gradient-to-l from-[#D33544] to-[#2C60AA] p-8 shadow-sm',
          className,
        )}
      >
        <div className="flex items-center gap-4">
          <div className="h-12 w-12 overflow-hidden rounded-full bg-white">
            {mainPhoto?.mediumUrl ? (
              <img
                src={mainPhoto.mediumUrl}
                alt=""
                className="h-full w-full object-cover"
              />
            ) : (
              <span className="flex h-full w-full items-center justify-center bg-gray-200 text-lg font-bold text-brand-primary">
                {profile.firstName[0]}
              </span>
            )}
          </div>
          <div className="flex flex-col flex-1">
            <div className="flex flex-col">
              <h3 className="text-lg font-semibold leading-none text-white">
                {t('Profile progress')}
              </h3>
              <p className="text-sm text-white">
                {t('Complete your profile to stand out')}
              </p>
            </div>
            <ProfileProgress progress={progress.progress} />
            {nextStepDetails && (
              <Link to={nextStepDetails.to} className="text-white underline">
                {isPartial ? nextStepDetails.partialText : nextStepDetails.text}
              </Link>
            )}
          </div>
        </div>
      </div>
    )
  );
};

const ProfileProgress = ({ progress }: { progress: number }) => {
  return (
    <div className="flex items-center gap-2">
      <div className="h-2 w-full max-w-[24rem] overflow-hidden rounded-full bg-white">
        <div
          className="h-full bg-green-300 transition-all"
          style={{ width: `${progress}%` }}
        />
      </div>
      <span className="text-sm text-white">{progress}%</span>
    </div>
  );
};

interface ProfileLayoutProps {
  children: React.ReactNode;
  renderSideActions?: () => React.ReactNode;
  progress?: ProfileProgressType;
}

const ProfileLayout: React.FC<ProfileLayoutProps> = ({
  children,
  renderSideActions,
  progress,
}) => {
  const { t } = useTranslation();
  const data = useRouteLoaderData<{
    user?: RootUser;
    profile?: RootProfile;
  }>('root');

  const routes = [
    {
      icon: <IdentificationIcon className="h-4 w-4" />,
      route: PROFILE_EDIT_BASIC_INFO_ROUTE,
      title: t('Basic Information'),
    },
    {
      icon: <Images className="h-4 w-4" />,
      route: PROFILE_EDIT_PHOTOS_ROUTE,
      title: t('Photos'),
    },
    {
      icon: <Stars className="h-4 w-4" />,
      route: PROFILE_EDIT_APPEARANCE_ROUTE,
      title: t('Appearance'),
    },
    {
      icon: <LeafIcon className="h-4 w-4" />,
      route: PROFILE_EDIT_LIFESTYLE_ROUTE,
      title: t('Lifestyle'),
    },
    {
      icon: <Users className="h-4 w-4" />,
      route: PROFILE_EDIT_DATING_PREFERENCE_ROUTE,
      title: t('Dating Preferences'),
    },
  ];

  return (
    <div className="flex tablet:flex-col gap-8 py-4 px-8">
      <div className="relative flex items-center justify-center desktop-min:hidden ">
        <Button variant={'ghost'} className="absolute -left-4">
          <Icon name="arrow-left" className="h-5 w-5" />
        </Button>
        <h2 className="text-2xl font-semibold">{t('Edit Profile')}</h2>
      </div>
      {progress && data?.profile && (
        <ProgressBanner
          progress={progress}
          mainPhoto={data?.profile?.mainPhoto}
          profile={data.profile}
          className="desktop-min:hidden"
        />
      )}
      <nav className="w-[300px] tablet:w-full rounded-3xl bg-card tablet:bg-transparent shadow-sm tablet:shadow-none tablet:overflow-auto flex flex-col gap-2 tablet:gap-8 py-8 px-8 tablet:px-8 tablet:flex-row tablet:scroll-smooth tablet:no-scrollbar tablet:-my-12">
        {routes.map(({ icon, route, title }) => (
          <NavItem key={route} route={route} title={title} icon={icon} />
        ))}
      </nav>
      <div className="flex-1">
        <div className="rounded-3xl bg-card px-14 tablet:px-8 tablet:py-2 py-8 shadow-sm">
          <div className="flex flex-col gap-6">
            <div className="flex flex-col gap-6">
              <div className="flex items-center justify-between tablet:hidden">
                <h2 className="text-2xl font-semibold">{t('Edit Profile')}</h2>
                {renderSideActions && renderSideActions()}
              </div>
              {progress && data?.profile && (
                <ProgressBanner
                  progress={progress}
                  mainPhoto={data?.profile?.mainPhoto}
                  profile={data.profile}
                  className="tablet:hidden"
                />
              )}
            </div>
            {children}
          </div>
          <div className="desktop-min:hidden mt-4">
            {renderSideActions && renderSideActions()}
          </div>
        </div>
      </div>
    </div>
  );
};

export { ProfileLayout };
