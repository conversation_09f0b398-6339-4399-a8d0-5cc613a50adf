import {
  type ClientValidationErrorType,
  type ClientNotFoundErrorType,
} from '#shared/types/errors.client';
import {
  APIValidationError,
  APIInternalServerError,
  APINotFoundError,
} from '#shared/utils/api-errors.client';

export async function handleFetchError(response: Response): Promise<void> {
  // probably can do better error handling here
  if (response.status === 400) {
    const data = (await response.json()) as ClientValidationErrorType;
    throw new APIValidationError(data);
  } else if (response.status === 404) {
    const data = (await response.json()) as ClientNotFoundErrorType;
    throw new APINotFoundError(data.error || 'Not found');
  } else if (response.status === 500) {
    throw new APIInternalServerError();
  } else {
    // TODO: add more cases
    throw new Error(response.statusText);
  }
}
