import React from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { TextInput, Button, Text } from 'react-native-paper';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslation } from 'react-i18next';
import * as SecureStore from 'expo-secure-store';
import { createSignupValidator } from '#shared/utils/validators';
import { API_AUTH_REGISTER_ROUTE } from '#shared/constants/routes';
import { apiPost, handleApiCall } from '@/utils/fetch.mobile';

interface FormData {
  email: string;
  password: string;
  verifyPassword: string;
}

const RegistrationScreen: React.FC = ({ navigation }: any) => {
  const { t } = useTranslation();
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>({
    resolver: zodResolver(createSignupValidator(t)),
  });

  const onSubmit = async (data: FormData) => {
    handleApiCall(apiPost<{ token: string }>(API_AUTH_REGISTER_ROUTE, data), {
      onApiSuccess: async (data) => {
        const { token } = data;
        console.log('Registration successful:', token);
        await SecureStore.setItemAsync('userToken', token);
        Alert.alert('Login Successful');
        // Navigate to the next screen or show a success message
      },
    });
  };

  return (
    <View style={styles.container}>
      <Controller
        control={control}
        name="email"
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            label={t('Email Address')}
            keyboardType="email-address"
            autoCapitalize="none"
            onBlur={onBlur}
            onChangeText={onChange}
            value={value}
            style={styles.input}
          />
        )}
      />
      {errors.email && <Text style={styles.error}>{errors.email.message}</Text>}

      <Controller
        control={control}
        name="password"
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            label={t('Password')}
            secureTextEntry
            autoCapitalize="none"
            onBlur={onBlur}
            onChangeText={onChange}
            value={value}
            style={styles.input}
          />
        )}
      />
      {errors.password && (
        <Text style={styles.error}>{errors.password.message}</Text>
      )}

      <Controller
        control={control}
        name="verifyPassword"
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            label={t('Verify Password')}
            secureTextEntry
            autoCapitalize="none"
            onBlur={onBlur}
            onChangeText={onChange}
            value={value}
            style={styles.input}
          />
        )}
      />
      {errors.verifyPassword && (
        <Text style={styles.error}>{errors.verifyPassword.message}</Text>
      )}

      <Button
        mode="contained"
        onPress={handleSubmit(onSubmit)}
        style={styles.button}
      >
        {t('Register')}
      </Button>

      <Button
        mode="text"
        onPress={() => navigation.navigate('Login')}
        style={styles.button}
      >
        {t('Login')}
      </Button>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  input: {
    marginBottom: 10,
  },
  button: {
    marginTop: 10,
  },
  error: {
    color: 'red',
    marginBottom: 10,
  },
});

export default RegistrationScreen;
