import type React from 'react';
import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { FaTimes } from 'react-icons/fa';
import {
  FormFromConfig,
  FormType,
} from '#app/components/forms/form-from-config';
import {
  type ConversationWithMessages,
  type RootProfile,
} from '#app/types/index';
import { AttachmentUpload } from '../forms/inputs';
import { Button } from '../ui/button';

type FileWithPreview = {
  id: string;
  file: File;
  previewUrl: string;
};

const generateId = () => Math.random().toString(36).substring(2, 9);

const createFileKey = (file: File) =>
  `${file.name}-${file.size}-${file.lastModified}`;

type MessagesProps = {
  conversationWithMessages: ConversationWithMessages | null;
  senderProfile: RootProfile;
  receiverProfile: RootProfile;
};

const ChatForm: React.FC<MessagesProps> = ({
  conversationWithMessages,
  receiverProfile,
}) => {
  const [filesWithPreviews, setFilesWithPreviews] = useState<FileWithPreview[]>(
    [],
  );
  const { t } = useTranslation();

  useEffect(() => {
    return () => {
      filesWithPreviews.forEach((item) => {
        if (item.previewUrl) {
          try {
            URL.revokeObjectURL(item.previewUrl);
          } catch (error) {
            console.error('Error revoking object URL:', error);
          }
        }
      });
    };
  }, [filesWithPreviews]);

  const fields = [
    {
      name: 'message',
      type: 'chat-input',
      placeholder: t('Write a message...'),
      className: 'w-full',
      filesWithPreviews,
    },
    {
      name: 'attachments',
      type: 'attachment-upload',
      className: 'w-full',
      triggerType: 'chat',
    },
  ] as const;

  const initiate = conversationWithMessages ? '' : '/initiate';
  const actionString = `/messages/${receiverProfile.username.toLowerCase()}${initiate}`;

  const selectedFiles = filesWithPreviews.map((item) => item.file);

  const handleRemoveFile = (id: string) => {
    setFilesWithPreviews((prev) => {
      const itemToRemove = prev.find((item) => item.id === id);
      if (itemToRemove?.previewUrl)
        URL.revokeObjectURL(itemToRemove.previewUrl);
      return prev.filter((item) => item.id !== id);
    });
  };

  const handleSubmit = () => {
    filesWithPreviews.forEach((item) => {
      if (item.previewUrl) URL.revokeObjectURL(item.previewUrl);
    });
    setFilesWithPreviews([]);
  };

  const handleFileChange = (newFiles: File[]) => {
    if (newFiles.length > selectedFiles.length) {
      const existingFileKeys = new Set(selectedFiles.map(createFileKey));

      const newlyAddedFiles = newFiles.filter(
        (file) => !existingFileKeys.has(createFileKey(file)),
      );

      const newItems = newlyAddedFiles.map((file) => ({
        id: generateId(),
        file,
        previewUrl: file.type.startsWith('image/')
          ? URL.createObjectURL(file)
          : '',
      }));

      setFilesWithPreviews((prev) => [...prev, ...newItems]);
    } else if (newFiles.length < selectedFiles.length) {
      const newFileKeys = new Set(newFiles.map(createFileKey));

      setFilesWithPreviews((prev) => {
        const itemsToRemove = prev.filter(
          (item) => !newFileKeys.has(createFileKey(item.file)),
        );

        itemsToRemove.forEach((item) => {
          if (item.previewUrl) URL.revokeObjectURL(item.previewUrl);
        });

        return prev.filter((item) => newFileKeys.has(createFileKey(item.file)));
      });
    }
  };

  return (
    <div className="relative bg-white w-full lg:rounded-br-[1.5rem] lg:rounded-bl-[1.5rem] px-4 py-4 sm:px-6 sm:py-4 shadow-lg shadow-gray-100/50">
      <div className="absolute bottom-[100%] flex items-center gap-2 left-[2px]">
        {filesWithPreviews.map((item) => (
          <div key={item.id} className="relative group">
            {item.file.type.startsWith('image/') ? (
              <img
                src={item.previewUrl || '/placeholder.svg'}
                alt={item.file.name}
                className="h-10 w-10 object-cover rounded-xl"
              />
            ) : (
              <div className="h-10 w-10 flex items-center justify-center bg-gray-100 rounded-md">
                <span className="text-sm text-gray-500">Video</span>
              </div>
            )}
            <Button
              type="button"
              variant="destructive"
              size="icon"
              onClick={() => handleRemoveFile(item.id)}
              className="absolute top-[-2px] right-[-5px] h-4 w-4"
            >
              <FaTimes fontSize={10} />
            </Button>
          </div>
        ))}
      </div>

      <FormFromConfig
        method="POST"
        encType="multipart/form-data"
        action={actionString}
        fields={fields}
        formType={FormType.FETCHER}
        onSubmit={handleSubmit}
        renderFields={({ fields }) => (
          <>
            <div className="flex items-center gap-4">
              <div>
                <AttachmentUpload
                  triggerType="chat"
                  name="attachments"
                  onChange={handleFileChange}
                  value={selectedFiles}
                  accept="image/*"
                />
              </div>
              <div className="grow">{fields.message.node}</div>
            </div>
          </>
        )}
      />
    </div>
  );
};

export default ChatForm;
