import { useLoaderData } from '@remix-run/react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FormFromConfig } from '#app/components/forms/form-from-config';
import CollapsibleList from '#app/components/profile/collapsible-list.js';
import { ProfileLayout } from '#app/components/profile/profile-layout';
import { Separator } from '#app/components/ui/separator';
import { apiGet, handleApiCall } from '#app/utils/fetch.client';
import { getProfileProgressData } from '#app/utils/profile.server';
import {
  handleFormActionForProfile,
  handleLoaderForProfileRoute,
} from '#app/utils/remix-helpers.server';
import { t } from '#server/utils/api.server';
import { prisma } from '#server/utils/db.server';
import { ValidationError } from '#server/utils/errors.server';
import {
  getCountries,
  getGeocodeData,
  getRegionsOfCountry,
  sortCountriesByPriority,
} from '#server/utils/geography';
import {
  attachImageUrlsToPhoto,
} from '#server/utils/profile.server';
import { PhotoType, type GenderEnum } from '#shared/constants/enums';
import { type FieldsFromConfig } from '#shared/types/forms';
import { type ApiRegion } from '#shared/types/geography';
import { updateProfileValidator } from '#shared/utils/validators';

export const loader = handleLoaderForProfileRoute(
  async ({ profile: initialProfile, privateUser }) => {
    const countries = sortCountriesByPriority(getCountries());
    const { profile, profileProgress } = await getProfileProgressData(
      initialProfile.id,
    );

    const { location, bio, parentPhotos } = profile;

    let regions: ApiRegion[] = [];
    if (location) {
      regions = getRegionsOfCountry(location.countryIso2);
    }
    
    const mainPhoto = parentPhotos.find(
      (photo) => photo.photoType === PhotoType.MAIN,
    );

    return {
      regions,
      countries,
      location,
      bio,
      profile: {
        ...profile,
        mainPhoto: mainPhoto ? attachImageUrlsToPhoto(mainPhoto) : undefined,
        dateOfBirth: privateUser.dateOfBirth?.toISOString(),
      },
      profileProgress,
    };
  },
);

export const action = handleFormActionForProfile(
  updateProfileValidator,
  async ({ successWithMessage, data, user, profile }) => {
    const {
      dateOfBirth,
      // Bio
      tagline,
      aboutMe,
      occupation,
      // Location
      cityOrTown,
      country,
      region,
      // Profile
      ...restData
    } = data;

    // Find similar username
    const otherProfile = await prisma.profile.findUnique({
      where: { username: restData.username, NOT: { id: profile.id } },
    });

    // Check if username already exists
    if (otherProfile) {
      throw new ValidationError({
        username: [t('Username is already taken')],
      });
    }

    // Address values
    let location = null;
    let locationFields = null;

    // Combine the addresses and filter out empty ones`
    const combinedAddress = [cityOrTown, country, region]
      .filter((part) => part)
      .join(',');

    // Check if there is an adress
    if (combinedAddress) {
      // Add checker, Google won't recognize if only country or city/town is provided.
      if (cityOrTown && !country) {
        throw new ValidationError({
          cityOrTown: [t('Country is required when adding city or town')],
        });
      }

      if (country && !cityOrTown) {
        throw new ValidationError({
          cityOrTown: [t('City or town is required when adding country')],
        });
      }

      // Get address geo code from Google
      const { results } = await getGeocodeData(combinedAddress);

      const result = results?.[0];

      // Throw if invalid adress
      if (!result) {
        throw new ValidationError({
          cityOrTown: [t('Invalid city or town')],
        });
      }

      // Check if the result is a partial match (indicates the address wasn't found exactly)
      if (result.partial_match) {
        throw new ValidationError({
          cityOrTown: [t('Invalid city or town')],
        });
      }

      // Check if the city name in the result matches what the user entered
      const cityComponent = result.address_components.find((component) =>
        component.types.some((type) =>
          ['locality', 'administrative_area_level_2', 'sublocality'].includes(
            type,
          ),
        ),
      );

      if (!cityComponent) {
        throw new ValidationError({
          cityOrTown: [t('Please enter a valid city or town name')],
        });
      }

      // Check if the user's input matches the Google result
      const userCity = cityOrTown!.toLowerCase().trim();
      const googleCity = cityComponent.long_name.toLowerCase().trim();

      if (userCity !== googleCity) {
        throw new ValidationError({
          cityOrTown: [t('Please enter the exact city or town name')],
        });
      }

      // Check if address type exists
      const countryComponent = result.address_components.find((component) =>
        component.types.includes('country'),
      );

      // Throw if invalid adress
      if (!countryComponent || countryComponent.short_name !== country) {
        throw new ValidationError({
          cityOrTown: [t('City or town does not match the selected country')],
        });
      }

      location = result;
    }

    // Set location field values if location exists
    if (location) {
      locationFields = {
        latitude: location.geometry.location.lat,
        longitude: location.geometry.location.lng,
        cityOrTown: cityOrTown || '',
        regionCode: region || '',
        countryIso2: country || '',
      };
    }

    // Save the profile data
    await prisma.$transaction(async (tx) => {
      // Execute only if there are location fields
      if (locationFields) {
        await tx.location.upsert({
          where: { profileId: profile.id },
          update: locationFields,
          create: {
            profileId: profile.id,
            ...locationFields,
          },
        });
      }

      await tx.bio.upsert({
        where: { profileId: profile.id },
        update: {
          tagline,
          aboutMe,
          occupation,
        },
        create: {
          tagline,
          aboutMe,
          occupation,
          profile: { connect: { id: profile.id } },
        },
      });

      await tx.profile.update({
        where: { id: profile.id },
        data: {
          ...restData,
          user: { connect: { id: user.id } },
        },
      });

      await tx.privateUser.update({
        where: {
          id: user.privateUserId,
        },
        data: {
          dateOfBirth,
        },
      });
    });

    return successWithMessage('Profile updated successfully');
  },
);

export default function NewProfileRoute() {
  const {
    profile,
    regions: initialRegions,
    countries,
    location,
    bio,
    profileProgress,
  } = useLoaderData<typeof loader>();
  const { t } = useTranslation();
  const [regions, setRegions] = useState(initialRegions);

  let fields: FieldsFromConfig<typeof updateProfileValidator> = [
    {
      name: 'firstName',
      type: 'text',
      label: 'First Name',
      defaultValue: profile.firstName,
    },
    {
      name: 'username',
      type: 'text',
      label: 'Username',
      defaultValue: profile.username,
    },
    {
      name: 'gender',
      type: 'gender-select',
      label: 'Gender',
      defaultValue: profile.gender as GenderEnum,
    },
    {
      name: 'dateOfBirth',
      type: 'date-picker',
      label: 'Date of Birth',
      calendarProps: {
        disabled: (date) => date > new Date(),
      },
      defaultValue: profile.dateOfBirth ?? undefined,
    },
    // Bio
    {
      name: 'tagline',
      label: t('Tagline'),
      type: 'text',
      defaultValue: bio?.tagline ?? '',
      placeholder: t('Write a quick intro'),
    },
    {
      name: 'aboutMe',
      label: t('About Me'),
      type: 'textarea',
      defaultValue: bio?.aboutMe ?? '',
      placeholder: t('Tell others a bit about you'),
    },
    {
      name: 'occupation',
      label: t('Occupation'),
      type: 'text',
      defaultValue: bio?.occupation ?? '',
      placeholder: t('Enter your profession'),
    },

    // Locations
    {
      name: 'country',
      label: t('Country'),
      type: 'combobox',
      options: countries.map((country) => ({
        label: country.name,
        value: country.iso2,
      })),
      defaultValue: location?.countryIso2,
      placeholder: t('Select your country'),
      asyncOnChange: async (value) => {
        if (!value) return;

        await handleApiCall(
          apiGet<{ regions: ApiRegion[] }>(
            `/api/geography/countries/${value}/regions`,
          ),
          {
            onApiSuccess: (data) => setRegions(data.regions),
          },
        );
      },
    },
    {
      name: 'region',
      label: t('Region'),
      type: 'combobox',
      options: regions.map((region) => ({
        label: region.name,
        value: region.regionCode,
      })),
      disabled: regions.length === 0,
      defaultValue: location?.regionCode,
      placeholder: t('Select your region'),
    },
    {
      name: 'cityOrTown',
      label: t('City or town'),
      type: 'text',
      defaultValue: location?.cityOrTown,
      placeholder: t('Select your city or town'),
    },
  ];

  return (
    <FormFromConfig
      showResetButton
      method="POST"
      action="/profile/edit/basic-info"
      fields={fields}
      createValidator={updateProfileValidator}
      renderFields={({ fields, submitButton, resetButton }) => (
        <ProfileLayout
          progress={profileProgress}
          renderSideActions={() => (
            <div className="flex gap-2 tablet:flex-col">
              {submitButton}
              {resetButton}
            </div>
          )}
        >
          <CollapsibleList title={t('Basic Information')}>
            {fields.firstName?.node}
            {fields.username?.node}
            {fields.gender?.node}
            {fields.dateOfBirth?.node}
          </CollapsibleList>
          <Separator />
          <CollapsibleList title={t('Bio')}>
            {fields.tagline?.node}
            {fields.occupation?.node}
            {fields.aboutMe?.node}
          </CollapsibleList>
          <Separator />
          <CollapsibleList
            title={t('Location details')}
            subtitle={t('Add your current location')}
            columns={3}
          >
            {fields.country?.node}
            {fields.region?.node}
            {fields.cityOrTown?.node}
          </CollapsibleList>
        </ProfileLayout>
      )}
    />
  );
}
