export type ErrorDetail = Record<string, string[] | undefined>;

export interface ClientErrorType {
  error: string;
  success: false;
}

export interface ClientValidationErrorType extends ClientErrorType {
  errorType: "ValidationError";
  errorDetail: ErrorDetail;
}

export interface ClientBadRequestErrorType extends ClientErrorType {
  errorType: "BadRequestError";
}

export interface ClientNotFoundErrorType extends ClientErrorType {
  errorType: "NotFoundError";
}

export interface ClientInternalServerErrorType extends ClientErrorType {
  errorType: "InternalServerError";
}

export type ClientFormErrorType =
  | ClientValidationErrorType
  | ClientInternalServerErrorType
  | ClientBadRequestErrorType
  | ClientNotFoundErrorType;
