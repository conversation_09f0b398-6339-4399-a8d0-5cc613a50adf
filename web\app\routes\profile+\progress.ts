import { type LoaderFunctionArgs } from '@remix-run/node';
import { getProfileProgressData } from '#app/utils/profile.server';
import { getBasicInfoFromRequest } from '#server/utils/auth.server';

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { profile: initialProfile } = await getBasicInfoFromRequest({
    request,
    includeProfile: true,
  });

  if (!initialProfile) {
    return { profileProgress: 0 };
  }

  const { profileProgress } = await getProfileProgressData(initialProfile.id);

  return { profileProgress: profileProgress.progress };
};
