// TYPES
import { type DayStyleProps } from '#app/types/calendar-types';

export const COLORS = {
  primary: '#D33544',
  secondary: '#2C80AA',
  primaryLight: '#F8DCE0',
};

export const STYLES = {
  gradientBorder: {
    borderWidth: '2px',
    padding: '11px 43px 11px 15px',
    background: `linear-gradient(white, white) padding-box, linear-gradient(90deg, ${COLORS.primary} 0%, ${COLORS.secondary} 100%) border-box`,
  },
  navButton: {
    backgroundColor: '#F8F9FC',
    width: '36px',
    height: '36px',
    minWidth: '36px',
    borderRadius: '50%',
    padding: '0',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#1E1E1E',
    boxShadow: '0 2px 6px rgba(0,0,0,0.05)',
  },
  navButtonHover: {
    backgroundColor: COLORS.primaryLight,
    color: COLORS.primary,
    boxShadow: '0 4px 10px rgba(211, 53, 68, 0.15)',
  },
  selectionButton: {
    fontSize: '16px',
    fontWeight: '600',
    color: '#1E1E1E',
    padding: '6px 10px',
    borderRadius: '20px',
    border: 'none',
    cursor: 'pointer',
    transition: 'all 0.15s ease',
  },
  activeElement: {
    color: '#FFFFFF',
    backgroundColor: COLORS.primary,
    fontWeight: '600',
    boxShadow: '0 4px 10px rgba(211, 53, 68, 0.25)',
  },
  hoverElement: {
    backgroundColor: COLORS.primaryLight,
    color: COLORS.primary,
    boxShadow: '0 4px 10px rgba(211, 53, 68, 0.15)',
  },
};

export const getDayStyle = ({
  isCurrentMonth,
  isTooRecent,
  dateIsHovered,
  isSelected,
}: DayStyleProps): React.CSSProperties => ({
  width: '40px',
  height: '40px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  fontSize: '15px',
  cursor: isCurrentMonth && !isTooRecent ? 'pointer' : 'default',
  borderRadius: '50%',
  color: isTooRecent
    ? '#C7CCD7'
    : !isCurrentMonth
      ? '#8E95A9'
      : dateIsHovered
        ? COLORS.primary
        : isSelected
          ? '#FFFFFF'
          : '#1E1E1E',
  backgroundColor: dateIsHovered
    ? COLORS.primaryLight
    : isSelected
      ? COLORS.primary
      : 'transparent',
  opacity: !isCurrentMonth || isTooRecent ? 0.5 : 1,
  fontWeight: isSelected ? '600' : 'normal',
  border: 'none',
  transition: 'all 0.15s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  transform: dateIsHovered
    ? 'scale(1.1)'
    : isSelected
      ? 'scale(1.05)'
      : 'scale(1)',
  boxShadow:
    dateIsHovered || isSelected ? '0 4px 10px rgba(211, 53, 68, 0.25)' : 'none',
});

export const yearContainerStyle: React.CSSProperties = {
  position: 'absolute',
  top: '0',
  left: '0',
  right: '0',
  backgroundColor: '#FFFFFF',
  borderRadius: '12px',
  padding: '12px',
  zIndex: 10,
  maxHeight: '280px',
  overflowY: 'auto',
  display: 'grid',
  gridTemplateColumns: 'repeat(4, 1fr)',
  gap: '8px',
  animation: 'fadeIn 0.2s ease-in-out',
};

export const baseYearStyle: React.CSSProperties = {
  height: '40px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  borderRadius: '8px',
  cursor: 'pointer',
  backgroundColor: 'transparent',
  color: '#1E1E1E',
  fontWeight: 'normal',
  transition: 'all 0.15s ease',
  boxShadow: 'none',
  border: 'none',
};

export const monthContainerStyle: React.CSSProperties = {
  position: 'absolute',
  top: '0',
  left: '0',
  right: '0',
  backgroundColor: '#FFFFFF',
  borderRadius: '12px',
  padding: '12px',
  zIndex: 10,
  display: 'grid',
  gridTemplateColumns: 'repeat(4, 1fr)',
  gap: '8px',
  animation: 'fadeIn 0.2s ease-in-out',
  height: '280px',
};

export const getBaseMonthStyle = (
  isDisabled: boolean,
): React.CSSProperties => ({
  width: '100%',
  height: '40px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  borderRadius: '8px',
  cursor: isDisabled ? 'default' : 'pointer',
  backgroundColor: 'transparent',
  color: isDisabled ? '#C7CCD7' : '#1E1E1E',
  fontWeight: 'normal',
  transition: 'all 0.15s ease',
  boxShadow: 'none',
  opacity: isDisabled ? 0.5 : 1,
  border: 'none',
});
