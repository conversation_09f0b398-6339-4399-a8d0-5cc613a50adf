import {
  v2 as cloudinary,
  type CropMode,
  type UploadApiResponse,
} from 'cloudinary';
import { VariantTypeConfig } from '#shared/constants/media';

// Rate limiting queue for Cloudinary operations, specially for seeding too many items
class CloudinaryQueue {
  private queue: (() => Promise<void>)[] = [];
  private processing = false;
  private readonly maxConcurrent = 3; // Limit concurrent operations
  private readonly delayBetweenOperations = 500; // 500ms delay between operations
  private activeOperations = 0;

  async add<T>(operation: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const result = await operation();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });
      void this.processQueue();
    });
  }

  private async processQueue() {
    if (this.processing || this.activeOperations >= this.maxConcurrent) {
      return;
    }

    const operation = this.queue.shift();
    if (!operation) {
      return;
    }

    this.processing = true;
    this.activeOperations++;

    try {
      await operation();
    } catch (error) {
      console.error('Cloudinary operation failed:', error);
    } finally {
      this.activeOperations--;

      // Add delay between operations to prevent rate limiting
      if (this.queue.length > 0) {
        await new Promise((resolve) =>
          setTimeout(resolve, this.delayBetweenOperations),
        );
      }

      this.processing = false;

      // Process next operation if queue is not empty
      if (this.queue.length > 0) {
        setImmediate(() => this.processQueue());
      }
    }
  }
}

const cloudinaryQueue = new CloudinaryQueue();

cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

export type CloudinaryOpts = {
  height?: number;
  width?: number;
  crop?: CropMode;
  resourceType?: string;
};

export function getCloudinaryUrl(publicId: string, opts?: CloudinaryOpts) {
  const height = opts?.height;
  const width = opts?.width;
  const crop = opts?.crop;
  const resourceType = opts?.resourceType;

  const url = cloudinary.url(publicId, {
    height,
    width,
    crop,
    resource_type: resourceType ?? undefined,
  });

  return url;
}

type CloudinaryUploadImageApiResponse = UploadApiResponse & {
  eager: CloudinaryImage[];
};

function buildCloudinaryFolderPath({
  subPath,
  profileId,
  resourceType,
}: {
  subPath: string;
  profileId: string;
  resourceType: string;
}) {
  const nodeEnv = process.env.NODE_ENV || 'development';
  return `${nodeEnv}/${profileId}/${resourceType}/${subPath}`;
}

export async function uploadFileToCloudinary({
  file,
  subPath,
  resourceType,
  profileId,
}: {
  file: File | Buffer;
  subPath: string;
  resourceType: 'image' | 'video';
  profileId: string;
}) {
  const buffer =
    file instanceof Buffer
      ? file
      : Buffer.from(await (file as File).arrayBuffer());

  const folder = buildCloudinaryFolderPath({
    subPath,
    profileId,
    resourceType,
  });

  const result: UploadApiResponse = await new Promise((resolve, reject) => {
    const uploadStream = cloudinary.uploader.upload_stream(
      {
        resource_type: resourceType,
        type: 'upload',
        folder,
        ...(resourceType === 'image' && {
          eager: Object.values(VariantTypeConfig),
        }),
      },
      (error, result) => {
        if (error) {
          reject(error);
        } else {
          resolve(result!);
        }
      },
    );

    uploadStream.end(buffer);
  });

  // console.log('Result', result);

  return {
    ...result,
    fileId: result.public_id,
    url: result.secure_url,
    assetId: result.asset_id,
    // eager is only returned if we pass transformation opts as
    // seen in results value assignment
    ...(resourceType === 'image' && {
      eager: result.eager,
    }),
  };
}

export async function uploadImageToCloudinary({
  file,
  subPath,
  profileId,
}: {
  file: File | Buffer;
  subPath: string;
  profileId: string;
}): Promise<CloudinaryUploadImageApiResponse> {
  const result = await uploadFileToCloudinary({
    file,
    profileId,
    subPath,
    resourceType: 'image',
  });

  return {
    ...result,
    eager: result.eager,
  };
}

type CloudinaryImage = {
  transformation: string;
  width?: number;
  height?: number;
  bytes?: number;
  format?: string;
  url?: string;
  secure_url: string;
};

export async function deleteFileFromCloudinary({
  fileId,
  type,
}: {
  fileId: string;
  type?: 'image' | 'video';
}) {
  const resourceType = type ?? 'image';

  const destroyOptions = {
    invalidate: true,
    type: 'upload',
    resource_type: resourceType,
  };

  return cloudinaryQueue.add(async () => {
    try {
      await cloudinary.uploader.destroy(fileId, destroyOptions);
    } catch (err) {
      throw err;
    }
  });
}
