# Filipina Meet Project

This project is divided into two main components: the `web` application and the `mobile` application. Each component has its own environment variables, dependencies, and specific setup instructions. Follow the steps below to get everything up and running.

## Prerequisites

- **[Volta](https://volta.sh/)** (for managing Node.js versions)
- **[Docker](https://www.docker.com/)** (for running the database and Redis)

## Setup Instructions

### Copy `.env.example` Files

Each component (`web` and `mobile`) requires its own environment configuration. The `.env.example` files provided serve as templates.

### Root Changes

Copy the `.env.example` file in the root directory:

```bash
cp .env.example .env
```

#### Web Application

Copy the `.env.example` file in the `web` directory:

```bash
cp web/.env.example web/.env
```

Update the `web/.env` file with the following environment variables:

##### Resend

- **`RESEND_API_KEY`**: API key for Resend. Replace with your API key.

##### Cloudinary

Refer to the [Cloudinary credentials documentation](https://cloudinary.com/documentation/finding_your_credentials_tutorial) for instructions on finding your API key, secret, and cloud name.

- **`CLOUDINARY_API_KEY`**: Your Cloudinary API key. Replace with your API key.
- **`CLOUDINARY_API_SECRET`**: Your Cloudinary API secret. Replace with your API secret.
- **`CLOUDINARY_CLOUD_NAME`**: Your Cloudinary cloud name. Replace with your cloud name.

##### Cloudflare

Request an invite to the Cloudflare Pioneer Dev AI team and refer to the [Cloudflare R2 API Tokens documentation](https://developers.cloudflare.com/r2/api/tokens/) for generating the required tokens.

- **`CLOUDFLARE_ENDPOINT`**: Your Cloudflare R2 endpoint. Replace with your endpoint.
- **`CLOUDFLARE_ACCESS_KEY_ID`**: Your Cloudflare R2 access key ID. Replace with your access key ID.
- **`CLOUDFLARE_SECRET_ACCESS_KEY`**: Your Cloudflare R2 secret access key. Replace with your secret access key.
- **`CLOUDFLARE_TOKEN`**: Your Cloudflare API token. Replace with your API token.
- **`CLOUDFLARE_BUCKET_NAME`**: Your Cloudflare R2 bucket name. Replace with your bucket name.
- **`CLOUDFLARE_PUBLIC_URL`**: Your Cloudflare R2 public URL. Replace with your public URL.

The following environment variables can be obtained from Fly's integration with Tigris storage:

- **`AWS_ACCESS_KEY_ID`**: Replace with your own access key id
- **`AWS_SECRET_ACCESS_KEY`**: Replace with your AWS secret access key.
- **`AWS_ENDPOINT_URL_S3`**: Leave as `https://fly.storage.tigris.dev`.
- **`AWS_REGION`**: Leave as `auto`.
- **`AWS_BUCKET_NAME`**: Leave as `filipina-meet`.

- **`GOOGLE_MAPS_API_KEY`**: Your Google Maps API key. Replace with your own.

#### Mobile Application

Copy the `.env.example` file in the `mobile` directory:

```bash
cp mobile/.env.example mobile/.env
```

Update the `mobile/.env` file with the following environment variables:

- **`EXPO_PUBLIC_API_URL`**: API URL for the mobile application. Replace with your IP address and port.
  ```plaintext
  EXPO_PUBLIC_API_URL=YOUR_IP_ADDRESS_WITH_PORT
  ```

### Install Dependencies

#### Install dependencies in the root directory

```bash
npm install
```

Install the dependencies for both `web` and `mobile` applications.

#### Web Application

```bash
npm --prefix ./web install
```

#### Mobile Application

```bash
npm --prefix ./mobile install
```

### Setup the Database

Start the PostgreSQL database and Redis using Docker:

```bash
docker-compose up -d
```

### Migrations

For the prisma client generation (and also for making the migrations if no prisma client has been generated yet), it's necessary the presence of `web/.env`, even if the file is empty. This way, the prisma client generated will load the environment variables in `web/.env` automatically when the generated client is imported. If the file `web/.env` is not present during the prisma client generation, the prisma client will not load automatically the env file in `web/.env`, when the prisma client is imported. One relevant parameter necessary for using an instance of the prisma client is the `DATABASE_URL` environment variable.

If your database is already up and running, run the migrations with this command in the root directory:

```bash
npm run prisma:migrate:dev
```

The prisma client is already generated in the previous step.

It's still possible to generate a prisma client if no database is configured yet. For the generation of the client, run this in the root directory:

```bash
npm run prisma:generate
```

### Running the Applications

#### Web Application

Start the web application for development:

```bash
npm run dev:web
```

The web application should now be running at `http://localhost:3000`. This will run a process watching for changes on the files and compiling in real time.

#### Adding testing data to the application

Generate a prisma client and run the migrations first. Use the following command:

```bash
    npm run seed
```

#### Mobile Application

Start the mobile application using Expo:

```bash
npm run dev:mobile
```

### Troubleshooting TypeScript Errors

If you encounter TypeScript errors such as `Cannot find module` or module resolution issues, try the following steps:

- **Restart the TypeScript Server**

  - Press `F1` and select `TypeScript: Restart TS Server`

- **For Cursor users**
  - Press `F1` and select `Cursor: Attempt Update`

### Stopping the Services

When you're done with development, you can stop and remove the Docker container:

```bash
docker-compose down
```

### Build for production

Web application:

```bash
npm run build:web
```

Mobile application:

```bash
npm run build:mobile
```

### Additional Notes

- **Environment Variables**: Ensure that you replace the placeholder values in the `.env` files with your actual environment-specific values.
- **Testing**: Make sure to add any specific testing instructions if needed.
- **CI/CD**: If you're using CI/CD, make sure your pipelines are set up to handle these environment variables and dependencies.
