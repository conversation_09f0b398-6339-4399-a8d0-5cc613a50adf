import { type FastifyInstance } from 'fastify';
import { type z } from 'zod';
import { handleSignup } from '#server/utils/auth-helpers.server';
import { handleFastifyRoute } from '#server/utils/fastify-helpers.server';
import { type SignupData } from '#shared/types/auth';
import { type createSignupValidator } from '#shared/utils/validators.js';

export async function authRouter(fastify: FastifyInstance) {
  fastify.post<{
    Body: z.infer<ReturnType<typeof createSignupValidator>>;
  }>(
    '/register',
    handleFastifyRoute(async ({ request, sendSuccessPayload }) => {
      const session = await handleSignup(request.body as SignupData);

      // Create a token containing user id and session id
      const token = {
        privateUserId: session.privateUserId,
        sessionId: session.id,
      };

      return sendSuccessPayload({ token });
    }),
  );
}
