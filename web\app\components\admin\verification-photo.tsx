import { type Profile } from '@prisma/client';
import { toast as showToast } from 'sonner';
import { Button } from '#app/components/ui/button';
import { type VerificationPhoto } from '#app/types/serialized-types';
import { apiPut } from '#app/utils/fetch.client';
import { VerificationStatus } from '#shared/constants/enums';

function VerificationPhoto({
  photo,
  onSuccess,
  profile,
}: {
  photo: VerificationPhoto;
  onSuccess: () => void;
  profile: Profile;
}) {
  const handleVerification = async (newStatus: VerificationStatus) => {
    try {
      await apiPut(`/api/admin/photos/${photo.id}/verify`, {
        verificationStatus: newStatus,
      });
      onSuccess();
    } catch {
      showToast.error('Failed to update verification status');
    }
  };

  return (
    <div>
      <img
        src={photo.mediumUrl}
        alt={`Photo for profile ${profile.username}`}
      />
      <p>Profile: {profile.username}</p>
      <p>Uploaded at: {new Date(photo.createdAt).toLocaleString()}</p>
      <p>Current Status: {photo.verificationStatus}</p>
      <Button onClick={() => handleVerification(VerificationStatus.APPROVED)}>
        Approve
      </Button>
      <Button onClick={() => handleVerification(VerificationStatus.REJECTED)}>
        Reject
      </Button>
    </div>
  );
}

export default VerificationPhoto;
