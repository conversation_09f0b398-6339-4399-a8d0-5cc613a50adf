import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import mailbox from '#app/images/svg-icons/mailbox.svg';
import { cn } from '#app/utils/misc.js';
import { SEARCH_BASIC_ROUTE } from '#shared/constants/routes';
import { type EnrichedConversation } from '#shared/types/conversations';
import Typography from '../typography';
import SearchMessage from '../ui/search-message';
import ConversationItem from './conversation-item';
import EmptyActivityView from './empty-activity-view';

interface ConversationsContainerProps {
  conversations: EnrichedConversation[];
  currentUserId: string;
  activeUsername?: string;
}

const ConversationsContainer = ({
  conversations,
  currentUserId,
  activeUsername,
}: ConversationsContainerProps) => {
  const { t } = useTranslation();
  const [searchValue, setSearchValue] = useState('');

  const hasConversations = conversations.length > 0;

  const filteredConversations = conversations.filter((conversation) => {
    const searchTerm = searchValue.toLowerCase();
    if (!conversation.otherProfile) return false;

    const firstNameMatch = conversation.otherProfile.firstName
      .toLowerCase()
      .includes(searchTerm);
    const usernameMatch = conversation.otherProfile.username
      .toLowerCase()
      .includes(searchTerm);
    return firstNameMatch || usernameMatch;
  });

  const handleClearInput = () => {
    setSearchValue('');
  };

  return (
    <div className="h-full flex-1 overflow-auto bg-white rounded-[24px] pt-6">
      {hasConversations ? (
        <>
          <div className={cn('px-6', hasConversations ? 'mb-6' : '')}>
            <Typography
              variant="h3"
              className="bg-gradient-to-r from-brand-secondary to-brand-primary bg-clip-text text-transparent w-[150px] h-[50px]"
            >
              {t('Messages')}
            </Typography>
          </div>
          <SearchMessage
            onClearInput={handleClearInput}
            searchValue={searchValue}
            setSearchValue={setSearchValue}
          />

          <div className="px-4">
            {filteredConversations.length > 0 ? (
              filteredConversations.map((conversation) => {
                if (!conversation.otherProfile) return null;

                const hasUnreadMessages =
                  conversation.lastMessage &&
                  !conversation.lastMessage.readAt &&
                  conversation.lastMessage.senderProfileId !== currentUserId &&
                  conversation.otherProfile.username !== activeUsername;

                return (
                  <ConversationItem
                    key={conversation.id}
                    lastMessage={conversation.lastMessage}
                    otherProfile={conversation.otherProfile}
                    hasUnreadMessages={hasUnreadMessages}
                  />
                );
              })
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                {t('No conversations found')}
              </div>
            )}
          </div>
        </>
      ) : (
        <EmptyActivityView
          imgSrc={mailbox}
          title={t('Every story starts somewhere.')}
          description={t('Discover new profiles and start yours today!')}
          cta={{
            title: t('Start Exploring'),
            path: SEARCH_BASIC_ROUTE,
          }}
        />
      )}
    </div>
  );
};

export default ConversationsContainer;
