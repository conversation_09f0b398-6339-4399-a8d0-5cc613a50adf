import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  FormFromConfig,
  FormType,
} from '#app/components/forms/form-from-config';
import {
  type RootProfile,
  type ConversationWithMessages,
} from '#app/types/serialized-types';
import { ChatMessage } from './chat-message';

type MessagesProps = {
  conversationWithMessages: ConversationWithMessages | null;
  senderProfile: RootProfile;
  receiverProfile: RootProfile;
};

const MessagesResult: React.FC<MessagesProps> = ({
  conversationWithMessages,
  senderProfile,
  receiverProfile,
}) => {
  const { t } = useTranslation();
  const fields = !!conversationWithMessages
    ? ([
        {
          name: 'message',
          type: 'textarea',
          label: t('Message'),
        },
        {
          name: 'attachments',
          type: 'attachment-upload' as const,
          label: t('Attachments'),
          multiple: true,
          accept: 'image/*,video/*',
          defaultValue: [] as File[],
        },
      ] as const)
    : ([
        {
          name: 'subject',
          type: 'text',
          label: t('Subject'),
        },
        {
          name: 'message',
          type: 'textarea',
          label: t('Message'),
        },
        {
          name: 'attachments',
          type: 'attachment-upload' as const,
          label: t('Attachments'),
          multiple: true,
          accept: 'image/*,video/*',
          defaultValue: [] as File[],
        },
      ] as const);

  const initiate = conversationWithMessages ? '' : '/initiate';
  const actionString = `/messages/${receiverProfile.username.toLowerCase()}${initiate}`;

  // In your MessagesResult component
  const formElement = (
    <FormFromConfig
      method="POST"
      action={actionString}
      encType="multipart/form-data"
      fields={fields}
      formType={FormType.FETCHER}
      onSubmit={(event) => {
        const formData = new FormData(event.currentTarget);

        // Debug form data before submission
        console.log('Form submission debug:');
        for (const [key, value] of formData.entries()) {
          console.log({
            key,
            valueType: typeof value,
            isFile: value instanceof File,
            constructor: value?.constructor?.name,
            details:
              value instanceof File
                ? {
                    name: value.name,
                    type: value.type,
                    size: value.size,
                  }
                : value,
          });
        }

        // Check attachments specifically
        const attachments = formData.getAll('attachments');
        console.log(
          'Attachments debug:',
          attachments.map((file) => ({
            type: typeof file,
            isFile: file instanceof File,
            constructor: file?.constructor?.name,
            properties:
              file instanceof File
                ? {
                    name: file.name,
                    type: file.type,
                    size: file.size,
                  }
                : Object.getOwnPropertyNames(file),
          })),
        );
      }}
    />
  );

  if (conversationWithMessages) {
    return (
      <div>
        Subject: {conversationWithMessages.subject}
        <div>
          {conversationWithMessages.messages?.map((message, index) => (
            <ChatMessage
              key={index}
              message={message}
              senderProfile={senderProfile}
              receiverProfile={receiverProfile}
            />
          ))}
          {formElement}
        </div>
      </div>
    );
  } else {
    return <div>{formElement}</div>;
  }
};

export { MessagesResult };
