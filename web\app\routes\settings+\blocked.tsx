import { useLoaderData } from '@remix-run/react';
import { BlockedUsers } from '#app/components/settings/blocked-users';
import { handleLoaderForProfileRoute } from '#app/utils/remix-helpers.server';
import { prisma } from '#server/utils/db.server';
import {
  enrichProfileForFullProfileView,
  getProfileWithExtrasByProfileId,
} from '#server/utils/profile.server';

export const loader = handleLoaderForProfileRoute(async ({ profile }) => {
  const blockedUsers = await prisma.profileBlock.findMany({
    where: {
      blockerProfileId: profile.id,
    },
    include: {
      blockedProfile: true,
    },
  });

  const enrichedBlockedUsers = await Promise.all(
    blockedUsers.map(async (block) => {
      const profileWithExtras = await getProfileWithExtrasByProfileId(
        block.blockedProfile.id,
      );
      const fullProfile = await enrichProfileForFullProfileView(
        profileWithExtras,
        profile.id,
      );
      return {
        ...block,
        blockedProfile: fullProfile,
      };
    }),
  );

  return { blockedUsers: enrichedBlockedUsers };
});

export default function BlockedUsersPage() {
  const { blockedUsers } = useLoaderData<typeof loader>();
  return <BlockedUsers blockedUsers={blockedUsers} />;
}
