import * as Slider from '@radix-ui/react-slider';
import React from 'react';
import { AGES } from '#shared/constants/profile';

interface AgeRangeSliderProps {
  name: string;
  showHighUpperAge?: boolean;
  onChange: (value: [number, number]) => void;
  value?: [number, number];
}

// TODO: show selected ages as a number
const AgeRangeSlider: React.FC<AgeRangeSliderProps> = ({
  name,
  showHighUpperAge = false,
  onChange,
  value = [AGES.MIN_AGE, AGES.MAX_AGE],
}) => {
  const maxSliderDisplayValue = showHighUpperAge
    ? AGES.UPPER_AGE_SLIDER_HIGH
    : AGES.UPPER_AGE_SLIDER_LOW;

  // If the upper value is the max value, we want to display the maxSliderDisplayValue
  const mappedUpperValue =
    value[1] >= maxSliderDisplayValue ? AGES.MAX_AGE : value[1];
  return (
    <div className="max-w-72">
      <Slider.Root
        className="relative flex h-4 w-full items-center"
        value={value}
        min={AGES.MIN_AGE}
        max={maxSliderDisplayValue}
        step={1}
        onValueChange={onChange}
        minStepsBetweenThumbs={1}
      >
        <Slider.Track className="relative h-2 flex-grow rounded-full bg-gray-300">
          <Slider.Range className="absolute h-full rounded-full bg-brand-primary" />
        </Slider.Track>
        <Slider.Thumb
          className="block h-5 w-5 rounded-full bg-brand-primary shadow-md"
          aria-label="Min Age"
        />
        <Slider.Thumb
          className="block h-5 w-5 rounded-full bg-brand-primary shadow-md"
          aria-label="Max Age"
        />
      </Slider.Root>
      <input
        type="hidden"
        name={name}
        value={`${value[0]},${mappedUpperValue}`}
      />
      <div className="mt-2 flex justify-between text-sm">
        <span>{value[0]}</span>
        <span>
          {value[1] >= maxSliderDisplayValue
            ? `${maxSliderDisplayValue}+`
            : value[1]}
        </span>
      </div>
    </div>
  );
};

export { AgeRangeSlider };
