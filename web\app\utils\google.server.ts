import { SocialProvider } from '@prisma/client';
import { apiGet, apiPost } from '#shared/utils/fetch.js';
import { getSocialRedirectUrl } from './social.server';

const GOOGLE_SCOPES = ['email', 'profile'].join(' ');

export function generateGoogleOauthUrl(requestUrl: string) {
  if (!process.env.GOOGLE_CLIENT_ID) {
    throw new Error('Missing GOOGLE_CLIENT_ID env variable');
  }
  const queryParams = new URLSearchParams({
    client_id: process.env.GOOGLE_CLIENT_ID,
    redirect_uri: getSocialRedirectUrl(SocialProvider.GOOGLE, requestUrl),
    state: '', // TODO: add state that we can check on callback
    scope: GOOGLE_SCOPES,
    response_type: 'code',
    access_type: 'offline',
    prompt: 'consent',
  });
  return `https://accounts.google.com/o/oauth2/auth?${queryParams.toString()}`;
}

export async function getGoogleAccessToken({
  code,
  requestUrl,
}: {
  code: string;
  requestUrl: string;
}) {
  const clientId = process.env.GOOGLE_CLIENT_ID;
  const clientSecret = process.env.GOOGLE_CLIENT_SECRET;
  if (!clientId || !clientSecret) {
    throw new Error('Google client ID or client secret is not set');
  }

  return apiPost<{
    access_token: string;
    refresh_token: string;
    expires_in: number;
    scope: string;
    token_type: string;
    id_token: string;
  }>('https://oauth2.googleapis.com/token', null, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    queryParams: {
      client_id: clientId,
      client_secret: clientSecret,
      redirect_uri: getSocialRedirectUrl(SocialProvider.GOOGLE, requestUrl),
      code: code,
      grant_type: 'authorization_code',
    },
  });
}

export async function getGoogleProfileData({
  accessToken,
}: {
  accessToken: string;
}) {
  return apiGet<{ id: string; email: string; given_name: string }>(
    'https://www.googleapis.com/oauth2/v2/userinfo',
    {
      queryParams: {
        access_token: accessToken,
      },
    },
  );
}
