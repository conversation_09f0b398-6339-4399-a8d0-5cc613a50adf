import { useEffect, useState, type ReactNode } from 'react';
import { removeEmptyStrings } from '#app/utils/misc.js';
import Typography from '../../typography';
import { Checkbox } from './checkbox';

interface Option<T extends string> {
  value: T;
  label: ReactNode;
  icon?: ReactNode;
}

interface MultiCheckboxProps<T extends string> {
  name: string;
  options: Option<T>[];
  value?: T[];
  onChange?: (selected: T[]) => void;
}

function GenericMultiCheckbox<T extends string>({
  name,
  options,
  value = [],
  onChange,
}: MultiCheckboxProps<T>) {
  const [selectedValues, setSelectedValues] = useState<T[]>(
    value.length ? removeEmptyStrings(value) : [],
  );

  useEffect(() => {
    if (value.length) setSelectedValues(removeEmptyStrings(value));
  }, [value]);

  useEffect(() => {
    if (onChange) onChange(selectedValues);
  }, [selectedValues, onChange]);

  return (
    <>
      <div className="space-y-1">
        {options.map((option, i) => {
          const isSelected = selectedValues.includes(option.value);

          return (
            <div key={i} className="flex items-center gap-2">
              <Checkbox
                checked={isSelected}
                onCheckedChange={() => {
                  if (isSelected) {
                    setSelectedValues((prev) =>
                      prev.filter((value) => value !== option.value),
                    );
                  } else {
                    setSelectedValues((prev) => [...prev, option.value]);
                  }
                }}
              />
              <Typography variant="small">{option.label}</Typography>
            </div>
          );
        })}
      </div>

      <input type="hidden" name={name} value={selectedValues} />
    </>
  );
}

export { GenericMultiCheckbox };
