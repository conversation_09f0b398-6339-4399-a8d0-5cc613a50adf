import { SocialProvider } from '@prisma/client';
import { prisma } from '#server/utils/db.server.js';
import { FACEBOOK_GRAPH_API_BASE_URL } from '#shared/constants/facebook.js';
import { apiGet, apiPost } from '#shared/utils/fetch.js';
import { getSocialRedirectUrl } from './social.server';

export const FACEBOOK_SCOPES = [
  'email',
  'public_profile',
  'pages_messaging',
].join(',');

export async function generateFacebookOauthUrl(requestUrl: string, state: string) {
  if (!process.env.FACEBOOK_CLIENT_ID) {
    throw new Error('Missing FACEBOOK_APP_ID env variable');
  }

  const queryParams = new URLSearchParams({
    client_id: process.env.FACEBOOK_CLIENT_ID,
    redirect_uri: getSocialRedirectUrl(SocialProvider.FACEBOOK, requestUrl),
    state: state,
    scope: FACEBOOK_SCOPES,
  });
  return `https://www.facebook.com/v23.0/dialog/oauth?${queryParams.toString()}`;
}

export async function getFacebookAccessToken({
  code,
  requestUrl,
}: {
  code: string;
  requestUrl: string;
}) {
  const clientId = process.env.FACEBOOK_CLIENT_ID;
  const clientSecret = process.env.FACEBOOK_CLIENT_SECRET;
  if (!clientId || !clientSecret) {
    throw new Error('Facebook client ID or client secret is not set');
  }

  const response = await apiGet<{
    access_token: string;
    token_type: string;
    expires_in: number;
  }>(`${FACEBOOK_GRAPH_API_BASE_URL}/oauth/access_token`, {
    queryParams: {
      client_id: clientId,
      client_secret: clientSecret,
      redirect_uri: getSocialRedirectUrl(SocialProvider.FACEBOOK, requestUrl),
      code,
    },
  });
  return response;
}

export async function getFacebookProfileData({
  accessToken,
}: {
  accessToken: string;
}) {
  const profileData = await apiGet<{
    first_name: string;
    email: string;
    id: string;
  }>('https://graph.facebook.com/me', {
    queryParams: {
      fields: 'first_name,email',
      access_token: accessToken,
    },
  });

  return profileData;
}

export async function sendFacebookNotificationMessage({
  recipientNotificationMessagesToken,
  recipientUsername,
  socialConnectionId,
}: {
  recipientNotificationMessagesToken: string;
  recipientUsername: string;
  socialConnectionId: string;
}) {
  await apiPost(
    `${FACEBOOK_GRAPH_API_BASE_URL}/${process.env.FACEBOOK_PAGE_ID}/messages?access_token=${process.env.FACEBOOK_MESSENGER_API_TOKEN}`, 
    {
      recipient: {
        notification_messages_token: recipientNotificationMessagesToken,
      },
      message: {
        attachment: {
          type: 'template',
          payload: {
            template_type: 'generic',
            elements: [
              {
                title: 'New Message',
                subtitle: `You received a new message from ${recipientUsername} in FilipinaMeet`,
                buttons: [
                  {
                    type: 'web_url',
                    url: `${process.env.BASE_URL}/messages/${recipientUsername}`,
                    title: 'View Message',
                  }
                ],
              },
            ]
          }
        }
      },
    }, 
  );

  await prisma.facebookUserOptIn.update( {
    where: {
      socialConnectionId: socialConnectionId,
    },
    data: {
      lastMessageSentAt: new Date( Date.now() )
    },
  })
}

export async function createFacebookNotificationTopic({
  topic,
  recipientPSID,
}: {
  topic: string;
  recipientPSID: string;
}) {
  await apiPost(
    `${FACEBOOK_GRAPH_API_BASE_URL}/${process.env.FACEBOOK_PAGE_ID}/messages?access_token=${process.env.FACEBOOK_MESSENGER_API_TOKEN}&debug=all`, 
    {
      recipient: {
        id: recipientPSID,
      },
      message: {
        attachment: {
          type: 'template',
          payload: {
            template_type: 'notification_messages',
            title: topic,
            payload: `Registering a new topic: ${topic}`,
          }
        }
      }
    }, 
    {
      headers: {
        'Content-Type': 'application/json',
      },
    },
  )
}