import { $Enums } from '@prisma/client';
import { createDimensionStringHash } from '#shared/utils/images';

export const LARGE_PHOTO_MAX_DIMENSION = 800;
export const PREVIEW_PHOTO_MAX_DIMENSION = 400;
export const THUMBNAIL_PHOTO_MAX_DIMENSION = 100;
export const DEFAULT_PHOTO_CROP = 'fill';
export const MIN_PHOTO_DIMENSION = 100;

export const VariantTypeConfig = {
  [$Enums.VariantType.LARGE]: {
    width: LARGE_PHOTO_MAX_DIMENSION,
    height: LARGE_PHOTO_MAX_DIMENSION,
    crop: DEFAULT_PHOTO_CROP,
  },
  [$Enums.VariantType.MEDIUM]: {
    width: PREVIEW_PHOTO_MAX_DIMENSION,
    height: PREVIEW_PHOTO_MAX_DIMENSION,
    crop: DEFAULT_PHOTO_CROP,
  },
  [$Enums.VariantType.SMALL]: {
    width: THUMBNAIL_PHOTO_MAX_DIMENSION,
    height: THUMBNAIL_PHOTO_MAX_DIMENSION,
    crop: DEFAULT_PHOTO_CROP,
  },
} as const;

/**
 *  used for reverse lookup of ```VariantType``` using ```createDimensionStringHash```
 *  @see createDimensionStringHash
 */
export const DimensionStringToVariantType = Object.fromEntries(
  Object.entries(VariantTypeConfig).map(([variant, config]) => [
    createDimensionStringHash({
      height: config.height,
      width: config.width,
    }),
    variant as $Enums.VariantType,
  ]),
);
