import { VerificationStatus } from '@prisma/client';
import { type FastifyInstance } from 'fastify';
import { type z } from 'zod';
import { prisma } from '#server/utils/db.server';
import { NotFoundError } from '#server/utils/errors.server';
import { handleFastifyRoute } from '#server/utils/fastify-helpers.server';

interface VerifyPhotoParams {
  photoId: string;
}

type VerifyPhotoBody = z.infer<
  Zod.ZodObject<
    { verificationStatus: Zod.ZodNativeEnum<typeof VerificationStatus> },
    'strip'
  >
>;

export async function photoRouter(fastify: FastifyInstance) {
  fastify.put<{
    Params: VerifyPhotoParams;
    Body: VerifyPhotoBody;
  }>(
    '/:photoId/verify',
    handleFastifyRoute(async ({ request, sendSuccessMessage }) => {
      const { photoId } = request.params as VerifyPhotoParams;
      const { verificationStatus } = request.body as VerifyPhotoBody;

      let photo = await prisma.parentPhoto.findUnique({
        where: { id: photoId },
      });

      if (!photo) {
        throw new NotFoundError('Photo not found.');
      }

      photo = await prisma.parentPhoto.update({
        where: { id: photoId },
        data: {
          verificationStatus,
          verifiedAt:
            verificationStatus === VerificationStatus.APPROVED
              ? new Date()
              : undefined,
        },
      });

      // admin doen't need translations
      sendSuccessMessage(
        `Photo ${photoId} has been updated to ${verificationStatus}.`,
        photo,
      );
    }),
  );
}
