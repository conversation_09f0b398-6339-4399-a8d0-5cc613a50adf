// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgres"
  url      = env("DATABASE_URL")
}

model PrivateUser {
  id          String    @id @default(cuid())
  email       String?
  phoneNumber String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  // uniqueId is based on email or the provider id
  uniqueId    String    @unique
  dateOfBirth DateTime?
  locale      String?

  messageNotificationSettings MessageNotificationSetting[]

  password          Password?
  socialConnections SocialConnection[]
  user              User?
  sessions          Session[]
}

model MessageNotificationSetting {
  id                   String                           @id @default(cuid())
  createdAt            DateTime                         @default(now())
  updatedAt            DateTime                         @updatedAt
  communicationChannel ExternalCommunicationChannelEnum
  setting              MessageNotificationSettingEnum
  privateUser          PrivateUser                      @relation(fields: [privateUserId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  privateUserId        String
  FacebookUserOptIn    FacebookUserOptIn[]

  @@unique([privateUserId, communicationChannel])
}

model User {
  id                 String             @id @default(cuid())
  createdAt          DateTime           @default(now())
  updatedAt          DateTime           @updatedAt
  userType           UserType           @default(USER)
  verificationStatus VerificationStatus @default(PENDING)
  verifiedAt         DateTime?

  privateUser   PrivateUser @relation(fields: [privateUserId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  privateUserId String      @unique
  profile       Profile?

  profileLikeNotifications ProfileLikeNotification[]
  messageNotifications     MessageNotification[]
  profileViewNotifications ProfileViewNotification[]
}

model Password {
  hash String

  privateUser   PrivateUser @relation(fields: [privateUserId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  privateUserId String      @unique
}

model Session {
  id             String   @id @default(cuid())
  expirationDate DateTime

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  privateUser   PrivateUser @relation(fields: [privateUserId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  privateUserId String

  // non-unique foreign key
  @@index([privateUserId])
}

// TODO: add photo verificaiton status
model Profile {
  id             String       @id @default(cuid())
  username       String       @unique
  firstName      String
  gender         GenderEnum
  genderInterest GenderEnum[]
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  userId String @unique

  allVideos            Video[]
  location             Location?
  appearance           Appearance?
  lifestyle            Lifestyle?
  bio                  Bio?
  datingPreference     DatingPreference?
  locationPreference   LocationPreference?
  profileLikesOfOthers ProfileLike[]       @relation("LikerProfile")
  likedVideos          VideoLike[]         @relation("VideoLikerProfile")
  profileLikesOfMe     ProfileLike[]       @relation("LikedProfile")
  profileViewsOfOthers ProfileView[]       @relation("ViewerProfile")
  profileViewsOfMe     ProfileView[]       @relation("ViewedProfile")

  messages               Message[]
  // only query for conversations
  conversations          Conversation[] @relation("Conversations")
  // we shouldn't use these directly for queries
  conversationsInitiated Conversation[] @relation("InititatorProfile")
  conversationsReceived  Conversation[] @relation("ReceiverProfile")

  completedProfileSetup Boolean?

  photoVariants         PhotoVariant[]
  parentPhotos          ParentPhoto[]
  profileBlocksOfOthers ProfileBlock[] @relation("BlockerProfile")
  profileBlocksOfMe     ProfileBlock[] @relation("BlockedProfile")
}

model SocialConnection {
  id           String         @id @default(cuid())
  providerName SocialProvider
  providerId   String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  privateUser   PrivateUser? @relation(fields: [privateUserId], references: [id])
  privateUserId String?

  // we can use this to populate the proifles's first name
  // but don't use it for anything else
  firstName         String?
  OAuthToken        OAuthToken?
  facebookUserOptIn FacebookUserOptIn?

  @@unique([providerName, providerId])
}

// Oauth tokens that never expire don't have a refrsh token or expiry date
model OAuthToken {
  id           String    @id @default(cuid())
  accessToken  String
  refreshToken String?
  scope        String
  expiryDate   DateTime?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  socialConnection   SocialConnection @relation(fields: [socialConnectionId], references: [id], onDelete: Cascade)
  socialConnectionId String           @unique
}

model FacebookUserOptIn {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  notificationMessageToken     String
  // Topic registered. See: https://developers.facebook.com/docs/messenger-platform/discovery/m-me-links/#register-your-topic
  title                        String
  lastMessageSentAt            DateTime?
  status                       MessageNotificationSetting @relation(fields: [messageNotificationSettingId], references: [id], onDelete: Cascade)
  messageNotificationSettingId String                     @unique

  socialConnection   SocialConnection @relation(fields: [socialConnectionId], references: [id], onDelete: Cascade)
  socialConnectionId String           @unique
}

model ParentPhoto {
  id       String   @id @default(cuid())
  caption  String?
  provider Provider

  // unsure if these fields should be shared with RawPhoto
  fileId String @unique
  format String // file extension, e.g. "jpg", "png"
  width  Int // original width px
  height Int // original height px

  photoType          PhotoType
  createdAt          DateTime           @default(now())
  updatedAt          DateTime           @updatedAt
  verifiedAt         DateTime?
  verificationStatus VerificationStatus @default(PENDING)

  profile   Profile @relation(fields: [profileId], references: [id], onDelete: Cascade)
  profileId String

  photoVariants PhotoVariant[]
  rawPhoto      RawPhoto?
}

model RawPhoto {
  id         String    @id @default(cuid())
  fileId     String    @unique
  key        String // object key
  format     String // file extension, e.g. "jpg", "png"
  width      Int // original width px
  height     Int // original height px
  fileSize   Int // size in bytes
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  verifiedAt DateTime?

  parentPhoto   ParentPhoto @relation(fields: [parentPhotoId], references: [id], onDelete: Cascade)
  parentPhotoId String      @unique
}

model PhotoVariant {
  id       String      @id @default(cuid())
  variant  VariantType
  provider Provider
  key      String // object key
  format   String // "jpg" or "png"
  width    Int // variant width px
  height   Int // variant height px
  fileSize Int // size in bytes

  profile   Profile @relation(fields: [profileId], references: [id], onDelete: Cascade)
  profileId String // redundant FK for fast lookups

  parentPhoto   ParentPhoto @relation(fields: [parentPhotoId], references: [id], onDelete: Cascade)
  parentPhotoId String
}

model Video {
  id                 String             @id @default(cuid())
  fileId             String             @unique
  caption            String?
  videoType          VideoType
  createdAt          DateTime           @default(now())
  updatedAt          DateTime           @updatedAt
  profileId          String
  verificationStatus VerificationStatus @default(PENDING)

  profile       Profile     @relation(fields: [profileId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  likesByOthers VideoLike[] @relation("LikedVideo")

  @@index([profileId])
  @@index([fileId])
}

model Location {
  id               String   @id @default(cuid())
  latitude         Float
  longitude        Float
  cityOrTown       String
  regionCode       String
  countryIso2      String
  openToRelocation Boolean?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  profile   Profile @relation(fields: [profileId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  profileId String  @unique

  @@index([countryIso2])
  @@index([regionCode])
  @@index([cityOrTown])
}

model Appearance {
  id        String          @id @default(cuid())
  hairColor HairColorEnum?
  eyeColor  EyeColorEnum?
  // in cm and kg
  height    Decimal?
  weight    Decimal?
  bodyType  BodyTypeEnum?
  ethnicity EthnicityEnum[]
  createdAt DateTime        @default(now())
  updatedAt DateTime        @updatedAt

  profile   Profile @relation(fields: [profileId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  profileId String  @unique
}

model Lifestyle {
  id           String               @id @default(cuid())
  drinking     NeverSometimesOften?
  smoking      NeverSometimesOften?
  haveChildren YesNo?
  wantChildren YesNoNotSure?
  religion     ReligionEnum?
  interests    String[]
  createdAt    DateTime             @default(now())
  updatedAt    DateTime             @updatedAt

  profile   Profile @relation(fields: [profileId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  profileId String  @unique
}

model Bio {
  id         String   @id @default(cuid())
  profileId  String   @unique
  tagline    String?
  aboutMe    String?
  occupation String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  profile Profile @relation(fields: [profileId], references: [id], onDelete: Cascade, onUpdate: Cascade)
}

// note gender interest sits in the profile model
model DatingPreference {
  id String @id @default(cuid())

  lookingFor             String?
  relationshipPreference RelationshipEnum[]
  drinking               NeverSometimesOften[]
  smoking                NeverSometimesOften[]
  bodyTypes              BodyTypeEnum[]
  createdAt              DateTime              @default(now())
  updatedAt              DateTime              @updatedAt

  profile   Profile @relation(fields: [profileId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  profileId String  @unique

  minAge Int @default(18)
  maxAge Int @default(120)
}

model Conversation {
  id        String   @id @default(cuid())
  subject   String?
  createdAt DateTime @default(now())
  // needs to get updated each time a message is sent
  updatedAt DateTime @updatedAt

  participants Profile[] @relation("Conversations")

  // when a profile is deleted, we keep these values
  // then if we can't find the profiles, we can still show the conversation but show a deleted profile
  initiatorProfile   Profile? @relation("InititatorProfile", fields: [initiatorProfileId], references: [id])
  initiatorProfileId String

  receiverProfile   Profile? @relation("ReceiverProfile", fields: [receiverProfileId], references: [id])
  receiverProfileId String

  messages Message[]

  @@unique([initiatorProfileId, receiverProfileId])
}

model Message {
  id        String    @id @default(cuid())
  content   String
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  readAt    DateTime?

  // if the conversation is deleted, we delete the messages
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  conversationId String

  // purposely not deleting the message if the profile is deleted
  senderProfile       Profile?              @relation(fields: [senderProfileId], references: [id])
  senderProfileId     String // sender id
  MessageNotification MessageNotification[]
  MessageAttachment   MessageAttachment[]
}

model ProfileLike {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  likerProfile   Profile @relation("LikerProfile", fields: [likerProfileId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  likerProfileId String

  likedProfile            Profile                   @relation("LikedProfile", fields: [likedProfileId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  likedProfileId          String
  ProfileLikeNotification ProfileLikeNotification[]

  @@unique([likerProfileId, likedProfileId])
}

model VideoLike {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  videoLikerProfile   Profile @relation("VideoLikerProfile", fields: [videoLikerProfileId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  videoLikerProfileId String

  likedVideo   Video  @relation("LikedVideo", fields: [likedVideoId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  likedVideoId String

  @@unique([videoLikerProfileId, likedVideoId])
}

model ProfileView {
  id             String   @id @default(cuid())
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  lastViewedTime DateTime @default(now())

  viewerProfile   Profile @relation("ViewerProfile", fields: [viewerProfileId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  viewerProfileId String

  viewedProfile   Profile @relation("ViewedProfile", fields: [viewedProfileId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  viewedProfileId String

  ProfileViewNotification ProfileViewNotification[]

  @@unique([viewerProfileId, viewedProfileId])
}

model ProfileBlock {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  blockerProfile   Profile @relation("BlockerProfile", fields: [blockerProfileId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  blockerProfileId String

  blockedProfile   Profile @relation("BlockedProfile", fields: [blockedProfileId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  blockedProfileId String

  @@unique([blockerProfileId, blockedProfileId])
}

// Notifications
model ProfileLikeNotification {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  profileLike   ProfileLike @relation(fields: [profileLikeId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  profileLikeId String

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  userId String

  readAt DateTime?

  @@unique([profileLikeId, userId])
}

model MessageNotification {
  id        String    @id @default(cuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  readAt    DateTime?

  message   Message @relation(fields: [messageId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  messageId String

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  userId String

  @@unique([messageId, userId])
}

model ProfileViewNotification {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  profileView   ProfileView @relation(fields: [profileViewId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  profileViewId String

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  userId String

  readAt DateTime?

  @@unique([profileViewId, userId])
}

model MessageAttachment {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  message   Message  @relation(fields: [messageId], references: [id], onDelete: Cascade)
  messageId String
  url       String
  type      String
}

// Location Preferences
model LocationPreference {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  profile   Profile @relation(fields: [profileId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  profileId String  @unique

  // for broad searches
  specialLocationSearchPreferences SpecialLocationSearchPreference[]

  // individual location selections
  locationSelections LocationSelection[]
}

model LocationSelection {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  locationPreference   LocationPreference @relation(fields: [locationPreferenceId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  locationPreferenceId String

  locationType LocationType

  countryIso2 String
  regionCode  String?
  cityOrTown  String?

  @@unique([locationPreferenceId, countryIso2, regionCode, cityOrTown])
}

// enums
enum GenderEnum {
  MALE
  FEMALE
  TRANSGENDER_MALE
  TRANSGENDER_FEMALE
  NON_BINARY
}

enum PhotoType {
  MAIN
  VERIFICATION
  GALLERY
}

enum VideoType {
  MAIN
  GALLERY
  STORY
}

enum VerificationStatus {
  PENDING
  APPROVED
  REJECTED
}

enum HairColorEnum {
  BALD_SHAVEN
  BLACK
  BLONDE
  BROWN
  GREY_WHITE
  LIGHT_BROWN
  RED
  CHANGES_FREQUENTLY
}

enum EyeColorEnum {
  BLACK
  BLUE
  BROWN
  GREEN
  GREY
  HAZEL
}

enum BodyTypeEnum {
  PETITE
  SLIM
  ATHLETIC
  AVERAGE
  FEW_EXTRA_POUNDS
  FULL_FIGURED
  LARGE
}

enum EthnicityEnum {
  MIDDLE_EASTERN
  EAST_ASIAN
  SOUTH_ASIAN
  SOUTHEAST_ASIAN
  FILIPINO
  BLACK
  WHITE
  HISPANIC
  PACIFIC_ISLANDER
  OTHER
}

enum NeverSometimesOften {
  NEVER
  SOMETIMES
  OFTEN
}

enum YesNo {
  YES
  NO
}

enum YesNoNotSure {
  YES
  NO
  NOT_SURE
}

enum ReligionEnum {
  CHRISTIAN
  MUSLIM
  HINDU
  BUDDHIST
  JEWISH
  SPIRITUAL
  AGNOSTIC
  ATHEIST
  OTHER
}

enum RelationshipEnum {
  MARRIAGE
  SERIOUS_RELATIONSHIP
  OPEN_RELATIONSHIP
  FRIENDSHIP_OR_COMPANIONSHIP
  CASUAL
  UNSURE
}

enum SocialProvider {
  FACEBOOK
  GOOGLE
}

enum UserType {
  USER
  ADMIN
}

enum MessageNotificationSettingEnum {
  EVERY_MESSAGE
  NEW_CONVERSATION
  NEVER
}

enum ExternalCommunicationChannelEnum {
  EMAIL
  SMS
  FACEBOOK
}

enum Provider {
  CLOUDINARY
  CLOUDFLARE_R2
}

enum VariantType {
  SMALL
  MEDIUM
  LARGE
}

enum LocationType {
  COUNTRY
  REGION
  CITY_OR_TOWN
}

enum SpecialLocationSearchPreference {
  WORLDWIDE
  WESTERN_COUNTRIES
  ASIAN_COUNTRIES
}
