DATABASE_URL=postgres://postgres:postgres@localhost:5432/filipina-meet-db-test
SESSION_SECRET=session-secret
HONEYPOT_SECRET=super-duper-s3cret
RESEND_API_KEY=re_blAh_blaHBlaHblahBLAhBlAh
JWT_SECRET=some-secret-key # ok to leave for development

# this is set to a random value in the Dockerfile
INTERNAL_COMMAND_TOKEN=some-made-up-token

# set this to false to prevent search engines from indexing the website
# default to allow indexing for seo safety
ALLOW_INDEXING=true
HOST=true

AWS_ACCESS_KEY_ID=some-made-up-id
AWS_SECRET_ACCESS_KEY=some-made-up-secret
AWS_ENDPOINT_URL_S3=https://fly.storage.tigris.dev
AWS_REGION=auto
AWS_BUCKET_NAME=filipina-meet
BASE_URL=http://localhost:3000
GOOGLE_MAPS_API_KEY=some-made-up-key

FACEBOOK_CLIENT_ID=some-made-up-id
FACEBOOK_CLIENT_SECRET=some-made-up-secret

GOOGLE_CLIENT_ID=some-made-up-id
GOOGLE_CLIENT_SECRET=some-made-up-secret

CLOUDFLARE_ENDPOINT= # you replace
CLOUDFLARE_ACCESS_KEY_ID= # you replace
CLOUDFLARE_SECRET_ACCESS_KEY= # you replace
CLOUDFLARE_TOKEN= # you replace
CLOUDFLARE_BUCKET_NAME= # you replace
