import { useTranslation } from 'react-i18next';
import { FormFromConfig } from '#app/components/forms/form-from-config';
import Typography from '#app/components/typography';
import {
  handleFormActionForLoggedInUser,
  handleLoaderForLoggedInUser,
} from '#app/utils/remix-helpers.server';
import { t } from '#server/utils/api.server';
import { handleChangePassword } from '#server/utils/auth-helpers.server';
import { type FieldsFromConfig } from '#shared/types/forms';
import { changePasswordValidator } from '#shared/utils/validators';

export const loader = handleLoaderForLoggedInUser(async ({ privateUser }) => {
  return { privateUser };
});

export const action = handleFormActionForLoggedInUser(
  changePasswordValidator,
  async ({ json, privateUser, data }) => {
    await handleChangePassword(data, privateUser);

    return json({
      success: true,
      message: t('Password updated successfully'),
    });
  },
);

export default function ChangePassword() {
  const { t } = useTranslation();

  const fields: FieldsFromConfig<typeof changePasswordValidator> = [
    {
      name: 'currentPassword',
      label: t('Current password'),
      type: 'password',
      placeholder: t('Enter current password'),
      defaultValue: '',
    },
    {
      name: 'newPassword',
      label: t('New password'),
      type: 'password',
      placeholder: t('Enter new password'),
      defaultValue: '',
    },
    {
      name: 'confirmPassword',
      label: t('Re-type new password'),
      type: 'password',
      placeholder: t('Re-type new password'),
      defaultValue: '',
    },
  ];

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Typography variant="h3" className="text-2xl font-bold">
            Account settings
          </Typography>
        </div>
        <Typography className="text-xs font-semibold">
          Change password
        </Typography>
      </div>

      <div className="bg-card shadow-sm px-5 py-7 rounded-xl">
        <FormFromConfig
          method="POST"
          fields={fields}
          createValidator={changePasswordValidator}
        />
      </div>
    </div>
  );
}
