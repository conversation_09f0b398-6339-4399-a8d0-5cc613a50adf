import { Prisma, type Profile, type User, type Video } from '@prisma/client';
import {
  type NOTIFICATION_TYPE_MAP,
  type ONLINE_STATUS_MAP,
} from '#shared/constants/profile';
import { type ParentPhotoWithPhotoVariants } from './media';
export type NotificationType =
  (typeof NOTIFICATION_TYPE_MAP)[keyof typeof NOTIFICATION_TYPE_MAP];
export type OnlineStatusType =
  (typeof ONLINE_STATUS_MAP)[keyof typeof ONLINE_STATUS_MAP];

// Enhanced online status with timestamp information
export type OnlineStatusWithTime = {
  onlineStatus: OnlineStatusType;
  lastOnlineTime: Date | null;
};

export interface ProfileWithMedia extends Profile {
  allVideos: Video[];
  age: number | null;
  user: Pick<User, 'verificationStatus'>;
  allPhotos: ParentPhotoWithPhotoVariants[];
}
