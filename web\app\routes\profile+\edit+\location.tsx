import { useLoaderData } from '@remix-run/react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { FormFromConfig } from '#app/components/forms/form-from-config';
import { ProfileLayout } from '#app/components/profile/profile-layout';

import { handleApiCall, apiGet } from '#app/utils/fetch.client';
import {
  handleFormActionForProfile,
  handleLoaderForProfileRoute,
} from '#app/utils/remix-helpers.server';
import { prisma } from '#server/utils/db.server';
import { BadRequestError } from '#server/utils/errors.server';
import {
  getRegionsOfCountry,
  getCountries,
  sortCountriesByPriority,
  getGeocodeData,
} from '#server/utils/geography';
import { type FieldsFromConfig } from '#shared/types/forms';
import { type ApiRegion } from '#shared/types/geography';
import { type TranslationFunction } from '#shared/types/translations';

const createLocationValidator = (t: TranslationFunction) =>
  z.object({
    country: z.string().min(2, { message: t('Country is required') }),
    region: z.string().min(2, { message: t('Region is required') }),
    cityOrTown: z.string().min(2, { message: t('City/Town is required') }),
  });

export const loader = handleLoaderForProfileRoute(async ({ profile }) => {
  const countries = sortCountriesByPriority(getCountries());
  const location = await prisma.location.findUnique({
    where: { profileId: profile.id },
  });

  // if we have a location, pre-populate the regions
  let regions: ApiRegion[] = [];
  if (location) {
    regions = getRegionsOfCountry(location.countryIso2);
  }

  return {
    regions,
    countries,
    location,
  };
});

export const action = handleFormActionForProfile(
  createLocationValidator,
  async ({ profile, data, successWithMessage }) => {
    const { country, region, cityOrTown } = data;
    const address = `${cityOrTown}, ${region}, ${country}`;
    const { results } = await getGeocodeData(address);
    // assume the first result is the most relevant
    const result = results[0];
    if (!result) {
      throw new BadRequestError('Invalid address');
    }
    const countryComponent = result.address_components.find((component) =>
      component.types.includes('country'),
    );
    // TODO: check if the region is valid with the city/town
    if (!countryComponent || countryComponent.short_name !== country) {
      throw new BadRequestError('Invalid address');
    }
    // assume if we get this far the address is ok
    const locationFields = {
      latitude: result.geometry.location.lat,
      longitude: result.geometry.location.lng,
      cityOrTown,
      regionCode: region,
      countryIso2: country,
    };
    await prisma.location.upsert({
      where: { profileId: profile.id },
      update: locationFields,
      create: {
        profileId: profile.id,
        ...locationFields,
      },
    });
    return successWithMessage('Location updated successfully');
  },
);

export default function NewProfileRoute() {
  const { t } = useTranslation();
  const {
    countries,
    location,
    regions: initialRegions,
  } = useLoaderData<typeof loader>();
  const [regions, setRegions] = useState(initialRegions);
  let fields: FieldsFromConfig<typeof createLocationValidator> = [
    {
      name: 'country',
      label: t('Country'),
      type: 'select',
      options: countries.map((country) => ({
        label: country.name,
        value: country.iso2,
      })),
      defaultValue: location?.countryIso2,
      asyncOnChange: async (value: string) => {
        await handleApiCall(
          apiGet<{ regions: ApiRegion[] }>(
            `/api/geography/countries/${value}/regions`,
          ),
          {
            onApiSuccess: (data) => setRegions(data.regions),
          },
        );
      },
    },
    {
      name: 'region',
      label: t('Region'),
      type: 'select',
      options: regions.map((region) => ({
        label: region.name,
        value: region.regionCode,
      })),
      disabled: regions.length === 0,
      defaultValue: location?.regionCode,
    },
    {
      name: 'cityOrTown',
      label: t('City or town'),
      type: 'text',
      defaultValue: location?.cityOrTown,
    },
  ] as const;
  return (
    <ProfileLayout>
      <div className="form-container m-5">
        {t('Edit Location')}
        <FormFromConfig
          method="POST"
          action="/profile/edit/location"
          fields={fields}
          createValidator={createLocationValidator}
        />
      </div>
    </ProfileLayout>
  );
}
