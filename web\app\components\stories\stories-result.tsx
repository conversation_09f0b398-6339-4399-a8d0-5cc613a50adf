import { Button } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/20/solid';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import moment from 'moment';
import React, { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { IoHeartOutline, IoPaperPlaneOutline } from 'react-icons/io5';
import { Input } from '#app/components/forms/inputs';
import Typography from '#app/components/typography';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
} from '#app/components/ui/dialog';
import { type performStoriesSearch } from '#app/utils/stories.server';

import StoryAvatar from './story-avatar';

type StoriesResultProps = {
  stories: Awaited<ReturnType<typeof performStoriesSearch>>[number];
  selectedIdx: number | null;
  currentIdx: number;
  onSelectIdx: (idx: number | null) => void;
  storiesLength: number;
};

type StoryItemProps = {
  story: Awaited<ReturnType<typeof performStoriesSearch>>[number][number];
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  onFinish: () => void;
  storyItemsLength: number;
  activeStoryItemIdx: number;
  setActiveStoryItemIdx: React.Dispatch<React.SetStateAction<number>>;
};

const StoriesResult: React.FC<StoriesResultProps> = ({
  stories,
  selectedIdx,
  onSelectIdx,
  currentIdx,
  storiesLength,
}) => {
  const { t } = useTranslation();
  const open = selectedIdx === currentIdx;
  const [activeStoryItemIdx, setActiveStoryItemIdx] = useState(0);

  const storyItemsLength = stories.length ?? 0;

  const handleSelectIdx = useCallback(() => {
    onSelectIdx(open ? null : currentIdx);
  }, [currentIdx, onSelectIdx, open]);

  if (storyItemsLength < 1) return null;

  const story = stories[0];

  const activeStory = stories[activeStoryItemIdx];

  return (
    <Dialog open={open} onOpenChange={handleSelectIdx}>
      <DialogTrigger asChild>
        <div className="flex items-center flex-col">
          <StoryAvatar
            url={story?.profile.mainPhoto?.smallUrl}
            userName={story?.profile.username ?? ''}
          />
          <div>{story?.profile.username}</div>
        </div>
      </DialogTrigger>
      <DialogContent
        dialogOverlayClassName="bg-black tablet-min:backdrop-blur-md tablet-min:bg-black/80"
        closeClassName="top-9 text-white hidden"
        className="flex p-0 m-0 w-fit bg-transparent border-none shadow-none"
      >
        <DialogTitle className="sr-only">
          {t(story ? `${story.profile.username}'s Story` : 'Story')}
        </DialogTitle>
        <DialogDescription className="sr-only">
          {t(`Story ${activeStoryItemIdx + 1} of ${storyItemsLength}`)}
        </DialogDescription>
        {currentIdx - 1 >= 0 && (
          <Button
            type="button"
            onClick={() => {
              onSelectIdx(currentIdx - 1);
            }}
            className="hidden tablet-min:block"
          >
            <ChevronLeft className="size-16 text-white/50" />
          </Button>
        )}
        {activeStory && (
          <StoryItem
            story={activeStory}
            open={open}
            setOpen={() => handleSelectIdx()}
            onFinish={() => {
              if (activeStoryItemIdx + 1 < storyItemsLength) {
                setActiveStoryItemIdx((prev) => prev + 1);
              } else {
                const canGoNext = currentIdx + 1 <= storiesLength;
                onSelectIdx(canGoNext ? currentIdx + 1 : null);
              }
            }}
            storyItemsLength={storyItemsLength}
            activeStoryItemIdx={activeStoryItemIdx}
            setActiveStoryItemIdx={setActiveStoryItemIdx}
          />
        )}
        {currentIdx + 1 < storiesLength && (
          <Button
            type="button"
            onClick={() => {
              onSelectIdx(currentIdx + 1);
            }}
            className="hidden tablet-min:block"
          >
            <ChevronRight className="size-16 text-white/50" />
          </Button>
        )}
        <DialogTrigger className="hidden tablet-min:block top-0 right-[-10vw] absolute size-8 text-white z-[3]">
          <XMarkIcon />
        </DialogTrigger>
      </DialogContent>
    </Dialog>
  );
};

const StoryItem = ({
  story,
  open,
  onFinish,
  activeStoryItemIdx,
  storyItemsLength,
  setActiveStoryItemIdx,
}: StoryItemProps) => {
  const { t } = useTranslation();
  const [progress, setProgress] = React.useState(0);
  const videoRef = React.useRef<HTMLVideoElement | null>(null);
  const videoIntervalRef = React.useRef<NodeJS.Timeout>();
  const videoCallbackRef = useCallback(
    (element: HTMLVideoElement | null) => {
      videoRef.current = element;
      if (!element) {
        if (!videoIntervalRef.current) return;
        clearInterval(videoIntervalRef.current);
        return;
      }

      const interval = 50;
      videoIntervalRef.current = setInterval(() => {
        setProgress((element.currentTime / element.duration) * 100);
      }, interval);
      element.onended = () => {
        onFinish();
      };
    },
    [onFinish],
  );

  const storyUrl = story.url;

  const handleHoldStart = () => {
    if (videoRef.current) {
      videoRef.current.pause();
    }
  };

  const handleHoldEnd = async () => {
    if (videoRef.current) {
      await videoRef.current.play();
    }
  };

  const handleClickLeftBoundary = () => {
    if (videoRef.current) videoRef.current.load();
  };

  const handleClickRightBoundary = () => {
    onFinish();
  };

  return (
    <div className={`relative flex flex-col h-[80vh] w-[44vh]`}>
      <DialogTrigger className="tablet-min:hidden absolute size-8 right-5 top-8 text-white z-[3]">
        <XMarkIcon />
      </DialogTrigger>
      <div
        className={`relative h-full w-full overflow-hidden !rounded-[1.7rem]`}
        onMouseDown={handleHoldStart}
        onMouseUp={handleHoldEnd}
        onTouchStart={handleHoldStart}
        onTouchEnd={handleHoldEnd}
      >
        <div
          onClick={handleClickLeftBoundary}
          className="z-[2] absolute h-full w-10 bg-transparent top-0 left-0"
        />
        <div
          onClick={handleClickRightBoundary}
          className="z-[2] absolute h-full w-10 bg-transparent top-0 right-0"
        />
        <div className="flex items-center gap-2 w-full absolute top-3 px-5 z-[2]">
          {[...Array(storyItemsLength)].map((_, i) => (
            <StoryProgress
              key={i}
              progress={progress}
              isComplete={i < activeStoryItemIdx}
              shouldStart={i === activeStoryItemIdx}
              index={i}
              setActiveStoryItemIdx={setActiveStoryItemIdx}
            />
          ))}
        </div>
        <div className="absolute top-8 w-full left-4 flex flex-col gap-4">
          <div className="flex items-center gap-3">
            <div className="h-[35px] w-[35px] rounded-full overflow-hidden">
              {story.profile.mainPhoto?.smallUrl ? (
                <img
                  className="h-full w-full object-cover rounded-full"
                  src={story.profile.mainPhoto.smallUrl}
                  alt={t(`${story.profile.username}'s profile picture`)}
                />
              ) : (
                <div className="flex h-full w-full justify-center items-center bg-muted rounded-full text-sm">
                  {story.profile.username.slice(0, 2).toUpperCase()}
                </div>
              )}
            </div>
            <div className="text-white mix-blend-difference">
              <Typography
                variant="extra-small"
                className="font-semibold drop-shadow"
              >
                {story.profile.username}
              </Typography>
              <Typography
                variant="extra-small"
                className="mix-blend-difference"
              >
                {moment(story.createdAt).fromNow()}
              </Typography>
            </div>
          </div>
        </div>

        {open && (
          <video
            ref={videoCallbackRef}
            className="h-full w-full object-cover border-0"
            autoPlay
            muted
            playsInline
            src={storyUrl}
          />
        )}
      </div>
      <div className="tablet-min:absolute bottom-4 w-full tablet-min:px-4 pt-4">
        <div className="w-full flex items-center gap-3">
          <div className="relative grow">
            <Input
              placeholder={`Reply to ${story.profile.username}`}
              className="bg-transparent focus:bg-transparent"
            />
            <div className="absolute top-[12px] right-[12px]">
              <IoPaperPlaneOutline fontSize={26} className="text-gray-300" />
            </div>
          </div>
          <IoHeartOutline fontSize={32} className="text-gray-300" />
        </div>
      </div>
    </div>
  );
};

type StoryProgressProps = {
  progress: number;
  shouldStart: boolean;
  isComplete: boolean;
  index: number;
  setActiveStoryItemIdx: React.Dispatch<React.SetStateAction<number>>;
};

const StoryProgress = ({
  progress,
  shouldStart,
  isComplete,
  index,
  setActiveStoryItemIdx,
}: StoryProgressProps) => {
  const width = isComplete ? 100 : shouldStart ? progress : 0;

  const handleStoryProgressClick = () => {
    setActiveStoryItemIdx(index);
  };

  return (
    <div
      className="w-full relative overflow-hidden rounded-full"
      onClick={handleStoryProgressClick}
    >
      <div className="absolute w-full">
        <div
          className="h-full z-[98] bg-muted/50 transition-all rounded-full"
          style={{ width: `100%`, height: 4 }}
        />
      </div>
      <div className="w-full">
        <div
          className="h-full w-full z-[99] bg-white transition-all rounded-full"
          style={{ transform: `translateX(${width - 100}%)`, height: 4 }}
        />
      </div>
    </div>
  );
};

export { StoriesResult };
