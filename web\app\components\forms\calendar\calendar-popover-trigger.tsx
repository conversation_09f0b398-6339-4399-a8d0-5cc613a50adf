import { CalendarIcon } from 'lucide-react';

// COMPONENTS
import { Button } from '#app/components/ui/button';
import { PopoverTrigger } from '#app/components/ui/popover';

// STYLES
import { COLORS } from '#app/styles/calendar-picker-styles';

type CalendarPopoverTriggerProps = {
  open: boolean;
};

export function CalendarPopoverTrigger({ open }: CalendarPopoverTriggerProps) {
  return (
    <PopoverTrigger asChild>
      <Button
        variant="ghost"
        size="icon"
        style={{
          position: 'absolute',
          right: '8px',
          top: '50%',
          transform: 'translateY(-50%)',
          height: '36px',
          width: '36px',
          borderRadius: '50%',
          backgroundColor: 'transparent',
          color: open ? COLORS.primary : '#8E95A9',
        }}
        type="button"
        aria-label="Open calendar"
      >
        <CalendarIcon className="h-5 w-5" />
      </Button>
    </PopoverTrigger>
  );
}
