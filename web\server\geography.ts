import { type FastifyInstance } from 'fastify';
import { z } from 'zod';
import { t } from '#server/utils/api.server';
import { ZodValidationError, NotFoundError } from '#server/utils/errors.server';
import { handleFastifyRoute } from '#server/utils/fastify-helpers.server';
import { getRegionsOfCountry, isValidIso2 } from '#server/utils/geography';

const countryValidator = z.object({
  countryIso2: z.string().refine(isValidIso2, {
    message: t('Invalid country code'),
  }),
});

export async function geographyRouter(fastify: FastifyInstance) {
  fastify.get(
    '/countries/:countryIso2/regions',
    handleFastifyRoute(async ({ request, sendSuccessPayload }) => {
      const validationResult = countryValidator.safeParse(request.params);
      if (!validationResult.success) {
        throw new ZodValidationError(validationResult.error);
      }

      const { countryIso2 } = validationResult.data;
      const regions = getRegionsOfCountry(countryIso2);
      if (regions.length > 0) {
        return sendSuccessPayload({ regions });
      } else {
        throw new NotFoundError(t('No regions available'));
      }
    }),
  );
}
