import { Link } from '@remix-run/react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { LinkButton } from '#app/components/buttons/link-button';
import { FormFromConfig } from '#app/components/forms/form-from-config';
import Typography from '#app/components/typography';
import { Button } from '#app/components/ui/button';
import { Separator } from '#app/components/ui/separator';
import Background from '#app/images/gradient-mesh.webp';
import FacebookLogoSVG from '#app/images/svg-icons/facebook.svg?react';
import GoogleLogoSVG from '#app/images/svg-icons/google.svg?react';
import MainLogoSVG from '#app/images/svg-icons/logos/main.svg?react';
import WordLogoSVG from '#app/images/svg-icons/logos/word.svg?react';
import { checkHoneypot } from '#app/utils/honeypot.server';
import { handleNewSession } from '#app/utils/remix-auth.server';
import { handleAction } from '#app/utils/remix-helpers.server';
import { t } from '#server/utils/api.server';
import { createSessionFromPrivateUser } from '#server/utils/auth.server';
import { prisma } from '#server/utils/db.server';
import {
  BadRequestError,
  ZodValidationError,
} from '#server/utils/errors.server';
import { comparePassword } from '#server/utils/password.server.js';
import { getBestRouteToLandUser } from '#server/utils/user.server';
import {
  FACEBOOK_OAUTH_INITIATE_ROUTE,
  GOOGLE_OAUTH_INITIATE_ROUTE,
  LOGIN_ROUTE,
  REGISTER_ROUTE,
} from '#shared/constants/routes';
import { type TranslationFunction } from '#shared/types/translations';
import {
  createEmailValidator,
  createPasswordValidator,
} from '#shared/utils/validators';

function createValidator(t: TranslationFunction) {
  return z.object({
    email: createEmailValidator(t),
    password: createPasswordValidator(t),
  });
}

export const action = handleAction(async ({ request, redirect }) => {
  const formData = await request.formData();
  checkHoneypot(formData);
  const formDataObj = Object.fromEntries(formData.entries());

  const result = createValidator(t).safeParse(formDataObj);

  if (!result.success) {
    throw new ZodValidationError(result.error);
  }

  const { email, password } = result.data;

  const privateUser = await prisma.privateUser.findUnique({
    where: { uniqueId: `email:${email.toLowerCase()}` },
    include: { password: true, user: true },
  });
  if (!privateUser) {
    throw new BadRequestError('Invalid email or password');
  }
  if (!privateUser.password) {
    throw new BadRequestError('User signs in with Facebook');
  }
  if (!(await comparePassword(password, privateUser.password.hash))) {
    throw new BadRequestError('Invalid email or password');
  }

  const session = await createSessionFromPrivateUser({ privateUser });
  // if we have a next url, redirect to that, otherwise redirect to the profile new route
  const redirectTo =
    new URL(request.url).searchParams.get('next') ||
    (await getBestRouteToLandUser(privateUser.user!));

  return handleNewSession({
    request,
    session,
    redirectTo,
    redirect,
  });
});

export default function LoginRoute() {
  const { t } = useTranslation();
  const fields = [
    {
      name: 'email',
      type: 'email',
      label: t('Email'),
      placeholder: 'Your email address',
    },
    {
      name: 'password',
      type: 'password',
      label: t('Password'),
      placeholder: 'Your password',
    },
  ] as const;
  return (
    <div className="form-container">
      <div
        style={{ backgroundImage: `url(${Background})` }}
        className="rotate-180 bg-[position:60%_100%] p-4 h-[90px] items-center sm:grid md:grid lg:hidden grid"
      ></div>

      <div className="px-4 sm:h-full md:h-full lg:h-[100dvh] flex flex-col  mx-auto justify-center items-center w-full">
        <div className="w-full sm:pt-6 pt-0">
          <div className="items-center sm:hidden md:hidden lg:flex hidden">
            <MainLogoSVG className="tablet:h-9" />
            <WordLogoSVG className="tablet:h-5 w-auto" />
          </div>
        </div>

        <div className="grow grid grid-cols-1 lg:grid-cols-7 gap-2 w-full overflow-hidden py-8 lg:py-0">
          <div className="hidden lg:flex lg:items-center xl:items-end lg:justify-center lg:pl-14  lg:col-span-3 lg:pt-4 ">
            <div className="w-full max-w-[400px] xl:max-w-[500px] 2xl:max-w-[600px]">
              <div>
                <div className="flex justify-center items-center">
                  <div className="aspect-[3/4] w-full">
                    <img
                      className="w-full h-full rounded-[2rem] xl:rounded-b-none object-cover"
                      src="/img/fm-login-hero-main.jpeg"
                      alt="Login Image"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="w-full flex justify-center items-center lg:col-span-4">
            <div className="sm:w-full md:w-full w-[80%]">
              <div className="justify-items-center lg:justify-items-start mb-5">
                <Typography
                  variant="h1"
                  className="sm:text-[1.5rem] text-[1.2rem] font-bold !leading-none mb-2"
                >
                  {t('Back for more sparks?')}
                </Typography>
                <Typography
                  variant="h4"
                  className="sm:text-[1rem] text-[.9rem] font-extralight"
                >
                  {t('Log in and see who’s thinking about you')}
                </Typography>
              </div>

              <FormFromConfig
                method="POST"
                action={LOGIN_ROUTE}
                fields={fields}
                className="w-full"
                renderFields={({ fields }) => (
                  <>
                    <div className="space-y-6 w-full">
                      <div>{fields.email.node}</div>
                      <div>
                        <div>{fields.password.node}</div>
                        <Typography className="font-medium text-brand-primary text-xs mt-2">
                          Forgot your password?
                        </Typography>
                      </div>
                      <div className="w-full space-y-3">
                        <Button type="submit" className="w-full">
                          {t('Login')}
                        </Button>
                      </div>
                    </div>
                    <div className="mt-4 hidden sm:flex justify-center ">
                      <Typography className="text-center">
                        {t('Don’t have an account yet?')}{' '}
                        <Link
                          className="text-brand-primary font-semibold"
                          to={REGISTER_ROUTE}
                        >
                          {t('Sign up now')}
                        </Link>
                      </Typography>
                    </div>
                  </>
                )}
              />
              <>
                <div className="register-button-container mt-5 space-y-6 w-full">
                  <div className="flex w-full items-center gap-4">
                    <Separator className="grow w-auto" />
                    <Typography className="text-xs text-[#A2A2A2]">
                      {t('OR')}
                    </Typography>
                    <Separator className="grow w-auto" />
                  </div>

                  <div className="w-full flex flex-col md:flex-row gap-4">
                    <LinkButton
                      to={GOOGLE_OAUTH_INITIATE_ROUTE}
                      variant={'outline'}
                      className="grow"
                    >
                      <GoogleLogoSVG /> {t('Continue with Google')}
                    </LinkButton>
                    <LinkButton
                      to={FACEBOOK_OAUTH_INITIATE_ROUTE}
                      variant={'outline'}
                      className="grow"
                    >
                      <FacebookLogoSVG /> {t('Continue with Facebook')}
                    </LinkButton>
                  </div>

                  <Typography className="text-center sm:hidden">
                    {t('Don’t have an account yet?')}{' '}
                    <Link
                      className="text-brand-primary font-semibold"
                      to={REGISTER_ROUTE}
                    >
                      {t('Sign up now')}
                    </Link>
                  </Typography>
                </div>
              </>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
