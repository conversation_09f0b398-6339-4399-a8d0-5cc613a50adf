import React, { useMemo, useState } from 'react';
import { Label } from '#app/components/forms/helpers/label';
import { UnitToggle } from '#app/components/forms/inputs/unit-toggle';
import { UnitTypeEnum, WeightUnitsEnum } from '#shared/constants/enums';
import { GenericSelect } from './generic-select';

const getWeightOptions = (unit: WeightUnitsEnum) => {
  const kgOptions = Array.from({ length: 161 }, (_, i) => {
    const weight = 40 + i;
    return { value: weight.toString(), label: `${weight} kg` };
  });

  if (unit === WeightUnitsEnum.KG) {
    return kgOptions;
  } else {
    return kgOptions.map((option) => {
      const lbWeight = Math.round(Number(option.value) * 2.205);
      return {
        value: lbWeight.toString(),
        label: `${lbWeight} lb`,
      };
    });
  }
};

type WeightSelectProps = Omit<
  React.ComponentProps<typeof GenericSelect>,
  'options' | 'value' | 'onChange' | 'defaultValue' | 'formValue'
> & {
  value?: number;
  onChange?: (value: number) => void;
  labelProps?: React.ComponentProps<typeof Label>;
};

const WeightSelect = ({
  value: inputValue,
  onChange: inputOnChange,
  labelProps,
  ...props
}: WeightSelectProps) => {
  const [unit, setUnit] = useState<WeightUnitsEnum>(WeightUnitsEnum.KG);
  const weightOptions = useMemo(() => getWeightOptions(unit), [unit]);

  const onChange = (value: string) => {
    if (inputOnChange) {
      inputOnChange(Number(value));
    }
  };

  const value = inputValue ? inputValue.toString() : undefined;

  return (
    <>
      <div className="mb-2 flex items-center justify-between">
        <Label {...labelProps} />
        <UnitToggle
          type={UnitTypeEnum.WEIGHT}
          value={unit}
          onChange={(value) => setUnit(value as WeightUnitsEnum)}
          name={`${props.name}Unit`}
        />
      </div>
      <GenericSelect
        options={weightOptions}
        onChange={onChange}
        value={value}
        formValue={value}
        {...props}
      />
    </>
  );
};

export { WeightSelect };
