import {
  type PhotoVariant,
  type ParentPhoto,
  type Video,
  type RawPhoto,
  Prisma,
  type VariantType,
} from '@prisma/client';

export interface PhotoWithUrls extends ParentPhoto {
  smallUrl: string | undefined;
  mediumUrl: string | undefined;
  largeUrl: string | undefined;
}

export interface VideoWithUrl extends Video {
  url: string;
}

export interface ParentPhotoWithPhotoVariants extends ParentPhoto {
  photoVariants: PhotoVariant[];
}

export interface ParentPhotoWithVariantsandRaw
  extends ParentPhotoWithPhotoVariants {
  rawPhoto: RawPhoto | null;
}

export type CloudflareKeyCategories = VariantType | 'RAWPHOTO';
