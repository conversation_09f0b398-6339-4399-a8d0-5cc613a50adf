import { Link } from '@remix-run/react';
import React from 'react';
import { IoMdArrowBack } from 'react-icons/io';
import Typography from '#app/components/typography';
import { cn } from '#app/utils/misc';

interface ProfileHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  path: string;
  title: string;
}

const ProfileHeader = ({ title, path, ...props }: ProfileHeaderProps) => {
  return (
    <div className={cn('flex items-center justify-between mb-5 mx-4', props)}>
      <Link to={path}>
        <IoMdArrowBack fontSize={24} />
      </Link>
      <Typography variant="h3">{title}</Typography>
      <div className="w-[24px] h-[24px]" />
    </div>
  );
};

export default ProfileHeader;
