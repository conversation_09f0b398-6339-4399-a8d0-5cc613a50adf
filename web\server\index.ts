// Import and initialize Sen<PERSON> first before loading fastify
import '#server/utils/monitoring';
import crypto from 'node:crypto';
import process from 'node:process';
import fastifyCaching from '@fastify/caching';
import helmet from '@fastify/helmet';
import multipart from '@fastify/multipart';
import fastifyWebsocket from '@fastify/websocket';
import { remixFastify } from '@mcansh/remix-fastify';
import { installGlobals } from '@remix-run/node';

import { fastify } from 'fastify';
import fastifyPrintRoutes from 'fastify-print-routes';
import { apiRouter } from './apiRoutes';
import { authRouter } from './auth';
import { webhookRouter } from './webhookRoutes';
import { wsRouter } from './wsRoutes';

installGlobals();

const app = fastify({ pluginTimeout: 30000, bodyLimit: 52428800 }); // 50MB
if (process.env.PRINT_FASTIFY_ROUTES === 'true') {
  await app.register(fastifyPrintRoutes);
}

if (process.env.NODE_ENV === 'production') {
  const Sentry = await import('@sentry/node');

  Sentry.setupFastifyErrorHandler(app);
}

// Get the directory name of the current module
// const __filename = fileURLToPath(import.meta.url);
// const __dirname = dirname(__filename)

app.addHook('onRequest', (request, reply, done) => {
  const scriptNonce = crypto.randomBytes(16).toString('hex');
  // @ts-ignore
  reply.raw.scriptNonce = scriptNonce;
  done();
});

if (process.env.NODE_ENV !== 'development') {
  await app.register(fastifyCaching, {
    privacy: 'private',
    expiresIn: 3600, // Cache for 1 hour
  });
}

await app.register(helmet, {
  xPoweredBy: false,
  referrerPolicy: { policy: 'same-origin' },
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    // NOTE: Remove reportOnly when you're ready to enforce this CSP
    reportOnly: true,
    directives: {
      // @ts-expect-error
      'connect-src': [
        process.env.NODE_ENV === 'development' ? 'ws:' : null,
        process.env.SENTRY_DSN ? '*.sentry.io' : null,
        "'self'",
      ].filter(Boolean),
      'font-src': ["'self'", 'https://fonts.gstatic.com'],
      'frame-src': ["'self'"],
      'img-src': ["'self'", 'data:', '*.r2.dev'],
      'script-src': [
        "'self'",
        "'unsafe-inline'",
        // @ts-ignore
        (_, reply) => `'nonce-${reply.scriptNonce}'`,
      ],
      'script-src-attr': [
        // @ts-ignore
        (_, reply) => `'nonce-${reply.scriptNonce}'`,
      ],
      'upgrade-insecure-requests': null,
      // Allow style tags from https://fonts.googleapis.com
      styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
      'worker-src': ['blob:'],
    },
  },
});

await app.register(multipart, {
  attachFieldsToBody: true,
});
// TODO: get compress and static images working properly
// await app.register(compress, {
// 	global: true, // Enable compression globally
// })

// if (process.env.DISABLE_FASTIFY_CACHE !== 'true') {
// 	await app.register(fastifyStatic, {
// 		root: join(__dirname, 'public'), // Serve static files from the 'public' directory
// 		prefix: '/public/', // Access files via /public/<file>
// 	})
// }

// Register Websocket plugin
await app.register(fastifyWebsocket);

// Define a WebSocket route
await app.register(wsRouter);

// Ensure no fastifyStatic registration in the apiRouter
await app.register(apiRouter, { prefix: '/api' });
await app.register(webhookRouter, { prefix: '/webhooks' });
// auth not user protected
await app.register(authRouter, { prefix: '/api/auth' });
await app.register(remixFastify, {
  getLoadContext: (_, reply) => {
    return {
      // @ts-ignore
      cspNonce: `${reply.raw.scriptNonce}`,
    };
  },
});

const port = Number(process.env.PORT) || 3000;
// TODO: why do we need this?
const host = process.env.HOST === 'true' ? '0.0.0.0' : '127.0.0.1';

let address = await app.listen({ port, host });
console.log(`✅ app ready: ${address}`);
