import React, {
  createContext,
  useContext,
  useState,
  type ReactNode,
} from 'react';
import { type RootUser } from '#app/types/serialized-types';

interface UserContextType {
  user?: RootUser;
  setUser: React.Dispatch<React.SetStateAction<RootUser | undefined>>;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const UserProvider: React.FC<{
  children: ReactNode;
  initialUser?: RootUser;
}> = ({ children, initialUser }) => {
  const [user, setUser] = useState<RootUser | undefined>(initialUser);

  return (
    <UserContext.Provider value={{ user, setUser }}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
