import { type Message } from '@prisma/client';
import type React from 'react';
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import { v4 as uuidv4 } from 'uuid';
import { MessageQueue } from '#app/utils/message-queue';
import { type EnrichedConversation } from '#shared/types/conversations';

declare global {
  interface Window {
    sendWebSocketMessage?: (
      messageContent: string,
      id: string,
      attachments?: Array<{
        id: string;
        base64Data?: string;
        type?: string;
        name?: string;
      }>,
    ) => void;
  }
}

interface WebSocketContextType {
  socket: WebSocket | null;
  messageQueue: MessageQueue;
  enrichedConversationsState: EnrichedConversation[];
  setEnrichedConversationsState: React.Dispatch<
    React.SetStateAction<EnrichedConversation[]>
  >;
  allIncomingMessages: any[];
  messages: Message[] | any[];
  setMessages: React.Dispatch<React.SetStateAction<any[]>>;
}

const WebSocketContext = createContext<WebSocketContextType | undefined>(
  undefined,
);

export const WebSocketProvider: React.FC<{
  conversationId?: string;
  conversationToken?: string;
  children: React.ReactNode;
  userId: string;
  BASE_URL?: string;
  initialMessages?: string[] | Message[];
  initialEnrichedConversations?: string[] | EnrichedConversation[];
}> = ({
  conversationId,
  conversationToken,
  children,
  userId,
  BASE_URL,
  initialMessages,
  initialEnrichedConversations,
}) => {
  const socketRef = useRef<WebSocket | null>(null);
  const [socket, setSocket] = useState<WebSocket | null>(null);
  const messageQueueRef = useRef(new MessageQueue());
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
  const maxReconnectDelay = 5000;
  const [enrichedConversationsState, setEnrichedConversationsState] = useState<
    EnrichedConversation[]
  >(
    (initialEnrichedConversations || []).map((item) =>
      typeof item === 'string' ? JSON.parse(item) : item,
    ),
  );
  const [allIncomingMessages, setAllIncomingMessages] = useState<any[]>([]);
  const [messages, setMessages] = useState(initialMessages || []);

  useEffect(() => {
    const connectWebSocket = () => {
      if (!conversationId || !conversationToken) {
        console.warn(
          'Missing conversationId or token, WebSocket connection not established',
        );
        return;
      }

      const baseUrl = BASE_URL || 'http://localhost:3000';
      const wsUrl = baseUrl.replace(/^http(s)?:\/\//, 'ws$1://');
      const url = `${wsUrl}/chat/message?conversationId=${conversationId}&token=${conversationToken}`;

      try {
        const ws = new WebSocket(url);
        socketRef.current = ws;

        ws.onopen = () => {
          console.log('✅ WebSocket connected');
          setSocket(ws);
          messageQueueRef.current.setSocket(ws);
        };

        ws.onerror = (error) => {
          console.error('❌ WebSocket error:', error);
          messageQueueRef.current.setSocket(null);
        };

        ws.onclose = (event) => {
          console.log('⚠️ WebSocket closed:', event.code, event.reason);
          setSocket(null);
          messageQueueRef.current.setSocket(null);

          if (reconnectTimeoutRef.current) {
            clearTimeout(reconnectTimeoutRef.current);
          }
          reconnectTimeoutRef.current = setTimeout(
            connectWebSocket,
            maxReconnectDelay,
          );
        };

        return () => {
          if (reconnectTimeoutRef.current) {
            clearTimeout(reconnectTimeoutRef.current);
          }
          if (
            ws.readyState === WebSocket.OPEN ||
            ws.readyState === WebSocket.CONNECTING
          ) {
            ws.close(1000, 'Component unmounted');
          }
          socketRef.current = null;
          setSocket(null);
          messageQueueRef.current.clear();
        };
      } catch (error) {
        console.error('Failed to create WebSocket connection:', error);
        return () => {
          socketRef.current = null;
          setSocket(null);
          messageQueueRef.current.clear();
        };
      }
    };

    connectWebSocket();

    return () => {
      if (socketRef.current) {
        socketRef.current.close(1000, 'Component unmounted');
      }
    };
  }, [BASE_URL, conversationId, conversationToken]);

  const handleSetMessages = useCallback(
    (
      messagesOrUpdater:
        | Array<any>
        | ((prevMessages: Array<any>) => Array<any>),
    ) => {
      if (typeof messagesOrUpdater === 'function') {
        setAllIncomingMessages((prevMessages) => {
          const updatedMessages = messagesOrUpdater(prevMessages);
          return updatedMessages;
        });
      } else {
        const newMessages = Array.isArray(messagesOrUpdater)
          ? messagesOrUpdater
          : [];
        setAllIncomingMessages((prev) => [...prev, ...newMessages]);

        if (conversationId && newMessages.length > 0) {
          const hasNewMessagesForActiveConversation = newMessages.some(
            (msg) =>
              msg.conversationId === conversationId &&
              msg.senderProfileId !== userId,
          );

          if (hasNewMessagesForActiveConversation) {
            setEnrichedConversationsState((prevConversations) =>
              prevConversations.map((conversation) => {
                if (conversation.id === conversationId) {
                  const latestMessage = newMessages.find(
                    (msg) =>
                      msg.conversationId === conversationId &&
                      msg.senderProfileId !== userId,
                  );

                  if (latestMessage) {
                    return {
                      ...conversation,
                      lastMessage: {
                        ...latestMessage,
                        readAt: new Date(),
                      },
                    };
                  }
                }
                return conversation;
              }),
            );
          }
        }
      }

      if (!conversationId) return;

      if (typeof messagesOrUpdater === 'function') {
        setMessages((prevMessages) => {
          const updatedMessages = messagesOrUpdater(prevMessages);
          return updatedMessages.filter(
            (msg: { conversationId: string }) =>
              msg.conversationId === conversationId,
          );
        });
      } else {
        const filteredMessages = messagesOrUpdater.filter(
          (msg: { conversationId: string }) =>
            msg.conversationId === conversationId,
        );
        setMessages(filteredMessages);
      }
    },
    [conversationId, userId],
  );

  return (
    <WebSocketContext.Provider
      value={{
        socket,
        messageQueue: messageQueueRef.current,
        enrichedConversationsState,
        setEnrichedConversationsState,
        allIncomingMessages,
        messages,
        setMessages,
      }}
    >
      <WebSocketListener
        handleSetMessages={handleSetMessages}
        userId={userId}
        conversationId={conversationId || ''}
        initialMessages={initialMessages || []}
      />
      {children}
    </WebSocketContext.Provider>
  );
};

export const useWebSocket = () => {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};

function WebSocketListener({
  handleSetMessages,
  conversationId,
  userId,
  initialMessages,
}: {
  handleSetMessages: React.Dispatch<React.SetStateAction<any[]>>;
  conversationId: string;
  userId: string;
  initialMessages?: string[] | Message[];
}) {
  const { socket, messageQueue } = useWebSocket();
  const processedMessageIds = useRef(new Set<string>());

  useEffect(() => {
    if (!socket) {
      window.sendWebSocketMessage = undefined;
      return;
    }

    if (initialMessages && initialMessages?.length <= 1) return;

    window.sendWebSocketMessage = async (
      messageContent: string,
      messageId: string,
      attachments = [],
    ) => {
      const socketPayload = {
        userId: userId,
        conversationId: conversationId,
        message: messageContent,
        messageId: messageId,
        attachments: attachments,
      };

      messageQueue.enqueue(socketPayload);

      const messageAttachments = attachments.map((att) => {
        let url = '/placeholder.svg';

        if (att.base64Data && att.type) {
          try {
            const base64Content = att.base64Data.split(',')[1] || '';
            const byteString = atob(base64Content);
            const ab = new ArrayBuffer(byteString.length);
            const ia = new Uint8Array(ab);

            for (let i = 0; i < byteString.length; i++) {
              ia[i] = byteString.charCodeAt(i);
            }

            const blob = new Blob([ab], { type: att.type });
            url = URL.createObjectURL(blob);
          } catch (error) {
            console.error('Error creating blob URL for sender preview:', error);
          }
        }

        return {
          id: att.id,
          url: url,
          messageId: messageId,
        };
      });

      const uiMessage = {
        id: messageId,
        content: messageContent,
        senderProfileId: userId,
        conversationId: conversationId,
        createdAt: new Date().toISOString(),
        MessageAttachment: messageAttachments,
      };

      handleSetMessages((prevMessages) => [...prevMessages, uiMessage]);
    };

    const handleMessage = (event: MessageEvent) => {
      try {
        const data = JSON.parse(event.data);

        if (data) {
          const messageId = data.messageId || uuidv4();

          if (processedMessageIds.current.has(messageId)) {
            return;
          }

          processedMessageIds.current.add(messageId);

          let messageAttachments = [];
          if (data.attachments && Array.isArray(data.attachments)) {
            messageAttachments = data.attachments.map((att: any) => {
              let url = '/placeholder.svg';

              if (att.base64Data && att.type) {
                try {
                  const base64Content = att.base64Data.split(',')[1] || '';
                  const byteString = atob(base64Content);
                  const ab = new ArrayBuffer(byteString.length);
                  const ia = new Uint8Array(ab);

                  for (let i = 0; i < byteString.length; i++) {
                    ia[i] = byteString.charCodeAt(i);
                  }

                  const blob = new Blob([ab], { type: att.type });
                  url = URL.createObjectURL(blob);
                } catch (error) {
                  console.error('Error creating blob URL from base64:', error);
                }
              }

              return {
                id: att.id,
                url: url,
                messageId: messageId,
              };
            });
          }

          const uiMessage = {
            id: messageId,
            content: data.message,
            senderProfileId: data.userId,
            conversationId: data.conversationId,
            createdAt: new Date().toISOString(),
            MessageAttachment: messageAttachments,
          };

          handleSetMessages((prevMessages) => {
            const isDuplicate = prevMessages.some((m) => m.id === uiMessage.id);
            if (isDuplicate) {
              return prevMessages;
            }
            return [...prevMessages, uiMessage];
          });

          if (processedMessageIds.current.size > 1000) {
            const idsArray = Array.from(processedMessageIds.current);
            processedMessageIds.current = new Set(idsArray.slice(-1000));
          }
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    socket.addEventListener('message', handleMessage);

    return () => {
      socket.removeEventListener('message', handleMessage);
      window.sendWebSocketMessage = undefined;
    };
  }, [
    socket,
    messageQueue,
    handleSetMessages,
    conversationId,
    userId,
    initialMessages,
  ]);

  return null;
}
