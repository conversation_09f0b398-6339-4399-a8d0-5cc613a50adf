import { Link, useLocation } from '@remix-run/react';
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  ACTIVITY_LIKES_ME_ROUTE,
  ACTIVITY_LIKES_OTHERS_ROUTE,
  ACTIVITY_LIKES_MUTUAL_ROUTE,
} from '#shared/constants/routes';

interface ActivityLayoutProps {
  children: React.ReactNode;
  counts?: {
    myLikes: number;
    likesMe: number;
    mutualLikes: number;
  };
}

const ActivityLayout: React.FC<ActivityLayoutProps> = ({
  children,
  counts,
}) => {
  const location = useLocation();
  const { t } = useTranslation();
  const [currentPage, setCurrentPage] = useState(1);
  const [{ itemsPerPage, isSmallScreen }, setScreenConfig] = useState({
    itemsPerPage: 20,
    isSmallScreen: false,
  });

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setScreenConfig((prev) => ({
        ...prev,
        isSmallScreen: width < 768,
        itemsPerPage:
          width < 640 ? 6 : width < 1024 ? 12 : width < 1280 ? 16 : 24,
      }));
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const childArray = React.Children.toArray(children);
  const totalPages = Math.ceil(childArray.length / itemsPerPage);
  const currentItems = childArray.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage,
  );

  const getPaginationItems = (): (number | string)[] => {
    if (totalPages <= 5) return [...Array(totalPages)].map((_, i) => i + 1);

    let items: (number | string)[] = [1];

    if (currentPage > 3) items.push('...');

    const start = Math.max(2, currentPage - 1);
    const end = Math.min(totalPages - 1, currentPage + 1);

    for (let i = start; i <= end; i++) {
      items.push(i);
    }

    if (currentPage < totalPages - 2) items.push('...');
    items.push(totalPages);

    return items;
  };

  const content = (
    <div className="flex flex-col gap-4">
      <svg
        width="203"
        height="20"
        viewBox="0 0 203 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M10.0233 11.5342L6.69083 10.6817L12.5033 0.916666H16.5333L10.0233 11.5342ZM6.5875 19V10.32H10.1267V19H6.5875ZM6.69083 11.5342L0.180833 0.916666H4.21083L10.0233 10.6817L6.69083 11.5342ZM22.0494 19.31C20.7405 19.31 19.5694 19.0258 18.536 18.4575C17.5199 17.8719 16.7191 17.0711 16.1335 16.055C15.5652 15.0389 15.281 13.8678 15.281 12.5417C15.281 11.2156 15.5652 10.0444 16.1335 9.02833C16.7019 8.01222 17.4941 7.22 18.5102 6.65167C19.5263 6.06611 20.6888 5.77333 21.9977 5.77333C23.3238 5.77333 24.4949 6.06611 25.511 6.65167C26.5271 7.22 27.3194 8.01222 27.8877 9.02833C28.456 10.0444 28.7402 11.2156 28.7402 12.5417C28.7402 13.8678 28.456 15.0389 27.8877 16.055C27.3194 17.0711 26.5271 17.8719 25.511 18.4575C24.5121 19.0258 23.3582 19.31 22.0494 19.31ZM22.0494 16.1583C22.6866 16.1583 23.2463 16.0033 23.7285 15.6933C24.2107 15.3833 24.581 14.9614 24.8394 14.4275C25.1149 13.8764 25.2527 13.2478 25.2527 12.5417C25.2527 11.8356 25.1149 11.2156 24.8394 10.6817C24.5638 10.1306 24.1763 9.7 23.6769 9.39C23.1946 9.08 22.6349 8.925 21.9977 8.925C21.3777 8.925 20.818 9.08 20.3185 9.39C19.8363 9.7 19.4574 10.1306 19.1819 10.6817C18.9063 11.2156 18.7685 11.8356 18.7685 12.5417C18.7685 13.2478 18.9063 13.8764 19.1819 14.4275C19.4574 14.9614 19.8449 15.3833 20.3444 15.6933C20.8438 16.0033 21.4121 16.1583 22.0494 16.1583ZM39.5141 19L39.3074 16.6233V6.08333H42.7174V19H39.5141ZM30.7566 12.6967V6.08333H34.1666V12.6967H30.7566ZM34.1666 12.6967C34.1666 13.5578 34.2613 14.2381 34.4508 14.7375C34.6574 15.2197 34.9416 15.5728 35.3033 15.7967C35.6822 16.0033 36.1041 16.1067 36.5691 16.1067C37.4647 16.1239 38.1449 15.8569 38.6099 15.3058C39.0749 14.7375 39.3074 13.9194 39.3074 12.8517H40.4699C40.4699 14.2294 40.2633 15.4006 39.8499 16.365C39.4366 17.3122 38.8683 18.0442 38.1449 18.5608C37.4216 19.0603 36.5691 19.31 35.5874 19.31C34.5541 19.31 33.6758 19.1033 32.9524 18.69C32.2291 18.2767 31.678 17.6394 31.2991 16.7783C30.9374 15.9 30.7566 14.7719 30.7566 13.3942V12.6967H34.1666ZM48.0742 12.2058C48.0742 10.8108 48.3412 9.65694 48.8751 8.74417C49.4262 7.83139 50.1237 7.15111 50.9676 6.70333C51.8287 6.25556 52.7242 6.03167 53.6542 6.03167V9.33833C52.862 9.33833 52.1128 9.43306 51.4067 9.6225C50.7178 9.81194 50.1581 10.1219 49.7276 10.5525C49.297 10.9831 49.0817 11.5342 49.0817 12.2058H48.0742ZM45.6717 19V6.08333H49.0817V19H45.6717ZM61.8224 19V6.08333H65.2324V19H61.8224ZM63.5274 3.99083C63.0107 3.99083 62.5543 3.80139 62.1582 3.4225C61.7621 3.02639 61.5641 2.56139 61.5641 2.0275C61.5641 1.49361 61.7621 1.03722 62.1582 0.658333C62.5543 0.262222 63.0107 0.0641666 63.5274 0.0641666C64.0613 0.0641666 64.5177 0.262222 64.8966 0.658333C65.2927 1.03722 65.4907 1.49361 65.4907 2.0275C65.4907 2.56139 65.2927 3.02639 64.8966 3.4225C64.5177 3.80139 64.0613 3.99083 63.5274 3.99083ZM68.3768 19V6.08333H71.5801L71.7868 8.46V19H68.3768ZM76.9276 19V12.3867H80.3376V19H76.9276ZM76.9276 12.3867C76.9276 11.5083 76.8243 10.8281 76.6176 10.3458C76.4282 9.86361 76.1526 9.51917 75.791 9.3125C75.4293 9.08861 75.0074 8.97667 74.5251 8.97667C73.6468 8.95944 72.9665 9.22639 72.4843 9.7775C72.0193 10.3286 71.7868 11.1467 71.7868 12.2317H70.6501C70.6501 10.8539 70.8482 9.69139 71.2443 8.74417C71.6576 7.77972 72.226 7.04778 72.9493 6.54833C73.6899 6.03167 74.551 5.77333 75.5326 5.77333C76.5487 5.77333 77.4185 5.98 78.1418 6.39333C78.8651 6.80667 79.4162 7.4525 79.7951 8.33083C80.174 9.19194 80.3549 10.3114 80.3376 11.6892V12.3867H76.9276ZM88.3811 19.31C86.9516 19.31 85.8666 18.9569 85.1261 18.2508C84.4027 17.5447 84.0411 16.5372 84.0411 15.2283V2.05333H87.4511V14.4792C87.4511 15.0303 87.5716 15.4522 87.8127 15.745C88.0539 16.0206 88.4069 16.1583 88.8719 16.1583C89.0441 16.1583 89.2336 16.1239 89.4402 16.055C89.6469 15.9689 89.8622 15.8483 90.0861 15.6933L91.2744 18.2508C90.8611 18.5608 90.3961 18.8106 89.8794 19C89.38 19.2067 88.8805 19.31 88.3811 19.31ZM81.9486 8.97667V6.08333H90.6802V8.97667H81.9486ZM99.0646 19.31C97.7901 19.31 96.6621 19.0258 95.6804 18.4575C94.6987 17.8719 93.9237 17.0711 93.3554 16.055C92.8043 15.0389 92.5287 13.8678 92.5287 12.5417C92.5287 11.2156 92.8129 10.0444 93.3812 9.02833C93.9668 8.01222 94.759 7.22 95.7579 6.65167C96.774 6.06611 97.9365 5.77333 99.2454 5.77333C100.382 5.77333 101.424 6.07472 102.371 6.6775C103.336 7.26306 104.102 8.13278 104.67 9.28667C105.256 10.4233 105.549 11.8097 105.549 13.4458H95.7579L96.0679 13.1358C96.0679 13.7731 96.2229 14.3328 96.5329 14.815C96.8601 15.28 97.2735 15.6417 97.7729 15.9C98.2896 16.1411 98.8407 16.2617 99.4262 16.2617C100.132 16.2617 100.709 16.1153 101.157 15.8225C101.605 15.5125 101.949 15.1164 102.19 14.6342L105.239 15.8225C104.877 16.5458 104.403 17.1744 103.818 17.7083C103.25 18.225 102.569 18.6211 101.777 18.8967C100.985 19.1722 100.081 19.31 99.0646 19.31ZM96.2487 11.2242L95.9387 10.9142H102.165L101.88 11.2242C101.88 10.6042 101.734 10.1133 101.441 9.75167C101.148 9.37278 100.787 9.09722 100.356 8.925C99.9429 8.75278 99.5382 8.66667 99.1421 8.66667C98.746 8.66667 98.324 8.75278 97.8762 8.925C97.4285 9.09722 97.041 9.37278 96.7137 9.75167C96.4037 10.1133 96.2487 10.6042 96.2487 11.2242ZM109.933 12.2058C109.933 10.8108 110.2 9.65694 110.734 8.74417C111.285 7.83139 111.982 7.15111 112.826 6.70333C113.687 6.25556 114.583 6.03167 115.513 6.03167V9.33833C114.721 9.33833 113.972 9.43306 113.265 9.6225C112.577 9.81194 112.017 10.1219 111.586 10.5525C111.156 10.9831 110.94 11.5342 110.94 12.2058H109.933ZM107.53 19V6.08333H110.94V19H107.53ZM125.121 19L125.017 16.3908V12.4642C125.017 11.6892 124.931 11.0261 124.759 10.475C124.604 9.92389 124.346 9.50194 123.984 9.20917C123.639 8.89917 123.166 8.74417 122.563 8.74417C122.012 8.74417 121.512 8.86472 121.065 9.10583C120.617 9.34694 120.238 9.71722 119.928 10.2167L116.931 9.18333C117.172 8.63222 117.526 8.09833 117.991 7.58167C118.456 7.04778 119.058 6.61722 119.799 6.29C120.557 5.94556 121.478 5.77333 122.563 5.77333C123.872 5.77333 124.957 6.02306 125.818 6.5225C126.696 7.02194 127.342 7.72806 127.756 8.64083C128.186 9.53639 128.393 10.6042 128.376 11.8442L128.298 19H125.121ZM121.504 19.31C119.954 19.31 118.748 18.9656 117.887 18.2767C117.043 17.5878 116.621 16.6147 116.621 15.3575C116.621 13.9797 117.078 12.9464 117.991 12.2575C118.921 11.5514 120.221 11.1983 121.891 11.1983H125.172V13.73H123.08C121.96 13.73 121.185 13.8678 120.755 14.1433C120.324 14.4017 120.109 14.7719 120.109 15.2542C120.109 15.6331 120.281 15.9344 120.626 16.1583C120.97 16.365 121.452 16.4683 122.072 16.4683C122.641 16.4683 123.14 16.3392 123.571 16.0808C124.018 15.8053 124.371 15.4608 124.63 15.0475C124.888 14.6169 125.017 14.1778 125.017 13.73H125.844C125.844 15.4867 125.508 16.8558 124.836 17.8375C124.182 18.8192 123.071 19.31 121.504 19.31ZM137.319 19.31C135.976 19.31 134.779 19.0258 133.728 18.4575C132.678 17.8719 131.86 17.0711 131.274 16.055C130.689 15.0217 130.396 13.8506 130.396 12.5417C130.396 11.2156 130.689 10.0444 131.274 9.02833C131.86 8.01222 132.669 7.22 133.702 6.65167C134.736 6.06611 135.924 5.77333 137.267 5.77333C138.559 5.77333 139.739 6.09194 140.807 6.72917C141.874 7.36639 142.649 8.28778 143.132 9.49333L139.928 10.63C139.687 10.1306 139.308 9.73444 138.792 9.44167C138.292 9.13167 137.732 8.97667 137.112 8.97667C136.475 8.97667 135.915 9.13167 135.433 9.44167C134.951 9.73444 134.572 10.1478 134.297 10.6817C134.021 11.2156 133.883 11.8356 133.883 12.5417C133.883 13.2478 134.021 13.8678 134.297 14.4017C134.572 14.9183 134.96 15.3317 135.459 15.6417C135.959 15.9517 136.527 16.1067 137.164 16.1067C137.784 16.1067 138.344 15.9431 138.843 15.6158C139.36 15.2886 139.739 14.8581 139.98 14.3242L143.209 15.4608C142.71 16.6836 141.926 17.6308 140.858 18.3025C139.808 18.9742 138.628 19.31 137.319 19.31ZM150.517 19.31C149.088 19.31 148.003 18.9569 147.262 18.2508C146.539 17.5447 146.177 16.5372 146.177 15.2283V2.05333H149.587V14.4792C149.587 15.0303 149.708 15.4522 149.949 15.745C150.19 16.0206 150.543 16.1583 151.008 16.1583C151.18 16.1583 151.37 16.1239 151.576 16.055C151.783 15.9689 151.998 15.8483 152.222 15.6933L153.411 18.2508C152.997 18.5608 152.532 18.8106 152.016 19C151.516 19.2067 151.017 19.31 150.517 19.31ZM144.085 8.97667V6.08333H152.816V8.97667H144.085ZM155.796 19V6.08333H159.206V19H155.796ZM157.501 3.99083C156.985 3.99083 156.528 3.80139 156.132 3.4225C155.736 3.02639 155.538 2.56139 155.538 2.0275C155.538 1.49361 155.736 1.03722 156.132 0.658333C156.528 0.262222 156.985 0.0641666 157.501 0.0641666C158.035 0.0641666 158.491 0.262222 158.87 0.658333C159.266 1.03722 159.465 1.49361 159.465 2.0275C159.465 2.56139 159.266 3.02639 158.87 3.4225C158.491 3.80139 158.035 3.99083 157.501 3.99083ZM168.421 19.31C167.113 19.31 165.941 19.0258 164.908 18.4575C163.892 17.8719 163.091 17.0711 162.506 16.055C161.937 15.0389 161.653 13.8678 161.653 12.5417C161.653 11.2156 161.937 10.0444 162.506 9.02833C163.074 8.01222 163.866 7.22 164.882 6.65167C165.898 6.06611 167.061 5.77333 168.37 5.77333C169.696 5.77333 170.867 6.06611 171.883 6.65167C172.899 7.22 173.691 8.01222 174.26 9.02833C174.828 10.0444 175.112 11.2156 175.112 12.5417C175.112 13.8678 174.828 15.0389 174.26 16.055C173.691 17.0711 172.899 17.8719 171.883 18.4575C170.884 19.0258 169.73 19.31 168.421 19.31ZM168.421 16.1583C169.059 16.1583 169.618 16.0033 170.101 15.6933C170.583 15.3833 170.953 14.9614 171.211 14.4275C171.487 13.8764 171.625 13.2478 171.625 12.5417C171.625 11.8356 171.487 11.2156 171.211 10.6817C170.936 10.1306 170.548 9.7 170.049 9.39C169.567 9.08 169.007 8.925 168.37 8.925C167.75 8.925 167.19 9.08 166.691 9.39C166.208 9.7 165.829 10.1306 165.554 10.6817C165.278 11.2156 165.141 11.8356 165.141 12.5417C165.141 13.2478 165.278 13.8764 165.554 14.4275C165.829 14.9614 166.217 15.3833 166.716 15.6933C167.216 16.0033 167.784 16.1583 168.421 16.1583ZM177.361 19V6.08333H180.565L180.771 8.46V19H177.361ZM185.912 19V12.3867H189.322V19H185.912ZM185.912 12.3867C185.912 11.5083 185.809 10.8281 185.602 10.3458C185.413 9.86361 185.137 9.51917 184.775 9.3125C184.414 9.08861 183.992 8.97667 183.51 8.97667C182.631 8.95944 181.951 9.22639 181.469 9.7775C181.004 10.3286 180.771 11.1467 180.771 12.2317H179.635C179.635 10.8539 179.833 9.69139 180.229 8.74417C180.642 7.77972 181.21 7.04778 181.934 6.54833C182.674 6.03167 183.535 5.77333 184.517 5.77333C185.533 5.77333 186.403 5.98 187.126 6.39333C187.85 6.80667 188.401 7.4525 188.78 8.33083C189.158 9.19194 189.339 10.3114 189.322 11.6892V12.3867H185.912ZM196.875 19.31C196.048 19.31 195.273 19.1894 194.55 18.9483C193.826 18.69 193.189 18.3283 192.638 17.8633C192.087 17.3811 191.656 16.7956 191.346 16.1067L194.265 14.7633C194.524 15.1767 194.877 15.5383 195.325 15.8483C195.772 16.1411 196.289 16.2875 196.875 16.2875C197.443 16.2875 197.882 16.21 198.192 16.055C198.502 15.8828 198.657 15.6417 198.657 15.3317C198.657 15.0217 198.528 14.7978 198.27 14.66C198.028 14.505 197.693 14.3758 197.262 14.2725L196.074 13.9625C194.834 13.6353 193.852 13.1186 193.129 12.4125C192.423 11.6892 192.07 10.8625 192.07 9.9325C192.07 8.60639 192.492 7.58167 193.335 6.85833C194.197 6.135 195.411 5.77333 196.978 5.77333C197.787 5.77333 198.537 5.89389 199.225 6.135C199.932 6.37611 200.534 6.71194 201.034 7.1425C201.533 7.57306 201.878 8.0725 202.067 8.64083L199.251 9.9325C199.131 9.58806 198.838 9.3125 198.373 9.10583C197.908 8.88194 197.443 8.77 196.978 8.77C196.513 8.77 196.151 8.87333 195.893 9.08C195.652 9.26944 195.531 9.53639 195.531 9.88083C195.531 10.1047 195.652 10.2942 195.893 10.4492C196.134 10.5869 196.478 10.7075 196.926 10.8108L198.605 11.2242C199.449 11.4308 200.121 11.7667 200.62 12.2317C201.137 12.6794 201.507 13.1875 201.731 13.7558C201.972 14.3069 202.093 14.8494 202.093 15.3833C202.093 16.1583 201.86 16.8472 201.395 17.45C200.948 18.0356 200.328 18.4919 199.535 18.8192C198.76 19.1464 197.873 19.31 196.875 19.31Z"
          fill="url(#paint0_linear_2224_4270)"
        />
        <defs>
          <linearGradient
            id="paint0_linear_2224_4270"
            x1="0"
            y1="9.5"
            x2="206.667"
            y2="9.5"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#2C60AA" />
            <stop offset="1" stopColor="#D33544" />
          </linearGradient>
        </defs>
      </svg>
      <nav className="flex flex-row gap-2">
        {[
          {
            route: ACTIVITY_LIKES_OTHERS_ROUTE,
            title: t('My Likes'),
            count: counts?.myLikes,
          },
          {
            route: ACTIVITY_LIKES_ME_ROUTE,
            title: t('Likes Me'),
            count: counts?.likesMe,
          },
          {
            route: ACTIVITY_LIKES_MUTUAL_ROUTE,
            title: t('Mutual Likes'),
            count: counts?.mutualLikes,
          },
        ].map(({ route, title, count }) => (
          <Link
            to={route}
            key={route}
            className={`
              font-figtree rounded-xl flex items-center no-underline justify-center
              ${location.pathname === route ? 'bg-[#D33544] text-white' : 'bg-transparent text-black'}
              ${isSmallScreen ? 'h-6 text-xs px-4' : 'h-12 w-[147px]'}
            `}
          >
            <span>{title}</span>
            {count !== undefined && (
              <span
                className={`ml-1 ${location.pathname === route ? 'text-white' : 'text-gray-500'}`}
              >
                {count === 0 ? '0' : count > 50 ? '50+' : count}
              </span>
            )}
          </Link>
        ))}
      </nav>

      <div
        className={`grid justify-items-center ${isSmallScreen ? 'grid-cols-2 gap-4' : 'grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-16'} w-full`}
      >
        {currentItems}
      </div>

      {totalPages > 1 && (
        <div className="flex justify-center items-center gap-1 sm:gap-2 mt-4">
          {getPaginationItems().map((page, index) =>
            typeof page === 'string' ? (
              <button
                key={`ellipsis-${index}`}
                className="w-6 h-6 flex items-center justify-center text-gray-500"
                disabled
              >
                ...
              </button>
            ) : (
              <button
                key={page}
                onClick={() => setCurrentPage(page)}
                className={`
                  w-6 h-6 flex items-center justify-center rounded-full text-sm
                  ${page === currentPage ? 'bg-[#D33544] text-white border-2 border-[#D9646836]' : 'text-gray-500'}
                `}
              >
                {page}
              </button>
            ),
          )}
        </div>
      )}
    </div>
  );

  return (
    <div className="px-4 sm:px-6 lg:px-8 py-4">
      {isSmallScreen ? (
        content
      ) : (
        <div className="w-full bg-white rounded-lg shadow-md px-4 sm:px-6 lg:px-8 py-4">
          {content}
        </div>
      )}
    </div>
  );
};

export { ActivityLayout };
