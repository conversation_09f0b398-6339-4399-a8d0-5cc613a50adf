import { type Path, type useController } from 'react-hook-form';
import { type z } from 'zod';
import { type GenderEnum } from '#shared/constants/enums';
import { type CreateValidator } from '#shared/types/validators';

export interface SelectOption<O extends string | number = string> {
  readonly label: string;
  readonly value: O | 'Any';
}

export interface BaseField<T, O extends any> {
  readonly name: Path<T>;
  readonly label?: string;
  readonly placeholder?: string;
  readonly triggerFormSubmitOnChange?: boolean;
  readonly disabled?: boolean;
  readonly readOnly?: boolean;
  readonly asyncOnChange?: (value: O) => Promise<void>;
  readonly showAny?: boolean; // New property to show "Any" option
  // TODO: make onBlurAsync work
  // not sure if we can do better here on the type
  // readonly onBlurAsync?: (params: {
  // 	value: any // TODO: fix any
  // 	showSuccess: (message: string) => void
  // 	showError: (message: string) => void
  // }) => Promise<void>
}

/**
 * Abstract fields
 */
// Start of Selection
export interface AbstractStringFieldType<T> extends BaseField<T, string> {
  readonly defaultValue?: string;
}

export interface AbstractSelectFieldType<T, O extends string | number = string>
  extends BaseField<T, O> {
  readonly options: SelectOption<O>[];
}

/**
 * Concrete fields
 */
export interface TextFieldType<T> extends AbstractStringFieldType<T> {
  readonly type: 'text';
}

export interface TextAreaFieldType<T> extends AbstractStringFieldType<T> {
  readonly type: 'textarea';
}

export interface EmailAddressFieldType<T> extends AbstractStringFieldType<T> {
  readonly type: 'email';
}

export interface PasswordFieldType<T> extends AbstractStringFieldType<T> {
  readonly type: 'password';
}

export interface HiddenFieldType<T> extends AbstractStringFieldType<T> {
  readonly type: 'hidden';
  // this
  readonly defaultValue: string;
}

export interface SelectFieldType<T, O extends string | number = string>
  extends AbstractSelectFieldType<T, O> {
  readonly type: 'select';
  readonly defaultValue?: O;
  readonly showAny?: boolean;
}

export interface MultiSelectFieldType<T, O extends string | number = string>
  extends AbstractSelectFieldType<T, O> {
  readonly type: 'multi-select';
  readonly defaultValue?: O[];
  readonly showAny?: boolean;
}

export interface ComboboxFieldType<T, O extends string | number = string>
  extends AbstractSelectFieldType<T, O> {
  readonly type: 'combobox';
  readonly defaultValue?: O;
}

// note that we don't need to specify the options
export interface GenderMultiSelectFieldType<T>
  extends BaseField<T, GenderEnum[] | 'Any'> {
  readonly type: 'gender-multi-select';
  readonly defaultValue?: GenderEnum[] | 'Any';
  readonly showAny?: boolean;
}

export interface GenderSelectType<T> extends BaseField<T, GenderEnum | 'Any'> {
  readonly type: 'gender-select';
  readonly defaultValue?: GenderEnum | 'Any';
  readonly showAny?: boolean;
}

export interface AgeFieldType<T> extends BaseField<T, number> {
  readonly type: 'age';
  readonly defaultValue?: number;
}

export interface HeightFieldType<T> extends BaseField<T, number> {
  readonly type: 'height';
  readonly defaultValue?: number;
}

export interface WeightFieldType<T> extends BaseField<T, number> {
  readonly type: 'weight';
  readonly defaultValue?: number;
}

export interface AgeSliderType<T> extends BaseField<T, [number, number]> {
  readonly type: 'age-range-slider';
  readonly showHighUpperAge?: boolean;
  readonly defaultValue?: [number, number];
}

export interface PhotoUploadFieldType<T> extends BaseField<T, File> {
  readonly type: 'photo-upload';
  // we don't need this except as a key
  readonly defaultValue?: undefined;
  readonly imageSize?: 'medium' | 'large' | 'small';
  readonly hidePreview?: boolean;
  readonly defaultImageUrl?: string;
  readonly onDelete?: () => void;
  readonly showLoadingSpinner?: boolean;
  readonly disableDelete?: boolean;
}

export interface VideoUploadFieldType<T> extends BaseField<T, File> {
  readonly type: 'video-upload';
  // we don't need this except as a key
  readonly defaultValue?: undefined;
  readonly videoSize?: 'medium' | 'large' | 'very-large-vertical';
  readonly hidePreview?: boolean;
  readonly defaultVideoUrl?: string;
  readonly onDelete?: () => void;
  readonly showLoadingSpinner?: boolean;
}

export interface PhoneNumberType<T> extends BaseField<T, string> {
  readonly type: 'phone-number';
  readonly defaultValue?: string;
  readonly defaultCountry?: string;
}

export interface DatePickerFieldType<T> extends BaseField<T, string> {
  readonly type: 'date-picker';
  readonly defaultValue?: string;
  readonly calendarProps?: {
    disabled?: (date: Date) => boolean;
  };
}

export interface RadioSelectFieldType<T> extends AbstractSelectFieldType<T> {
  readonly type: 'radio-select';
  readonly defaultValue?: string;
  readonly showAny?: boolean;
  readonly containerClassName?: string;
}

export interface SegmentedToggleFieldType<T> extends BaseField<T, string> {
  readonly type: 'segmented-toggle';
  readonly defaultValue?: string;
  readonly options: SelectOption[];
}
export interface AttachmentUploadFieldType<T> extends BaseField<T, File[]> {
  readonly type: 'attachment-upload';
  readonly defaultValue?: File[];
  readonly multiple?: boolean;
  readonly accept?: string;
}

export type FieldFromConfigType<T> =
  | TextFieldType<T>
  | EmailAddressFieldType<T>
  | PasswordFieldType<T>
  | SelectFieldType<T>
  | MultiSelectFieldType<T>
  | AgeFieldType<T>
  | GenderSelectType<T>
  | GenderMultiSelectFieldType<T>
  | PhotoUploadFieldType<T>
  | VideoUploadFieldType<T>
  | HiddenFieldType<T>
  | TextAreaFieldType<T>
  | HeightFieldType<T>
  | WeightFieldType<T>
  | AgeSliderType<T>
  | PhoneNumberType<T>
  | DatePickerFieldType<T>
  | RadioSelectFieldType<T>
  | SegmentedToggleFieldType<T>
  | AttachmentUploadFieldType<T>
  | ChatInputFieldType<T>
  | GenderMultiCheckboxFieldType<T>
  | MultiCheckboxFieldType<T>
  | ComboboxFieldType<T>;
export type ListOfErrors = Array<
  ReturnType<typeof useController>['fieldState']['error']
>;

export type FieldsFromConfig<TCreateValidator extends CreateValidator> =
  FieldFromConfigType<z.infer<ReturnType<TCreateValidator>>>[];

export interface ChatInputFieldType<T> extends BaseField<T, string> {
  readonly type: 'chat-input';
  readonly defaultValue?: string;
}

export interface GenderMultiCheckboxFieldType<T>
  extends BaseField<T, GenderEnum[] | 'Any'> {
  readonly type: 'gender-multi-checkbox';
  readonly defaultValue?: GenderEnum[] | 'Any';
  readonly showAny?: boolean;
}

export interface MultiCheckboxFieldType<T, O extends string | number = string>
  extends AbstractSelectFieldType<T, O> {
  readonly type: 'multi-checkbox';
  readonly defaultValue?: O[];
  readonly showAny?: boolean;
}
