// This page is pathless. This page was made before the setup page was redesigned.
// Leaving this here in-case we want to re-add them.

import { useNavigate } from "@remix-run/react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { FaArrowLeftLong } from "react-icons/fa6";
import { z } from "zod";
import { Button } from "#app/components/ui/button.js";
import { LayoutType } from "#app/types/layouts.js";
import { cn } from "#app/utils/misc.js";
import { handleFormActionForProfile, handleLoaderForLoggedInUser } from "#app/utils/remix-helpers.server.js";
import { PROFILE_SETUP_COMPLETE_ROUTE } from "#shared/constants/routes.js";
import { InterestWithIcons } from "#shared/types/interests.js";

const nextRoute = PROFILE_SETUP_COMPLETE_ROUTE;

export const handle = {layout: LayoutType.NONE}

export const loader = handleLoaderForLoggedInUser(
  async () => {
    return {
    };
  },
);
 
export const action = handleFormActionForProfile(
  () => z.object({}),
  async ({ redirect }) => {
    return redirect(PROFILE_SETUP_COMPLETE_ROUTE);
  },
);

export default function SetupLifestyleRoute() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [selected, setSelected] = useState<Set<string>>(new Set());

  function handleClickBack() {
    if (window.history.length > 1) {
      window.history.back();
    } 
  }

  function handleClickSkip() {
    console.log('skipping');
    navigate(nextRoute);
  } 

  return (
    <>
      <div className="mb-6 md:mb-8">
        <h1
          className={cn(
            'text-3xl md:text-4xl font-extrabold text-gray-900',
            'dark:text-white md:tracking-tight',
          )}
        >
          {t('Show off your favorites')}
        </h1>
        <p className="mt-1 text-sm text-gray-500">
          {t('Music, food, weekend plans — common ground makes the best connections')}
        </p>
      </div>
      <form action="/profile/setup/lifestyle" className="flex flex-wrap gap-5">
        {
          InterestWithIcons.map((interest) => (
            <>
              <input 
                type="radio" 
                className="hidden" 
                onClick={() => {
                  console.log('state', selected);
                  setSelected( (state) => {
                    if (state.has(interest.value)) {
                      state.delete(interest.value);
                    }
                    else {
                      state.add(interest.value);
                    }
                    return new Set(state);
                  })
                }}
                checked={selected.has(interest.value)}
                id={`interest-id-${interest.value}`}
              />
              <label 
                htmlFor={`interest-id-${interest.value}`}
                key={interest.value}
                className={
                    cn(
                    'flex items-center p-3 cursor-pointer hover:bg-[#fcd6df] border-b border-gray-100',
                    'bg-red-50',
                    'rounded-lg bg-white',
                    'shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none',
                    selected.has(interest.value) && 'bg-red-500'
                  )
                } 
              >
                <span>{interest.icon}</span>
                <span>{interest.label}</span>
              </label>
            </>
          ))
        }
        <button type="submit">submit</button>
      </form>
      <form method="POST" action="/profile/setup/lifestyle">
        <div className="flex justify-between mt-auto pt-8">
          <Button 
            onClick={handleClickBack}
            className="text-black"
            variant='link'
            type="button"
          >
            <FaArrowLeftLong className="mr-2"/>
            Back
          </Button>
          <button
            type="submit"
            className={cn(
              'w-[20%] rounded-lg bg-[#D33544] py-3 text-center font-medium text-white',
              'transition-colors hover:bg-[#B82C3A] focus:outline-none focus:ring-2',
              'focus:ring-[#D33544] focus:ring-offset-2',
            )}
          >
            Next
          </button>
        </div>
        <div className="flex justify-end">
          <Button 
            onClick={handleClickSkip}
            variant="link"
            className="w-[20%]"
            type="button"
          >
            Skip for now
          </Button>
        </div>
      </form>
    </>
  );
}