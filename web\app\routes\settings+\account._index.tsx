import { useLoaderData } from '@remix-run/react';
import { useState } from 'react';
import { Label } from '#app/components/forms/helpers/label';
import { Input } from '#app/components/forms/inputs/input';
import Typography from '#app/components/typography';
import { Button } from '#app/components/ui/button';
import { handleLoaderForLoggedInUser } from '#app/utils/remix-helpers.server';

export const loader = handleLoaderForLoggedInUser(async ({ privateUser }) => {
  return { privateUser };
});

export default function AccountInformation() {
  const { privateUser } = useLoaderData<typeof loader>();
  const [mobileNumber, setMobileNumber] = useState(
    privateUser.phoneNumber ?? '',
  );

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Typography variant="h3" className="text-2xl font-bold">
            Account settings
          </Typography>
          <Button className="bg-red-500 hover:bg-red-600 text-white px-8 py-2 min-w-[140px]">
            Save changes
          </Button>
        </div>
        <Typography className="text-xs font-semibold">
          Personal information
        </Typography>
      </div>

      <div className="space-y-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              value={privateUser.email ?? ''}
              readOnly
              disabled
              className="mt-1 bg-muted"
            />
          </div>

          <div>
            <Label htmlFor="mobile">Mobile number</Label>
            <Input
              id="mobile"
              value={mobileNumber}
              onChange={(e) => setMobileNumber(e.target.value)}
              className="mt-1"
              placeholder="Enter mobile number"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
