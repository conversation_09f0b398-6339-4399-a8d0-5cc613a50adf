import { useLoaderData } from '@remix-run/react';
import { useTranslation } from 'react-i18next';
import { LinkButton } from '#app/components/buttons/link-button';
import { handleLoaderForLoggedInUser } from '#app/utils/remix-helpers.server';
import { BadRequestError } from '#server/utils/errors.server';
import { verifyJwt } from '#server/utils/jwt.server';
import {
  changeUserVerificationStatus,
  getBestRouteToLandUser,
} from '#server/utils/user.server';
import { VerificationStatus } from '#shared/constants/enums';

export const loader = handleLoaderForLoggedInUser(
  async ({ params: { token }, privateUser, user }) => {
    // TODO: we should put these errors in the status and show them to the user
    // or redirect them to some error page
    if (!token) {
      throw new BadRequestError('No token provided');
    }
    const decoded = verifyJwt(token);
    if (
      typeof decoded !== 'object' ||
      !decoded ||
      !('privateUserId' in decoded)
    ) {
      throw new BadRequestError('Invalid token: No user id in decoded token');
    }
    if (decoded.privateUserId !== privateUser.id) {
      throw new BadRequestError(
        'User id in token does not match logged in user',
      );
    }

    // Not sure what to do if the verification status is rejected
    if (user.verificationStatus === VerificationStatus.PENDING) {
      await changeUserVerificationStatus(user);
    }
    const status =
      user.verificationStatus === VerificationStatus.APPROVED
        ? 'ALREADY_VERIFIED'
        : 'VERIFIED';

    return {
      status: status,
      nextUrl: await getBestRouteToLandUser(user),
    };
  },
);

// Simple webpage component for user to click and continue
export default function VerifyPage() {
  const { nextUrl, status } = useLoaderData<typeof loader>();
  const { t } = useTranslation();
  const message =
    status === 'VERIFIED'
      ? t('Email Verified Successfully!')
      : t('Email Already Verified!');

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-100">
      <h1 className="mb-6 text-2xl font-bold">{message}</h1>
      <p className="mb-4">Click the button below to continue.</p>
      <LinkButton to={nextUrl}>Continue</LinkButton>
    </div>
  );
}
