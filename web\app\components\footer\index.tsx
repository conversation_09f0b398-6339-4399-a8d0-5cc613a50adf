import { useTranslation } from 'react-i18next';
import Typography from '#app/components/typography';
import FilipinaMeetIconWhite from '#app/images/svg-icons/logos/white.svg?react';

const links = [
  { label: 'About Us', link: '' },
  { label: 'Contact Us', link: '' },
  { label: 'Terms of Use', link: '' },
  { label: 'Privacy Statement', link: '' },
  { label: 'Dating Safety', link: '' },
  { label: 'Community Guidelines', link: '' },
];

const Footer = () => {
  const { t } = useTranslation();
  return (
    <footer className="tablet-min:flex hidden flex-col gap-5 justify-center items-center w-full bg-gradient-to-r from-brand-secondary to-brand-primary px-4 py-8">
      <div className="flex flex-col items-center justify-center">
        <FilipinaMeetIconWhite width={50} height={50} />
        <Typography
          variant="h3"
          className="text-white font-libre-bodoni font-light italic"
        >
          {t('filipinameet')}
        </Typography>
      </div>
      <div className="text-center space-y-2.5">
        <ul className="flex justify-center items-center gap-4 md:gap-8 text-white flex-wrap">
          {links.map((link, i) => (
            <li key={i}>
              <Typography className={'font-normal'} variant="small">
                {t(link.label)}
              </Typography>
            </li>
          ))}
        </ul>
        <Typography className="text-white font-light" variant="extra-small">
          {t('Copyright ©2024 FilipinaMeet. All Rights Reserved.')}
        </Typography>
      </div>
    </footer>
  );
};

export default Footer;
