import { ChevronLeft, ChevronRight } from 'lucide-react';

// COMPONENTS
import { CalendarGrid } from '#app/components/forms/calendar/calendar-grid';
import { CalendarMonthSelector } from '#app/components/forms/calendar/calendar-month-selector';
import { CalendarYearSelector } from '#app/components/forms/calendar/calendar-year-selector';
import { Button } from '#app/components/ui/button';
import { PopoverContent } from '#app/components/ui/popover';

// STYLES
import { COLORS, STYLES } from '#app/styles/calendar-picker-styles';

// UTILS
import { isNextMonthDisabled } from '#app/utils/calendar-helpers';

type CalendarPopoverContentProps = {
  viewDate: Date;
  selectedDate?: Date;
  hoveredDate: Date | null;
  yearSelectOpen: boolean;
  monthSelectOpen: boolean;
  animating: boolean;
  slideDirection: string;
  setHoveredDate: (date: Date | null) => void;
  setViewDate: (date: Date | ((prevDate: Date) => Date)) => void;
  setYearSelectOpen: (open: boolean) => void;
  setMonthSelectOpen: (open: boolean) => void;
  onDateSelect: (date: Date) => void;
  animate: (direction: string) => void;
};

export function CalendarPopoverContent({
  viewDate,
  selectedDate,
  hoveredDate,
  yearSelectOpen,
  monthSelectOpen,
  animating,
  slideDirection,
  setHoveredDate,
  setViewDate,
  setYearSelectOpen,
  setMonthSelectOpen,
  onDateSelect,
  animate,
}: CalendarPopoverContentProps) {
  const getAnimationStyle = () => {
    if (!animating) return { opacity: 1, transform: 'translateX(0)' };
    return {
      opacity: 0,
      transform:
        slideDirection === 'left' ? 'translateX(-20px)' : 'translateX(20px)',
      transition: 'all 0.2s ease-in-out',
    };
  };

  const previousMonth = () => {
    animate('right');
    setTimeout(() => {
      setViewDate((prevDate) => {
        const newDate = new Date(prevDate);
        newDate.setMonth(newDate.getMonth() - 1);
        return newDate;
      });
    }, 200);
  };

  const nextMonth = () => {
    if (isNextMonthDisabled(viewDate)) return;

    animate('left');
    setTimeout(() => {
      setViewDate((prevDate) => {
        const newDate = new Date(prevDate);
        newDate.setMonth(newDate.getMonth() + 1);
        return newDate;
      });
    }, 200);
  };

  const renderDateView = () => {
    if (yearSelectOpen) {
      return (
        <>
          <CalendarYearSelector
            viewDate={viewDate}
            setViewDate={setViewDate}
            setYearSelectOpen={setYearSelectOpen}
          />
          <div style={{ height: '280px' }} />
        </>
      );
    }

    if (monthSelectOpen) {
      return (
        <>
          <CalendarMonthSelector
            viewDate={viewDate}
            setViewDate={setViewDate}
            setMonthSelectOpen={setMonthSelectOpen}
          />
          <div style={{ height: '280px' }} />
        </>
      );
    }

    const WEEKDAYS = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

    return (
      <>
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(7, 1fr)',
            marginBottom: '10px',
            textAlign: 'center',
          }}
        >
          {WEEKDAYS.map((day) => (
            <div
              key={day}
              style={{
                fontSize: '12px',
                fontWeight: '500',
                color: '#8E95A9',
                padding: '4px 0',
              }}
            >
              {day.charAt(0)}
            </div>
          ))}
        </div>

        <div
          style={{
            ...getAnimationStyle(),
            display: 'grid',
            gridTemplateColumns: 'repeat(7, 1fr)',
            gap: '6px',
            justifyItems: 'center',
          }}
        >
          <CalendarGrid
            viewDate={viewDate}
            selectedDate={selectedDate}
            hoveredDate={hoveredDate}
            setHoveredDate={setHoveredDate}
            onDateSelect={onDateSelect}
          />
        </div>
      </>
    );
  };

  return (
    <PopoverContent
      className="p-0 shadow-xl border-0 overflow-hidden w-[332px]"
      align="start"
      style={{
        backgroundColor: '#FFFFFF',
        borderRadius: '20px',
        boxShadow: '0 12px 28px rgba(0,0,0,0.15)',
      }}
    >
      <div
        style={{
          height: '6px',
          background: `linear-gradient(90deg, ${COLORS.secondary} 0%, ${COLORS.primary} 100%)`,
        }}
      />

      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '18px 20px 14px',
          borderBottom: `1px solid #EAEDF5`,
        }}
      >
        <Button
          variant="ghost"
          style={STYLES.navButton}
          onClick={previousMonth}
          aria-label="Previous month"
          onMouseOver={(e) =>
            Object.assign(e.currentTarget.style, STYLES.navButtonHover)
          }
          onMouseOut={(e) =>
            Object.assign(e.currentTarget.style, STYLES.navButton)
          }
        >
          <ChevronLeft className="h-5 w-5" />
        </Button>

        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '8px',
          }}
        >
          <button
            onClick={() => {
              setMonthSelectOpen(true);
              setYearSelectOpen(false);
            }}
            style={{
              ...STYLES.selectionButton,
              ...(monthSelectOpen ? STYLES.activeElement : {}),
            }}
            onMouseOver={(e) => {
              if (!monthSelectOpen) {
                e.currentTarget.style.backgroundColor = COLORS.primaryLight;
                e.currentTarget.style.color = COLORS.primary;
                e.currentTarget.style.boxShadow =
                  '0 4px 10px rgba(211, 53, 68, 0.15)';
              }
            }}
            onMouseOut={(e) => {
              if (!monthSelectOpen) {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = '#1E1E1E';
                e.currentTarget.style.boxShadow = 'none';
                e.currentTarget.style.border = 'none';
              }
            }}
          >
            {new Intl.DateTimeFormat('en-US', { month: 'long' }).format(
              viewDate,
            )}
          </button>

          <button
            onClick={() => {
              setYearSelectOpen(true);
              setMonthSelectOpen(false);
            }}
            style={{
              ...STYLES.selectionButton,
              ...(yearSelectOpen ? STYLES.activeElement : {}),
            }}
            onMouseOver={(e) => {
              if (!yearSelectOpen) {
                e.currentTarget.style.backgroundColor = COLORS.primaryLight;
                e.currentTarget.style.color = COLORS.primary;
                e.currentTarget.style.boxShadow =
                  '0 4px 10px rgba(211, 53, 68, 0.15)';
              }
            }}
            onMouseOut={(e) => {
              if (!yearSelectOpen) {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = '#1E1E1E';
                e.currentTarget.style.boxShadow = 'none';
                e.currentTarget.style.border = 'none';
              }
            }}
          >
            {viewDate.getFullYear()}
          </button>
        </div>

        <Button
          variant="ghost"
          style={{
            ...STYLES.navButton,
            color: isNextMonthDisabled(viewDate) ? '#C7CCD7' : '#1E1E1E',
            opacity: isNextMonthDisabled(viewDate) ? 0.5 : 1,
            cursor: isNextMonthDisabled(viewDate) ? 'default' : 'pointer',
          }}
          onClick={nextMonth}
          disabled={isNextMonthDisabled(viewDate)}
          aria-label="Next month"
          onMouseOver={(e) => {
            if (!isNextMonthDisabled(viewDate)) {
              Object.assign(e.currentTarget.style, STYLES.navButtonHover);
            }
          }}
          onMouseOut={(e) => {
            if (!isNextMonthDisabled(viewDate)) {
              Object.assign(e.currentTarget.style, {
                ...STYLES.navButton,
                color: isNextMonthDisabled(viewDate) ? '#C7CCD7' : '#1E1E1E',
                opacity: isNextMonthDisabled(viewDate) ? 0.5 : 1,
              });
            }
          }}
        >
          <ChevronRight className="h-5 w-5" />
        </Button>
      </div>

      <div style={{ padding: '16px 18px 20px', position: 'relative' }}>
        {renderDateView()}
      </div>
    </PopoverContent>
  );
}
