/// <reference types="vitest" />

import react from '@vitejs/plugin-react';
import dotenv from 'dotenv';
import { defineConfig } from 'vite';

dotenv.config({ path: '.env.test' });

export default defineConfig({
  plugins: [react()],
  css: { postcss: { plugins: [] } },
  test: {
    include: ['./app/**/*.test.{ts,tsx}'],
    setupFiles: ['./tests/setup/setup-test-env'],
    globalSetup: ['./tests/setup/global-setup'],
    restoreMocks: true,
    coverage: {
      include: ['app/**/*.{ts,tsx}'],
      all: true,
    },
  },
});
